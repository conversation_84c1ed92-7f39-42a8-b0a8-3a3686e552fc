define("local_offermanager/app/app-lazy",["core/config","tool_lfxp/ajax","core/notification"],function(q_,H_,W_){"use strict";function j_(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const s in e)if(s!=="default"){const i=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(t,s,i.get?i:{enumerable:!0,get:()=>e[s]})}}return t.default=e,Object.freeze(t)}const G_=j_(q_);/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ur(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const lt={}.NODE_ENV!=="production"?Object.freeze({}):{},Mn={}.NODE_ENV!=="production"?Object.freeze([]):[],Nt=()=>{},z_=()=>!1,go=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),yi=e=>e.startsWith("onUpdate:"),vt=Object.assign,gl=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},K_=Object.prototype.hasOwnProperty,tt=(e,t)=>K_.call(e,t),Ee=Array.isArray,rn=e=>_o(e)==="[object Map]",kn=e=>_o(e)==="[object Set]",fd=e=>_o(e)==="[object Date]",Ae=e=>typeof e=="function",gt=e=>typeof e=="string",Ts=e=>typeof e=="symbol",rt=e=>e!==null&&typeof e=="object",_l=e=>(rt(e)||Ae(e))&&Ae(e.then)&&Ae(e.catch),hd=Object.prototype.toString,_o=e=>hd.call(e),vl=e=>_o(e).slice(8,-1),pd=e=>_o(e)==="[object Object]",yl=e=>gt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,vo=ur(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),X_=ur("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),bi=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Y_=/-(\w)/g,Kt=bi(e=>e.replace(Y_,(t,s)=>s?s.toUpperCase():"")),J_=/\B([A-Z])/g,kr=bi(e=>e.replace(J_,"-$1").toLowerCase()),nn=bi(e=>e.charAt(0).toUpperCase()+e.slice(1)),on=bi(e=>e?`on${nn(e)}`:""),Vr=(e,t)=>!Object.is(e,t),Vn=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},yo=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},Ei=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Q_=e=>{const t=gt(e)?Number(e):NaN;return isNaN(t)?e:t};let md;const bo=()=>md||(md=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function As(e){if(Ee(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=gt(i)?sv(i):As(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(gt(e)||rt(e))return e}const Z_=/;(?![^(]*\))/g,ev=/:([^]+)/,tv=/\/\*[^]*?\*\//g;function sv(e){const t={};return e.replace(tv,"").split(Z_).forEach(s=>{if(s){const i=s.split(ev);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function me(e){let t="";if(gt(e))t=e;else if(Ee(e))for(let s=0;s<e.length;s++){const i=me(e[s]);i&&(t+=i+" ")}else if(rt(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const rv="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",nv="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",ov="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",iv=ur(rv),av=ur(nv),lv=ur(ov),uv=ur("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function gd(e){return!!e||e===""}function cv(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=Eo(e[i],t[i]);return s}function Eo(e,t){if(e===t)return!0;let s=fd(e),i=fd(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Ts(e),i=Ts(t),s||i)return e===t;if(s=Ee(e),i=Ee(t),s||i)return s&&i?cv(e,t):!1;if(s=rt(e),i=rt(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),f=t.hasOwnProperty(u);if(c&&!f||!c&&f||!Eo(e[u],t[u]))return!1}}return String(e)===String(t)}function bl(e,t){return e.findIndex(s=>Eo(s,t))}const _d=e=>!!(e&&e.__v_isRef===!0),ne=e=>gt(e)?e:e==null?"":Ee(e)||rt(e)&&(e.toString===hd||!Ae(e.toString))?_d(e)?ne(e.value):JSON.stringify(e,vd,2):String(e),vd=(e,t)=>_d(t)?vd(e,t.value):rn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[El(i,a)+" =>"]=n,s),{})}:kn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>El(s))}:Ts(t)?El(t):rt(t)&&!Ee(t)&&!pd(t)?String(t):t,El=(e,t="")=>{var s;return Ts(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Gs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let is;class yd{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=is,!t&&is&&(this.index=(is.scopes||(is.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=is;try{return is=this,t()}finally{is=s}}else({}).NODE_ENV!=="production"&&Gs("cannot run an inactive effect scope.")}on(){++this._on===1&&(this.prevScope=is,is=this)}off(){this._on>0&&--this._on===0&&(is=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function dv(e){return new yd(e)}function fv(){return is}let ut;const Cl=new WeakSet;class bd{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,is&&is.active&&is.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Cl.has(this)&&(Cl.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Cd(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Id(this),wd(this);const t=ut,s=Rs;ut=this,Rs=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&ut!==this&&Gs("Active effect was not restored correctly - this is likely a Vue internal bug."),Od(this),ut=t,Rs=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Sl(t);this.deps=this.depsTail=void 0,Id(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Cl.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){xl(this)&&this.run()}get dirty(){return xl(this)}}let Ed=0,Co,wo;function Cd(e,t=!1){if(e.flags|=8,t){e.next=wo,wo=e;return}e.next=Co,Co=e}function wl(){Ed++}function Ol(){if(--Ed>0)return;if(wo){let t=wo;for(wo=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;Co;){let t=Co;for(Co=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function wd(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Od(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),Sl(i),hv(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function xl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(xd(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function xd(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Oo)||(e.globalVersion=Oo,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!xl(e))))return;e.flags|=2;const t=e.dep,s=ut,i=Rs;ut=e,Rs=!0;try{wd(e);const n=e.fn(e._value);(t.version===0||Vr(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{ut=s,Rs=i,Od(e),e.flags&=-3}}function Sl(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)Sl(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function hv(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Rs=!0;const Sd=[];function Ps(){Sd.push(Rs),Rs=!1}function Ms(){const e=Sd.pop();Rs=e===void 0?!0:e}function Id(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=ut;ut=void 0;try{t()}finally{ut=s}}}let Oo=0;class pv{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Il{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!ut||!Rs||ut===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==ut)s=this.activeLink=new pv(ut,this),ut.deps?(s.prevDep=ut.depsTail,ut.depsTail.nextDep=s,ut.depsTail=s):ut.deps=ut.depsTail=s,Dd(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=ut.depsTail,s.nextDep=void 0,ut.depsTail.nextDep=s,ut.depsTail=s,ut.deps===s&&(ut.deps=i)}return{}.NODE_ENV!=="production"&&ut.onTrack&&ut.onTrack(vt({effect:ut},t)),s}trigger(t){this.version++,Oo++,this.notify(t)}notify(t){wl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(vt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Ol()}}}function Dd(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Dd(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const Dl=new WeakMap,an=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),Nl=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),xo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Tt(e,t,s){if(Rs&&ut){let i=Dl.get(e);i||Dl.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new Il),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function zs(e,t,s,i,n,a){const u=Dl.get(e);if(!u){Oo++;return}const c=f=>{f&&({}.NODE_ENV!=="production"?f.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):f.trigger())};if(wl(),t==="clear")u.forEach(c);else{const f=Ee(e),g=f&&yl(s);if(f&&s==="length"){const h=Number(i);u.forEach((p,v)=>{(v==="length"||v===xo||!Ts(v)&&v>=h)&&c(p)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),g&&c(u.get(xo)),t){case"add":f?g&&c(u.get("length")):(c(u.get(an)),rn(e)&&c(u.get(Nl)));break;case"delete":f||(c(u.get(an)),rn(e)&&c(u.get(Nl)));break;case"set":rn(e)&&c(u.get(an));break}}Ol()}function Ln(e){const t=Ve(e);return t===e?t:(Tt(t,"iterate",xo),Xt(e)?t:t.map(Bt))}function Ci(e){return Tt(e=Ve(e),"iterate",xo),e}const mv={__proto__:null,[Symbol.iterator](){return Tl(this,Symbol.iterator,Bt)},concat(...e){return Ln(this).concat(...e.map(t=>Ee(t)?Ln(t):t))},entries(){return Tl(this,"entries",e=>(e[1]=Bt(e[1]),e))},every(e,t){return cr(this,"every",e,t,void 0,arguments)},filter(e,t){return cr(this,"filter",e,t,s=>s.map(Bt),arguments)},find(e,t){return cr(this,"find",e,t,Bt,arguments)},findIndex(e,t){return cr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return cr(this,"findLast",e,t,Bt,arguments)},findLastIndex(e,t){return cr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return cr(this,"forEach",e,t,void 0,arguments)},includes(...e){return Al(this,"includes",e)},indexOf(...e){return Al(this,"indexOf",e)},join(e){return Ln(this).join(e)},lastIndexOf(...e){return Al(this,"lastIndexOf",e)},map(e,t){return cr(this,"map",e,t,void 0,arguments)},pop(){return So(this,"pop")},push(...e){return So(this,"push",e)},reduce(e,...t){return Nd(this,"reduce",e,t)},reduceRight(e,...t){return Nd(this,"reduceRight",e,t)},shift(){return So(this,"shift")},some(e,t){return cr(this,"some",e,t,void 0,arguments)},splice(...e){return So(this,"splice",e)},toReversed(){return Ln(this).toReversed()},toSorted(e){return Ln(this).toSorted(e)},toSpliced(...e){return Ln(this).toSpliced(...e)},unshift(...e){return So(this,"unshift",e)},values(){return Tl(this,"values",Bt)}};function Tl(e,t,s){const i=Ci(e),n=i[t]();return i!==e&&!Xt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const gv=Array.prototype;function cr(e,t,s,i,n,a){const u=Ci(e),c=u!==e&&!Xt(e),f=u[t];if(f!==gv[t]){const p=f.apply(e,a);return c?Bt(p):p}let g=s;u!==e&&(c?g=function(p,v){return s.call(this,Bt(p),v,e)}:s.length>2&&(g=function(p,v){return s.call(this,p,v,e)}));const h=f.call(u,g,i);return c&&n?n(h):h}function Nd(e,t,s,i){const n=Ci(e);let a=s;return n!==e&&(Xt(e)?s.length>3&&(a=function(u,c,f){return s.call(this,u,c,f,e)}):a=function(u,c,f){return s.call(this,u,Bt(c),f,e)}),n[t](a,...i)}function Al(e,t,s){const i=Ve(e);Tt(i,"iterate",xo);const n=i[t](...s);return(n===-1||n===!1)&&Di(s[0])?(s[0]=Ve(s[0]),i[t](...s)):n}function So(e,t,s=[]){Ps(),wl();const i=Ve(e)[t].apply(e,s);return Ol(),Ms(),i}const _v=ur("__proto__,__v_isRef,__isVue"),Td=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ts));function vv(e){Ts(e)||(e=String(e));const t=Ve(this);return Tt(t,"has",e),t.hasOwnProperty(e)}class Ad{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?$d:Ld:a?Vd:kd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=Ee(t);if(!n){let f;if(u&&(f=mv[s]))return f;if(s==="hasOwnProperty")return vv}const c=Reflect.get(t,s,It(t)?t:i);return(Ts(s)?Td.has(s):_v(s))||(n||Tt(t,"get",s),a)?c:It(c)?u&&yl(s)?c:c.value:rt(c)?n?Ud(c):Si(c):c}}class Rd extends Ad{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const f=Xs(a);if(!Xt(i)&&!Xs(i)&&(a=Ve(a),i=Ve(i)),!Ee(t)&&It(a)&&!It(i))return f?!1:(a.value=i,!0)}const u=Ee(t)&&yl(s)?Number(s)<t.length:tt(t,s),c=Reflect.set(t,s,i,It(t)?t:n);return t===Ve(n)&&(u?Vr(i,a)&&zs(t,"set",s,i,a):zs(t,"add",s,i)),c}deleteProperty(t,s){const i=tt(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&zs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!Ts(s)||!Td.has(s))&&Tt(t,"has",s),i}ownKeys(t){return Tt(t,"iterate",Ee(t)?"length":an),Reflect.ownKeys(t)}}class Pd extends Ad{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Gs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Gs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const yv=new Rd,bv=new Pd,Ev=new Rd(!0),Cv=new Pd(!0),Rl=e=>e,wi=e=>Reflect.getPrototypeOf(e);function wv(e,t,s){return function(...i){const n=this.__v_raw,a=Ve(n),u=rn(a),c=e==="entries"||e===Symbol.iterator&&u,f=e==="keys"&&u,g=n[e](...i),h=s?Rl:t?Ni:Bt;return!t&&Tt(a,"iterate",f?Nl:an),{next(){const{value:p,done:v}=g.next();return v?{value:p,done:v}:{value:c?[h(p[0]),h(p[1])]:h(p),done:v}},[Symbol.iterator](){return this}}}}function Oi(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Gs(`${nn(e)} operation ${s}failed: target is readonly.`,Ve(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function Ov(e,t){const s={get(n){const a=this.__v_raw,u=Ve(a),c=Ve(n);e||(Vr(n,c)&&Tt(u,"get",n),Tt(u,"get",c));const{has:f}=wi(u),g=t?Rl:e?Ni:Bt;if(f.call(u,n))return g(a.get(n));if(f.call(u,c))return g(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&Tt(Ve(n),"iterate",an),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Ve(a),c=Ve(n);return e||(Vr(n,c)&&Tt(u,"has",n),Tt(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,f=Ve(c),g=t?Rl:e?Ni:Bt;return!e&&Tt(f,"iterate",an),c.forEach((h,p)=>n.call(a,g(h),g(p),u))}};return vt(s,e?{add:Oi("add"),set:Oi("set"),delete:Oi("delete"),clear:Oi("clear")}:{add(n){!t&&!Xt(n)&&!Xs(n)&&(n=Ve(n));const a=Ve(this);return wi(a).has.call(a,n)||(a.add(n),zs(a,"add",n,n)),this},set(n,a){!t&&!Xt(a)&&!Xs(a)&&(a=Ve(a));const u=Ve(this),{has:c,get:f}=wi(u);let g=c.call(u,n);g?{}.NODE_ENV!=="production"&&Md(u,c,n):(n=Ve(n),g=c.call(u,n));const h=f.call(u,n);return u.set(n,a),g?Vr(a,h)&&zs(u,"set",n,a,h):zs(u,"add",n,a),this},delete(n){const a=Ve(this),{has:u,get:c}=wi(a);let f=u.call(a,n);f?{}.NODE_ENV!=="production"&&Md(a,u,n):(n=Ve(n),f=u.call(a,n));const g=c?c.call(a,n):void 0,h=a.delete(n);return f&&zs(a,"delete",n,void 0,g),h},clear(){const n=Ve(this),a=n.size!==0,u={}.NODE_ENV!=="production"?rn(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&zs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=wv(n,e,t)}),s}function xi(e,t){const s=Ov(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(tt(s,n)&&n in i?s:i,n,a)}const xv={get:xi(!1,!1)},Sv={get:xi(!1,!0)},Iv={get:xi(!0,!1)},Dv={get:xi(!0,!0)};function Md(e,t,s){const i=Ve(s);if(i!==s&&t.call(e,i)){const n=vl(e);Gs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const kd=new WeakMap,Vd=new WeakMap,Ld=new WeakMap,$d=new WeakMap;function Nv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Tv(e){return e.__v_skip||!Object.isExtensible(e)?0:Nv(vl(e))}function Si(e){return Xs(e)?e:Ii(e,!1,yv,xv,kd)}function Fd(e){return Ii(e,!1,Ev,Sv,Vd)}function Ud(e){return Ii(e,!0,bv,Iv,Ld)}function Ks(e){return Ii(e,!0,Cv,Dv,$d)}function Ii(e,t,s,i,n){if(!rt(e))return{}.NODE_ENV!=="production"&&Gs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=Tv(e);if(a===0)return e;const u=n.get(e);if(u)return u;const c=new Proxy(e,a===2?i:s);return n.set(e,c),c}function ln(e){return Xs(e)?ln(e.__v_raw):!!(e&&e.__v_isReactive)}function Xs(e){return!!(e&&e.__v_isReadonly)}function Xt(e){return!!(e&&e.__v_isShallow)}function Di(e){return e?!!e.__v_raw:!1}function Ve(e){const t=e&&e.__v_raw;return t?Ve(t):e}function Pl(e){return!tt(e,"__v_skip")&&Object.isExtensible(e)&&yo(e,"__v_skip",!0),e}const Bt=e=>rt(e)?Si(e):e,Ni=e=>rt(e)?Ud(e):e;function It(e){return e?e.__v_isRef===!0:!1}function Bd(e){return qd(e,!1)}function Av(e){return qd(e,!0)}function qd(e,t){return It(e)?e:new Rv(e,t)}class Rv{constructor(t,s){this.dep=new Il,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ve(t),this._value=s?t:Bt(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Xt(t)||Xs(t);t=i?t:Ve(t),Vr(t,s)&&(this._rawValue=t,this._value=i?t:Bt(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Lr(e){return It(e)?e.value:e}const Pv={get:(e,t,s)=>t==="__v_raw"?e:Lr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return It(n)&&!It(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function Hd(e){return ln(e)?e:new Proxy(e,Pv)}class Mv{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Il(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Oo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&ut!==this)return Cd(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return xd(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Gs("Write operation failed: computed value is readonly")}}function kv(e,t,s=!1){let i,n;Ae(e)?i=e:(i=e.get,n=e.set);const a=new Mv(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const Ti={},Ai=new WeakMap;let un;function Vv(e,t=!1,s=un){if(s){let i=Ai.get(s);i||Ai.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Gs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function Lv(e,t,s=lt){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:f}=s,g=se=>{(s.onWarn||Gs)("Invalid watch source: ",se,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},h=se=>n?se:Xt(se)||n===!1||n===0?dr(se,1):dr(se);let p,v,C,P,A=!1,oe=!1;if(It(e)?(v=()=>e.value,A=Xt(e)):ln(e)?(v=()=>h(e),A=!0):Ee(e)?(oe=!0,A=e.some(se=>ln(se)||Xt(se)),v=()=>e.map(se=>{if(It(se))return se.value;if(ln(se))return h(se);if(Ae(se))return f?f(se,2):se();({}).NODE_ENV!=="production"&&g(se)})):Ae(e)?t?v=f?()=>f(e,2):e:v=()=>{if(C){Ps();try{C()}finally{Ms()}}const se=un;un=p;try{return f?f(e,3,[P]):e(P)}finally{un=se}}:(v=Nt,{}.NODE_ENV!=="production"&&g(e)),t&&n){const se=v,be=n===!0?1/0:n;v=()=>dr(se(),be)}const W=fv(),ie=()=>{p.stop(),W&&W.active&&gl(W.effects,p)};if(a&&t){const se=t;t=(...be)=>{se(...be),ie()}}let q=oe?new Array(e.length).fill(Ti):Ti;const ye=se=>{if(!(!(p.flags&1)||!p.dirty&&!se))if(t){const be=p.run();if(n||A||(oe?be.some((xe,ke)=>Vr(xe,q[ke])):Vr(be,q))){C&&C();const xe=un;un=p;try{const ke=[be,q===Ti?void 0:oe&&q[0]===Ti?[]:q,P];q=be,f?f(t,3,ke):t(...ke)}finally{un=xe}}}else p.run()};return c&&c(ye),p=new bd(v),p.scheduler=u?()=>u(ye,!1):ye,P=se=>Vv(se,!1,p),C=p.onStop=()=>{const se=Ai.get(p);if(se){if(f)f(se,4);else for(const be of se)be();Ai.delete(p)}},{}.NODE_ENV!=="production"&&(p.onTrack=s.onTrack,p.onTrigger=s.onTrigger),t?i?ye(!0):q=p.run():u?u(ye.bind(null,!0),!0):p.run(),ie.pause=p.pause.bind(p),ie.resume=p.resume.bind(p),ie.stop=ie,ie}function dr(e,t=1/0,s){if(t<=0||!rt(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,It(e))dr(e.value,t,s);else if(Ee(e))for(let i=0;i<e.length;i++)dr(e[i],t,s);else if(kn(e)||rn(e))e.forEach(i=>{dr(i,t,s)});else if(pd(e)){for(const i in e)dr(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&dr(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const cn=[];function Ri(e){cn.push(e)}function Pi(){cn.pop()}let Ml=!1;function Z(e,...t){if(Ml)return;Ml=!0,Ps();const s=cn.length?cn[cn.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=$v();if(i)$n(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Qi(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...Fv(n)),console.warn(...a)}Ms(),Ml=!1}function $v(){let e=cn[cn.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function Fv(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...Uv(s))}),t}function Uv({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Qi(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...Bv(e.props),a]:[n+a]}function Bv(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...Wd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function Wd(e,t,s){return gt(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:It(t)?(t=Wd(e,Ve(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):Ae(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Ve(t),s?t:[`${e}=`,t])}function qv(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Z(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Z(`${t} is NaN - the duration expression might be incorrect.`))}const kl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function $n(e,t,s,i){try{return i?e(...i):e()}catch(n){Io(n,t,s)}}function ks(e,t,s,i){if(Ae(e)){const n=$n(e,t,s,i);return n&&_l(n)&&n.catch(a=>{Io(a,t,s)}),n}if(Ee(e)){const n=[];for(let a=0;a<e.length;a++)n.push(ks(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Z(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function Io(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||lt;if(t){let c=t.parent;const f=t.proxy,g={}.NODE_ENV!=="production"?kl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const h=c.ec;if(h){for(let p=0;p<h.length;p++)if(h[p](e,f,g)===!1)return}c=c.parent}if(a){Ps(),$n(a,null,10,[e,f,g]),Ms();return}}Hv(e,s,n,i,u)}function Hv(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=kl[t];if(s&&Ri(s),Z(`Unhandled error${a?` during execution of ${a}`:""}`),s&&Pi(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Yt=[];let Ys=-1;const Fn=[];let $r=null,Un=0;const jd=Promise.resolve();let Mi=null;const Wv=100;function Vl(e){const t=Mi||jd;return e?t.then(this?e.bind(this):e):t}function jv(e){let t=Ys+1,s=Yt.length;for(;t<s;){const i=t+s>>>1,n=Yt[i],a=Do(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function ki(e){if(!(e.flags&1)){const t=Do(e),s=Yt[Yt.length-1];!s||!(e.flags&2)&&t>=Do(s)?Yt.push(e):Yt.splice(jv(t),0,e),e.flags|=1,Gd()}}function Gd(){Mi||(Mi=jd.then(Yd))}function zd(e){Ee(e)?Fn.push(...e):$r&&e.id===-1?$r.splice(Un+1,0,e):e.flags&1||(Fn.push(e),e.flags|=1),Gd()}function Kd(e,t,s=Ys+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Yt.length;s++){const i=Yt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&Ll(t,i))continue;Yt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function Xd(e){if(Fn.length){const t=[...new Set(Fn)].sort((s,i)=>Do(s)-Do(i));if(Fn.length=0,$r){$r.push(...t);return}for($r=t,{}.NODE_ENV!=="production"&&(e=e||new Map),Un=0;Un<$r.length;Un++){const s=$r[Un];({}).NODE_ENV!=="production"&&Ll(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}$r=null,Un=0}}const Do=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Yd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>Ll(e,s):Nt;try{for(Ys=0;Ys<Yt.length;Ys++){const s=Yt[Ys];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),$n(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ys<Yt.length;Ys++){const s=Yt[Ys];s&&(s.flags&=-2)}Ys=-1,Yt.length=0,Xd(e),Mi=null,(Yt.length||Fn.length)&&Yd(e)}}function Ll(e,t){const s=e.get(t)||0;if(s>Wv){const i=t.i,n=i&&du(i.type);return Io(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let Vs=!1;const Vi=new Map;({}).NODE_ENV!=="production"&&(bo().__VUE_HMR_RUNTIME__={createRecord:$l(Jd),rerender:$l(Kv),reload:$l(Xv)});const dn=new Map;function Gv(e){const t=e.type.__hmrId;let s=dn.get(t);s||(Jd(t,e.type),s=dn.get(t)),s.instances.add(e)}function zv(e){dn.get(e.type.__hmrId).instances.delete(e)}function Jd(e,t){return dn.has(e)?!1:(dn.set(e,{initialDef:Li(t),instances:new Set}),!0)}function Li(e){return lh(e)?e.__vccOpts:e}function Kv(e,t){const s=dn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Li(i.type).render=t),i.renderCache=[],Vs=!0,i.update(),Vs=!1}))}function Xv(e,t){const s=dn.get(e);if(!s)return;t=Li(t),Qd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Li(a.type);let c=Vi.get(u);c||(u!==s.initialDef&&Qd(u,t),Vi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?ki(()=>{Vs=!0,a.parent.update(),Vs=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}zd(()=>{Vi.clear()})}function Qd(e,t){vt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function $l(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Js,No=[],Fl=!1;function To(e,...t){Js?Js.emit(e,...t):Fl||No.push({event:e,args:t})}function Zd(e,t){var s,i;Js=e,Js?(Js.enabled=!0,No.forEach(({event:n,args:a})=>Js.emit(n,...a)),No=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{Zd(a,t)}),setTimeout(()=>{Js||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Fl=!0,No=[])},3e3)):(Fl=!0,No=[])}function Yv(e,t){To("app:init",e,t,{Fragment:je,Text:Lo,Comment:Ot,Static:$o})}function Jv(e){To("app:unmount",e)}const Qv=Ul("component:added"),ef=Ul("component:updated"),Zv=Ul("component:removed"),ey=e=>{Js&&typeof Js.cleanupBuffer=="function"&&!Js.cleanupBuffer(e)&&Zv(e)};/*! #__NO_SIDE_EFFECTS__ */function Ul(e){return t=>{To(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const ty=tf("perf:start"),sy=tf("perf:end");function tf(e){return(t,s,i)=>{To(e,t.appContext.app,t.uid,t,s,i)}}function ry(e,t,s){To("component:emit",e.appContext.app,e,t,s)}let wt=null,sf=null;function $i(e){const t=wt;return wt=e,sf=e&&e.type.__scopeId||null,t}function Re(e,t=wt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&Qf(-1);const a=$i(t);let u;try{u=e(...n)}finally{$i(a),i._d&&Qf(1)}return{}.NODE_ENV!=="production"&&ef(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function rf(e){X_(e)&&Z("Do not use built-in directive ids as custom directive id: "+e)}function Jt(e,t){if(wt===null)return{}.NODE_ENV!=="production"&&Z("withDirectives can only be used inside render functions."),e;const s=Ji(wt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,f=lt]=t[n];a&&(Ae(a)&&(a={mounted:a,updated:a}),a.deep&&dr(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:f}))}return e}function fn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let f=c.dir[i];f&&(Ps(),ks(f,s,8,[e.el,c,e,t]),Ms())}}const nf=Symbol("_vte"),of=e=>e.__isTeleport,hn=e=>e&&(e.disabled||e.disabled===""),af=e=>e&&(e.defer||e.defer===""),lf=e=>typeof SVGElement<"u"&&e instanceof SVGElement,uf=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Bl=(e,t)=>{const s=e&&e.to;if(gt(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!hn(e)&&Z(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Z("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!hn(e)&&Z(`Invalid Teleport target: ${s}`),s},cf={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,f,g){const{mc:h,pc:p,pbc:v,o:{insert:C,querySelector:P,createText:A,createComment:oe}}=g,W=hn(t.props);let{shapeFlag:ie,children:q,dynamicChildren:ye}=t;if({}.NODE_ENV!=="production"&&Vs&&(f=!1,ye=null),e==null){const se=t.el={}.NODE_ENV!=="production"?oe("teleport start"):A(""),be=t.anchor={}.NODE_ENV!=="production"?oe("teleport end"):A("");C(se,s,i),C(be,s,i);const xe=(de,De)=>{ie&16&&(n&&n.isCE&&(n.ce._teleportTarget=de),h(q,de,De,n,a,u,c,f))},ke=()=>{const de=t.target=Bl(t.props,P),De=df(de,t,A,C);de?(u!=="svg"&&lf(de)?u="svg":u!=="mathml"&&uf(de)&&(u="mathml"),W||(xe(de,De),Ui(t,!1))):{}.NODE_ENV!=="production"&&!W&&Z("Invalid Teleport target on mount:",de,`(${typeof de})`)};W&&(xe(s,be),Ui(t,!0)),af(t.props)?(t.el.__isMounted=!1,Zt(()=>{ke(),delete t.el.__isMounted},a)):ke()}else{if(af(t.props)&&e.el.__isMounted===!1){Zt(()=>{cf.process(e,t,s,i,n,a,u,c,f,g)},a);return}t.el=e.el,t.targetStart=e.targetStart;const se=t.anchor=e.anchor,be=t.target=e.target,xe=t.targetAnchor=e.targetAnchor,ke=hn(e.props),de=ke?s:be,De=ke?se:xe;if(u==="svg"||lf(be)?u="svg":(u==="mathml"||uf(be))&&(u="mathml"),ye?(v(e.dynamicChildren,ye,de,n,a,u,c),Vo(e,t,{}.NODE_ENV==="production")):f||p(e,t,de,De,n,a,u,c,!1),W)ke?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Fi(t,s,se,g,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const $e=t.target=Bl(t.props,P);$e?Fi(t,$e,null,g,0):{}.NODE_ENV!=="production"&&Z("Invalid Teleport target on update:",be,`(${typeof be})`)}else ke&&Fi(t,be,xe,g,1);Ui(t,W)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:f,targetStart:g,targetAnchor:h,target:p,props:v}=e;if(p&&(n(g),n(h)),a&&n(f),u&16){const C=a||!hn(v);for(let P=0;P<c.length;P++){const A=c[P];i(A,t,s,C,!!A.dynamicChildren)}}},move:Fi,hydrate:ny};function Fi(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:f,children:g,props:h}=e,p=a===2;if(p&&i(u,t,s),(!p||hn(h))&&f&16)for(let v=0;v<g.length;v++)n(g[v],t,s,2);p&&i(c,t,s)}function ny(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:f,insert:g,createText:h}},p){const v=t.target=Bl(t.props,f);if(v){const C=hn(t.props),P=v._lpa||v.firstChild;if(t.shapeFlag&16)if(C)t.anchor=p(u(e),t,c(e),s,i,n,a),t.targetStart=P,t.targetAnchor=P&&u(P);else{t.anchor=u(e);let A=P;for(;A;){if(A&&A.nodeType===8){if(A.data==="teleport start anchor")t.targetStart=A;else if(A.data==="teleport anchor"){t.targetAnchor=A,v._lpa=t.targetAnchor&&u(t.targetAnchor);break}}A=u(A)}t.targetAnchor||df(v,t,h,g),p(P&&u(P),t,v,s,i,n,a)}Ui(t,C)}return t.anchor&&u(t.anchor)}const oy=cf;function Ui(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function df(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[nf]=a,e&&(i(n,e),i(a,e)),a}const Fr=Symbol("_leaveCb"),Bi=Symbol("_enterCb");function iy(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ef(()=>{e.isMounted=!0}),Cf(()=>{e.isUnmounting=!0}),e}const bs=[Function,Array],ff={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:bs,onEnter:bs,onAfterEnter:bs,onEnterCancelled:bs,onBeforeLeave:bs,onLeave:bs,onAfterLeave:bs,onLeaveCancelled:bs,onBeforeAppear:bs,onAppear:bs,onAfterAppear:bs,onAppearCancelled:bs},hf=e=>{const t=e.subTree;return t.component?hf(t.component):t},ay={name:"BaseTransition",props:ff,setup(e,{slots:t}){const s=Xi(),i=iy();return()=>{const n=t.default&&_f(t.default(),!0);if(!n||!n.length)return;const a=pf(n),u=Ve(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Z(`invalid <transition> mode: ${c}`),i.isLeaving)return Hl(a);const f=gf(a);if(!f)return Hl(a);let g=ql(f,u,i,s,p=>g=p);f.type!==Ot&&Ao(f,g);let h=s.subTree&&gf(s.subTree);if(h&&h.type!==Ot&&!_n(f,h)&&hf(s).type!==Ot){let p=ql(h,u,i,s);if(Ao(h,p),c==="out-in"&&f.type!==Ot)return i.isLeaving=!0,p.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete p.afterLeave,h=void 0},Hl(a);c==="in-out"&&f.type!==Ot?p.delayLeave=(v,C,P)=>{const A=mf(i,h);A[String(h.key)]=h,v[Fr]=()=>{C(),v[Fr]=void 0,delete g.delayedLeave,h=void 0},g.delayedLeave=()=>{P(),delete g.delayedLeave,h=void 0}}:h=void 0}else h&&(h=void 0);return a}}};function pf(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==Ot){if({}.NODE_ENV!=="production"&&s){Z("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const ly=ay;function mf(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function ql(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:f,onEnter:g,onAfterEnter:h,onEnterCancelled:p,onBeforeLeave:v,onLeave:C,onAfterLeave:P,onLeaveCancelled:A,onBeforeAppear:oe,onAppear:W,onAfterAppear:ie,onAppearCancelled:q}=t,ye=String(e.key),se=mf(s,e),be=(de,De)=>{de&&ks(de,i,9,De)},xe=(de,De)=>{const $e=De[1];be(de,De),Ee(de)?de.every(ue=>ue.length<=1)&&$e():de.length<=1&&$e()},ke={mode:u,persisted:c,beforeEnter(de){let De=f;if(!s.isMounted)if(a)De=oe||f;else return;de[Fr]&&de[Fr](!0);const $e=se[ye];$e&&_n(e,$e)&&$e.el[Fr]&&$e.el[Fr](),be(De,[de])},enter(de){let De=g,$e=h,ue=p;if(!s.isMounted)if(a)De=W||g,$e=ie||h,ue=q||p;else return;let R=!1;const G=de[Bi]=Y=>{R||(R=!0,Y?be(ue,[de]):be($e,[de]),ke.delayedLeave&&ke.delayedLeave(),de[Bi]=void 0)};De?xe(De,[de,G]):G()},leave(de,De){const $e=String(e.key);if(de[Bi]&&de[Bi](!0),s.isUnmounting)return De();be(v,[de]);let ue=!1;const R=de[Fr]=G=>{ue||(ue=!0,De(),G?be(A,[de]):be(P,[de]),de[Fr]=void 0,se[$e]===e&&delete se[$e])};se[$e]=e,C?xe(C,[de,R]):R()},clone(de){const De=ql(de,t,s,i,n);return n&&n(De),De}};return ke}function Hl(e){if(Po(e))return e=Qs(e),e.children=null,e}function gf(e){if(!Po(e))return of(e.type)&&e.children?pf(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&Ae(s.default))return s.default()}}function Ao(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ao(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function _f(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===je?(u.patchFlag&128&&n++,i=i.concat(_f(u.children,t,c))):(t||u.type!==Ot)&&i.push(c!=null?Qs(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function vf(e,t){return Ae(e)?(()=>vt({name:e.name},t,{setup:e}))():e}function yf(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const uy=new WeakSet;function Ro(e,t,s,i,n=!1){if(Ee(e)){e.forEach((P,A)=>Ro(P,t&&(Ee(t)?t[A]:t),s,i,n));return}if(Bn(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Ro(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ji(i.component):i.el,u=n?null:a,{i:c,r:f}=e;if({}.NODE_ENV!=="production"&&!c){Z("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const g=t&&t.r,h=c.refs===lt?c.refs={}:c.refs,p=c.setupState,v=Ve(p),C=p===lt?()=>!1:P=>({}).NODE_ENV!=="production"&&(tt(v,P)&&!It(v[P])&&Z(`Template ref "${P}" used on a non-ref value. It will not work in the production build.`),uy.has(v[P]))?!1:tt(v,P);if(g!=null&&g!==f&&(gt(g)?(h[g]=null,C(g)&&(p[g]=null)):It(g)&&(g.value=null)),Ae(f))$n(f,c,12,[u,h]);else{const P=gt(f),A=It(f);if(P||A){const oe=()=>{if(e.f){const W=P?C(f)?p[f]:h[f]:f.value;n?Ee(W)&&gl(W,a):Ee(W)?W.includes(a)||W.push(a):P?(h[f]=[a],C(f)&&(p[f]=h[f])):(f.value=[a],e.k&&(h[e.k]=f.value))}else P?(h[f]=u,C(f)&&(p[f]=u)):A?(f.value=u,e.k&&(h[e.k]=u)):{}.NODE_ENV!=="production"&&Z("Invalid template ref type:",f,`(${typeof f})`)};u?(oe.id=-1,Zt(oe,s)):oe()}else({}).NODE_ENV!=="production"&&Z("Invalid template ref type:",f,`(${typeof f})`)}}bo().requestIdleCallback,bo().cancelIdleCallback;const Bn=e=>!!e.type.__asyncLoader,Po=e=>e.type.__isKeepAlive;function cy(e,t){bf(e,"a",t)}function dy(e,t){bf(e,"da",t)}function bf(e,t,s=Pt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(qi(t,i,s),s){let n=s.parent;for(;n&&n.parent;)Po(n.parent.vnode)&&fy(i,t,s,n),n=n.parent}}function fy(e,t,s,i){const n=qi(t,e,i,!0);wf(()=>{gl(i[t],n)},s)}function qi(e,t,s=Pt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{Ps();const c=Bo(s),f=ks(t,s,e,u);return c(),Ms(),f});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=on(kl[e].replace(/ hook$/,""));Z(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const fr=e=>(t,s=Pt)=>{(!qo||e==="sp")&&qi(e,(...i)=>t(...i),s)},hy=fr("bm"),Ef=fr("m"),py=fr("bu"),my=fr("u"),Cf=fr("bum"),wf=fr("um"),gy=fr("sp"),_y=fr("rtg"),vy=fr("rtc");function yy(e,t=Pt){qi("ec",e,t)}const Wl="components";function L(e,t){return Ey(Wl,e,!0,t)||e}const by=Symbol.for("v-ndc");function Ey(e,t,s=!0,i=!1){const n=wt||Pt;if(n){const a=n.type;if(e===Wl){const c=du(a,!1);if(c&&(c===t||c===Kt(t)||c===nn(Kt(t))))return a}const u=Of(n[e]||a[e],t)||Of(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===Wl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Z(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Z(`resolve${nn(e.slice(0,-1))} can only be used in render() or setup().`)}function Of(e,t){return e&&(e[t]||e[Kt(t)]||e[nn(Kt(t))])}function At(e,t,s,i){let n;const a=s&&s[i],u=Ee(e);if(u||gt(e)){const c=u&&ln(e);let f=!1,g=!1;c&&(f=!Xt(e),g=Xs(e),e=Ci(e)),n=new Array(e.length);for(let h=0,p=e.length;h<p;h++)n[h]=t(f?g?Ni(Bt(e[h])):Bt(e[h]):e[h],h,void 0,a&&a[h])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Z(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(rt(e))if(e[Symbol.iterator])n=Array.from(e,(c,f)=>t(c,f,void 0,a&&a[f]));else{const c=Object.keys(e);n=new Array(c.length);for(let f=0,g=c.length;f<g;f++){const h=c[f];n[f]=t(e[h],h,f,a&&a[f])}}else n=[];return s&&(s[i]=n),n}function Rt(e,t,s={},i,n){if(wt.ce||wt.parent&&Bn(wt.parent)&&wt.parent.ce)return t!=="default"&&(s.name=t),S(),ct(je,null,[x("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Z("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),S();const u=a&&xf(a(s)),c=s.key||u&&u.key,f=ct(je,{key:(c&&!Ts(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&f.scopeId&&(f.slotScopeIds=[f.scopeId+"-s"]),a&&a._c&&(a._d=!0),f}function xf(e){return e.some(t=>gn(t)?!(t.type===Ot||t.type===je&&!xf(t.children)):!0)?e:null}const jl=e=>e?nh(e)?Ji(e):jl(e.parent):null,pn=vt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?Ks(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?Ks(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?Ks(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?Ks(e.refs):e.refs,$parent:e=>jl(e.parent),$root:e=>jl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Xl(e),$forceUpdate:e=>e.f||(e.f=()=>{ki(e.update)}),$nextTick:e=>e.n||(e.n=Vl.bind(e.proxy)),$watch:e=>eb.bind(e)}),Gl=e=>e==="_"||e==="$",zl=(e,t)=>e!==lt&&!e.__isScriptSetup&&tt(e,t),Sf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:f}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let g;if(t[0]!=="$"){const C=u[t];if(C!==void 0)switch(C){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(zl(i,t))return u[t]=1,i[t];if(n!==lt&&tt(n,t))return u[t]=2,n[t];if((g=e.propsOptions[0])&&tt(g,t))return u[t]=3,a[t];if(s!==lt&&tt(s,t))return u[t]=4,s[t];Kl&&(u[t]=0)}}const h=pn[t];let p,v;if(h)return t==="$attrs"?(Tt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&zi()):{}.NODE_ENV!=="production"&&t==="$slots"&&Tt(e,"get",t),h(e);if((p=c.__cssModules)&&(p=p[t]))return p;if(s!==lt&&tt(s,t))return u[t]=4,s[t];if(v=f.config.globalProperties,tt(v,t))return v[t];({}).NODE_ENV!=="production"&&wt&&(!gt(t)||t.indexOf("__v")!==0)&&(n!==lt&&Gl(t[0])&&tt(n,t)?Z(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===wt&&Z(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return zl(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&tt(n,t)?(Z(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==lt&&tt(i,t)?(i[t]=s,!0):tt(e.props,t)?({}.NODE_ENV!=="production"&&Z(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Z(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==lt&&tt(e,u)||zl(t,u)||(c=a[0])&&tt(c,u)||tt(i,u)||tt(pn,u)||tt(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:tt(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Sf.ownKeys=e=>(Z("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function Cy(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(pn).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>pn[s](e),set:Nt})}),t}function wy(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:Nt})})}function Oy(e){const{ctx:t,setupState:s}=e;Object.keys(Ve(s)).forEach(i=>{if(!s.__isScriptSetup){if(Gl(i[0])){Z(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:Nt})}})}function If(e){return Ee(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function xy(){const e=Object.create(null);return(t,s)=>{e[s]?Z(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Kl=!0;function Sy(e){const t=Xl(e),s=e.proxy,i=e.ctx;Kl=!1,t.beforeCreate&&Df(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:f,inject:g,created:h,beforeMount:p,mounted:v,beforeUpdate:C,updated:P,activated:A,deactivated:oe,beforeDestroy:W,beforeUnmount:ie,destroyed:q,unmounted:ye,render:se,renderTracked:be,renderTriggered:xe,errorCaptured:ke,serverPrefetch:de,expose:De,inheritAttrs:$e,components:ue,directives:R,filters:G}=t,Y={}.NODE_ENV!=="production"?xy():null;if({}.NODE_ENV!=="production"){const[j]=e.propsOptions;if(j)for(const J in j)Y("Props",J)}if(g&&Iy(g,i,Y),u)for(const j in u){const J=u[j];Ae(J)?({}.NODE_ENV!=="production"?Object.defineProperty(i,j,{value:J.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[j]=J.bind(s),{}.NODE_ENV!=="production"&&Y("Methods",j)):{}.NODE_ENV!=="production"&&Z(`Method "${j}" has type "${typeof J}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!Ae(n)&&Z("The data option must be a function. Plain object usage is no longer supported.");const j=n.call(s,s);if({}.NODE_ENV!=="production"&&_l(j)&&Z("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!rt(j))({}).NODE_ENV!=="production"&&Z("data() should return an object.");else if(e.data=Si(j),{}.NODE_ENV!=="production")for(const J in j)Y("Data",J),Gl(J[0])||Object.defineProperty(i,J,{configurable:!0,enumerable:!0,get:()=>j[J],set:Nt})}if(Kl=!0,a)for(const j in a){const J=a[j],ge=Ae(J)?J.bind(s,s):Ae(J.get)?J.get.bind(s,s):Nt;({}).NODE_ENV!=="production"&&ge===Nt&&Z(`Computed property "${j}" has no getter.`);const ee=!Ae(J)&&Ae(J.set)?J.set.bind(s):{}.NODE_ENV!=="production"?()=>{Z(`Write operation failed: computed property "${j}" is readonly.`)}:Nt,_e=$s({get:ge,set:ee});Object.defineProperty(i,j,{enumerable:!0,configurable:!0,get:()=>_e.value,set:Se=>_e.value=Se}),{}.NODE_ENV!=="production"&&Y("Computed",j)}if(c)for(const j in c)Nf(c[j],i,s,j);if(f){const j=Ae(f)?f.call(s):f;Reflect.ownKeys(j).forEach(J=>{Wi(J,j[J])})}h&&Df(h,e,"c");function pe(j,J){Ee(J)?J.forEach(ge=>j(ge.bind(s))):J&&j(J.bind(s))}if(pe(hy,p),pe(Ef,v),pe(py,C),pe(my,P),pe(cy,A),pe(dy,oe),pe(yy,ke),pe(vy,be),pe(_y,xe),pe(Cf,ie),pe(wf,ye),pe(gy,de),Ee(De))if(De.length){const j=e.exposed||(e.exposed={});De.forEach(J=>{Object.defineProperty(j,J,{get:()=>s[J],set:ge=>s[J]=ge})})}else e.exposed||(e.exposed={});se&&e.render===Nt&&(e.render=se),$e!=null&&(e.inheritAttrs=$e),ue&&(e.components=ue),R&&(e.directives=R),de&&yf(e)}function Iy(e,t,s=Nt){Ee(e)&&(e=Yl(e));for(const i in e){const n=e[i];let a;rt(n)?"default"in n?a=hr(n.from||i,n.default,!0):a=hr(n.from||i):a=hr(n),It(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Df(e,t,s){ks(Ee(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Nf(e,t,s,i){let n=i.includes(".")?Gf(s,i):()=>s[i];if(gt(e)){const a=t[e];Ae(a)?Hn(n,a):{}.NODE_ENV!=="production"&&Z(`Invalid watch handler specified by key "${e}"`,a)}else if(Ae(e))Hn(n,e.bind(s));else if(rt(e))if(Ee(e))e.forEach(a=>Nf(a,t,s,i));else{const a=Ae(e.handler)?e.handler.bind(s):t[e.handler];Ae(a)?Hn(n,a,e):{}.NODE_ENV!=="production"&&Z(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Z(`Invalid watch option: "${i}"`,e)}function Xl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let f;return c?f=c:!n.length&&!s&&!i?f=t:(f={},n.length&&n.forEach(g=>Hi(f,g,u,!0)),Hi(f,t,u)),rt(t)&&a.set(t,f),f}function Hi(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Hi(e,a,s,!0),n&&n.forEach(u=>Hi(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Z('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=Dy[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const Dy={data:Tf,props:Af,emits:Af,methods:Mo,computed:Mo,beforeCreate:Qt,created:Qt,beforeMount:Qt,mounted:Qt,beforeUpdate:Qt,updated:Qt,beforeDestroy:Qt,beforeUnmount:Qt,destroyed:Qt,unmounted:Qt,activated:Qt,deactivated:Qt,errorCaptured:Qt,serverPrefetch:Qt,components:Mo,directives:Mo,watch:Ty,provide:Tf,inject:Ny};function Tf(e,t){return t?e?function(){return vt(Ae(e)?e.call(this,this):e,Ae(t)?t.call(this,this):t)}:t:e}function Ny(e,t){return Mo(Yl(e),Yl(t))}function Yl(e){if(Ee(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Qt(e,t){return e?[...new Set([].concat(e,t))]:t}function Mo(e,t){return e?vt(Object.create(null),e,t):t}function Af(e,t){return e?Ee(e)&&Ee(t)?[...new Set([...e,...t])]:vt(Object.create(null),If(e),If(t??{})):t}function Ty(e,t){if(!e)return t;if(!t)return e;const s=vt(Object.create(null),e);for(const i in t)s[i]=Qt(e[i],t[i]);return s}function Rf(){return{app:null,config:{isNativeTag:z_,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ay=0;function Ry(e,t){return function(i,n=null){Ae(i)||(i=vt({},i)),n!=null&&!rt(n)&&({}.NODE_ENV!=="production"&&Z("root props passed to app.mount() must be an object."),n=null);const a=Rf(),u=new WeakSet,c=[];let f=!1;const g=a.app={_uid:Ay++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:uh,get config(){return a.config},set config(h){({}).NODE_ENV!=="production"&&Z("app.config cannot be replaced. Modify individual options instead.")},use(h,...p){return u.has(h)?{}.NODE_ENV!=="production"&&Z("Plugin has already been applied to target app."):h&&Ae(h.install)?(u.add(h),h.install(g,...p)):Ae(h)?(u.add(h),h(g,...p)):{}.NODE_ENV!=="production"&&Z('A plugin must either be a function or an object with an "install" function.'),g},mixin(h){return a.mixins.includes(h)?{}.NODE_ENV!=="production"&&Z("Mixin has already been applied to target app"+(h.name?`: ${h.name}`:"")):a.mixins.push(h),g},component(h,p){return{}.NODE_ENV!=="production"&&uu(h,a.config),p?({}.NODE_ENV!=="production"&&a.components[h]&&Z(`Component "${h}" has already been registered in target app.`),a.components[h]=p,g):a.components[h]},directive(h,p){return{}.NODE_ENV!=="production"&&rf(h),p?({}.NODE_ENV!=="production"&&a.directives[h]&&Z(`Directive "${h}" has already been registered in target app.`),a.directives[h]=p,g):a.directives[h]},mount(h,p,v){if(f)({}).NODE_ENV!=="production"&&Z("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&h.__vue_app__&&Z("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const C=g._ceVNode||x(i,n);return C.appContext=a,v===!0?v="svg":v===!1&&(v=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{const P=Qs(C);P.el=null,e(P,h,v)}),p&&t?t(C,h):e(C,h,v),f=!0,g._container=h,h.__vue_app__=g,{}.NODE_ENV!=="production"&&(g._instance=C.component,Yv(g,uh)),Ji(C.component)}},onUnmount(h){({}).NODE_ENV!=="production"&&typeof h!="function"&&Z(`Expected function as first argument to app.onUnmount(), but got ${typeof h}`),c.push(h)},unmount(){f?(ks(c,g._instance,16),e(null,g._container),{}.NODE_ENV!=="production"&&(g._instance=null,Jv(g)),delete g._container.__vue_app__):{}.NODE_ENV!=="production"&&Z("Cannot unmount an app that is not mounted.")},provide(h,p){return{}.NODE_ENV!=="production"&&h in a.provides&&(tt(a.provides,h)?Z(`App already provides property with key "${String(h)}". It will be overwritten with the new value.`):Z(`App already provides property with key "${String(h)}" inherited from its parent element. It will be overwritten with the new value.`)),a.provides[h]=p,g},runWithContext(h){const p=qn;qn=g;try{return h()}finally{qn=p}}};return g}}let qn=null;function Wi(e,t){if(!Pt)({}).NODE_ENV!=="production"&&Z("provide() can only be used inside setup().");else{let s=Pt.provides;const i=Pt.parent&&Pt.parent.provides;i===s&&(s=Pt.provides=Object.create(i)),s[e]=t}}function hr(e,t,s=!1){const i=Pt||wt;if(i||qn){let n=qn?qn._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&Ae(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Z(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Z("inject() can only be used inside setup() or functional components.")}const Pf={},Mf=()=>Object.create(Pf),kf=e=>Object.getPrototypeOf(e)===Pf;function Py(e,t,s,i=!1){const n={},a=Mf();e.propsDefaults=Object.create(null),Vf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&Ff(t||{},n,e),s?e.props=i?n:Fd(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function My(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function ky(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Ve(n),[f]=e.propsOptions;let g=!1;if(!({}.NODE_ENV!=="production"&&My(e))&&(i||u>0)&&!(u&16)){if(u&8){const h=e.vnode.dynamicProps;for(let p=0;p<h.length;p++){let v=h[p];if(Gi(e.emitsOptions,v))continue;const C=t[v];if(f)if(tt(a,v))C!==a[v]&&(a[v]=C,g=!0);else{const P=Kt(v);n[P]=Jl(f,c,P,C,e,!1)}else C!==a[v]&&(a[v]=C,g=!0)}}}else{Vf(e,t,n,a)&&(g=!0);let h;for(const p in c)(!t||!tt(t,p)&&((h=kr(p))===p||!tt(t,h)))&&(f?s&&(s[p]!==void 0||s[h]!==void 0)&&(n[p]=Jl(f,c,p,void 0,e,!0)):delete n[p]);if(a!==c)for(const p in a)(!t||!tt(t,p))&&(delete a[p],g=!0)}g&&zs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&Ff(t||{},n,e)}function Vf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let f in t){if(vo(f))continue;const g=t[f];let h;n&&tt(n,h=Kt(f))?!a||!a.includes(h)?s[h]=g:(c||(c={}))[h]=g:Gi(e.emitsOptions,f)||(!(f in i)||g!==i[f])&&(i[f]=g,u=!0)}if(a){const f=Ve(s),g=c||lt;for(let h=0;h<a.length;h++){const p=a[h];s[p]=Jl(n,f,p,g[p],e,!tt(g,p))}}return u}function Jl(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=tt(u,"default");if(c&&i===void 0){const f=u.default;if(u.type!==Function&&!u.skipFactory&&Ae(f)){const{propsDefaults:g}=n;if(s in g)i=g[s];else{const h=Bo(n);i=g[s]=f.call(null,t),h()}}else i=f;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===kr(s))&&(i=!0))}return i}const Vy=new WeakMap;function Lf(e,t,s=!1){const i=s?Vy:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let f=!1;if(!Ae(e)){const h=p=>{f=!0;const[v,C]=Lf(p,t,!0);vt(u,v),C&&c.push(...C)};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}if(!a&&!f)return rt(e)&&i.set(e,Mn),Mn;if(Ee(a))for(let h=0;h<a.length;h++){({}).NODE_ENV!=="production"&&!gt(a[h])&&Z("props must be strings when using array syntax.",a[h]);const p=Kt(a[h]);$f(p)&&(u[p]=lt)}else if(a){({}).NODE_ENV!=="production"&&!rt(a)&&Z("invalid props options",a);for(const h in a){const p=Kt(h);if($f(p)){const v=a[h],C=u[p]=Ee(v)||Ae(v)?{type:v}:vt({},v),P=C.type;let A=!1,oe=!0;if(Ee(P))for(let W=0;W<P.length;++W){const ie=P[W],q=Ae(ie)&&ie.name;if(q==="Boolean"){A=!0;break}else q==="String"&&(oe=!1)}else A=Ae(P)&&P.name==="Boolean";C[0]=A,C[1]=oe,(A||tt(C,"default"))&&c.push(p)}}}const g=[u,c];return rt(e)&&i.set(e,g),g}function $f(e){return e[0]!=="$"&&!vo(e)?!0:({}.NODE_ENV!=="production"&&Z(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Ly(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function Ff(e,t,s){const i=Ve(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in n){let c=n[u];c!=null&&$y(u,i[u],c,{}.NODE_ENV!=="production"?Ks(i):i,!a.includes(u))}}function $y(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:f}=s;if(u&&n){Z('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!f){let g=!1;const h=Ee(a)?a:[a],p=[];for(let v=0;v<h.length&&!g;v++){const{valid:C,expectedType:P}=Uy(t,h[v]);p.push(P||""),g=C}if(!g){Z(By(e,t,p));return}}c&&!c(t,i)&&Z('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Fy=ur("String,Number,Boolean,Function,Symbol,BigInt");function Uy(e,t){let s;const i=Ly(t);if(i==="null")s=e===null;else if(Fy(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=rt(e):i==="Array"?s=Ee(e):s=e instanceof t;return{valid:s,expectedType:i}}function By(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(nn).join(" | ")}`;const n=s[0],a=vl(t),u=Uf(t,n),c=Uf(t,a);return s.length===1&&Bf(n)&&!qy(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,Bf(a)&&(i+=`with value ${c}.`),i}function Uf(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function Bf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function qy(...e){return e.some(t=>t.toLowerCase()==="boolean")}const Ql=e=>e[0]==="_"||e==="$stable",Zl=e=>Ee(e)?e.map(Ls):[Ls(e)],Hy=(e,t,s)=>{if(t._n)return t;const i=Re((...n)=>({}.NODE_ENV!=="production"&&Pt&&!(s===null&&wt)&&!(s&&s.root!==Pt.root)&&Z(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Zl(t(...n))),s);return i._c=!1,i},qf=(e,t,s)=>{const i=e._ctx;for(const n in e){if(Ql(n))continue;const a=e[n];if(Ae(a))t[n]=Hy(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Z(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Zl(a);t[n]=()=>u}}},Hf=(e,t)=>{({}).NODE_ENV!=="production"&&!Po(e.vnode)&&Z("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Zl(t);e.slots.default=()=>s},eu=(e,t,s)=>{for(const i in t)(s||!Ql(i))&&(e[i]=t[i])},Wy=(e,t,s)=>{const i=e.slots=Mf();if(e.vnode.shapeFlag&32){const n=t.__;n&&yo(i,"__",n,!0);const a=t._;a?(eu(i,t,s),s&&yo(i,"_",a,!0)):qf(t,i)}else t&&Hf(e,t)},jy=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=lt;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&Vs?(eu(n,t,s),zs(e,"set","$slots")):s&&c===1?a=!1:eu(n,t,s):(a=!t.$stable,qf(t,n)),u=t}else t&&(Hf(e,t),u={default:1});if(a)for(const c in n)!Ql(c)&&u[c]==null&&delete n[c]};let ko,Ur;function pr(e,t){e.appContext.config.performance&&ji()&&Ur.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&ty(e,t,ji()?Ur.now():Date.now())}function mr(e,t){if(e.appContext.config.performance&&ji()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ur.mark(i),Ur.measure(`<${Qi(e,e.type)}> ${t}`,s,i),Ur.clearMarks(s),Ur.clearMarks(i)}({}).NODE_ENV!=="production"&&sy(e,t,ji()?Ur.now():Date.now())}function ji(){return ko!==void 0||(typeof window<"u"&&window.performance?(ko=!0,Ur=window.performance):ko=!1),ko}function Gy(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Zt=ab;function zy(e){return Ky(e)}function Ky(e,t){Gy();const s=bo();s.__VUE__=!0,{}.NODE_ENV!=="production"&&Zd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:f,setText:g,setElementText:h,parentNode:p,nextSibling:v,setScopeId:C=Nt,insertStaticContent:P}=e,A=(E,O,k,F=null,H=null,z=null,ae=void 0,Q=null,re={}.NODE_ENV!=="production"&&Vs?!1:!!O.dynamicChildren)=>{if(E===O)return;E&&!_n(E,O)&&(F=fe(E),Ye(E,H,z,!0),E=null),O.patchFlag===-2&&(re=!1,O.dynamicChildren=null);const{type:X,ref:Ie,shapeFlag:le}=O;switch(X){case Lo:oe(E,O,k,F);break;case Ot:W(E,O,k,F);break;case $o:E==null?ie(O,k,F,ae):{}.NODE_ENV!=="production"&&q(E,O,k,ae);break;case je:R(E,O,k,F,H,z,ae,Q,re);break;default:le&1?be(E,O,k,F,H,z,ae,Q,re):le&6?G(E,O,k,F,H,z,ae,Q,re):le&64||le&128?X.process(E,O,k,F,H,z,ae,Q,re,Ue):{}.NODE_ENV!=="production"&&Z("Invalid VNode type:",X,`(${typeof X})`)}Ie!=null&&H?Ro(Ie,E&&E.ref,z,O||E,!O):Ie==null&&E&&E.ref!=null&&Ro(E.ref,null,z,E,!0)},oe=(E,O,k,F)=>{if(E==null)i(O.el=c(O.children),k,F);else{const H=O.el=E.el;O.children!==E.children&&g(H,O.children)}},W=(E,O,k,F)=>{E==null?i(O.el=f(O.children||""),k,F):O.el=E.el},ie=(E,O,k,F)=>{[E.el,E.anchor]=P(E.children,O,k,F,E.el,E.anchor)},q=(E,O,k,F)=>{if(O.children!==E.children){const H=v(E.anchor);se(E),[O.el,O.anchor]=P(O.children,k,H,F)}else O.el=E.el,O.anchor=E.anchor},ye=({el:E,anchor:O},k,F)=>{let H;for(;E&&E!==O;)H=v(E),i(E,k,F),E=H;i(O,k,F)},se=({el:E,anchor:O})=>{let k;for(;E&&E!==O;)k=v(E),n(E),E=k;n(O)},be=(E,O,k,F,H,z,ae,Q,re)=>{O.type==="svg"?ae="svg":O.type==="math"&&(ae="mathml"),E==null?xe(O,k,F,H,z,ae,Q,re):De(E,O,H,z,ae,Q,re)},xe=(E,O,k,F,H,z,ae,Q)=>{let re,X;const{props:Ie,shapeFlag:le,transition:we,dirs:Ne}=E;if(re=E.el=u(E.type,z,Ie&&Ie.is,Ie),le&8?h(re,E.children):le&16&&de(E.children,re,null,F,H,tu(E,z),ae,Q),Ne&&fn(E,null,F,"created"),ke(re,E,E.scopeId,ae,F),Ie){for(const it in Ie)it!=="value"&&!vo(it)&&a(re,it,null,Ie[it],z,F);"value"in Ie&&a(re,"value",null,Ie.value,z),(X=Ie.onVnodeBeforeMount)&&Zs(X,F,E)}({}).NODE_ENV!=="production"&&(yo(re,"__vnode",E,!0),yo(re,"__vueParentComponent",F,!0)),Ne&&fn(E,null,F,"beforeMount");const He=Xy(H,we);He&&we.beforeEnter(re),i(re,O,k),((X=Ie&&Ie.onVnodeMounted)||He||Ne)&&Zt(()=>{X&&Zs(X,F,E),He&&we.enter(re),Ne&&fn(E,null,F,"mounted")},H)},ke=(E,O,k,F,H)=>{if(k&&C(E,k),F)for(let z=0;z<F.length;z++)C(E,F[z]);if(H){let z=H.subTree;if({}.NODE_ENV!=="production"&&z.patchFlag>0&&z.patchFlag&2048&&(z=ou(z.children)||z),O===z||Jf(z.type)&&(z.ssContent===O||z.ssFallback===O)){const ae=H.vnode;ke(E,ae,ae.scopeId,ae.slotScopeIds,H.parent)}}},de=(E,O,k,F,H,z,ae,Q,re=0)=>{for(let X=re;X<E.length;X++){const Ie=E[X]=Q?Br(E[X]):Ls(E[X]);A(null,Ie,O,k,F,H,z,ae,Q)}},De=(E,O,k,F,H,z,ae)=>{const Q=O.el=E.el;({}).NODE_ENV!=="production"&&(Q.__vnode=O);let{patchFlag:re,dynamicChildren:X,dirs:Ie}=O;re|=E.patchFlag&16;const le=E.props||lt,we=O.props||lt;let Ne;if(k&&mn(k,!1),(Ne=we.onVnodeBeforeUpdate)&&Zs(Ne,k,O,E),Ie&&fn(O,E,k,"beforeUpdate"),k&&mn(k,!0),{}.NODE_ENV!=="production"&&Vs&&(re=0,ae=!1,X=null),(le.innerHTML&&we.innerHTML==null||le.textContent&&we.textContent==null)&&h(Q,""),X?($e(E.dynamicChildren,X,Q,k,F,tu(O,H),z),{}.NODE_ENV!=="production"&&Vo(E,O)):ae||ge(E,O,Q,null,k,F,tu(O,H),z,!1),re>0){if(re&16)ue(Q,le,we,k,H);else if(re&2&&le.class!==we.class&&a(Q,"class",null,we.class,H),re&4&&a(Q,"style",le.style,we.style,H),re&8){const He=O.dynamicProps;for(let it=0;it<He.length;it++){const st=He[it],Ft=le[st],Dt=we[st];(Dt!==Ft||st==="value")&&a(Q,st,Ft,Dt,H,k)}}re&1&&E.children!==O.children&&h(Q,O.children)}else!ae&&X==null&&ue(Q,le,we,k,H);((Ne=we.onVnodeUpdated)||Ie)&&Zt(()=>{Ne&&Zs(Ne,k,O,E),Ie&&fn(O,E,k,"updated")},F)},$e=(E,O,k,F,H,z,ae)=>{for(let Q=0;Q<O.length;Q++){const re=E[Q],X=O[Q],Ie=re.el&&(re.type===je||!_n(re,X)||re.shapeFlag&198)?p(re.el):k;A(re,X,Ie,null,F,H,z,ae,!0)}},ue=(E,O,k,F,H)=>{if(O!==k){if(O!==lt)for(const z in O)!vo(z)&&!(z in k)&&a(E,z,O[z],null,H,F);for(const z in k){if(vo(z))continue;const ae=k[z],Q=O[z];ae!==Q&&z!=="value"&&a(E,z,Q,ae,H,F)}"value"in k&&a(E,"value",O.value,k.value,H)}},R=(E,O,k,F,H,z,ae,Q,re)=>{const X=O.el=E?E.el:c(""),Ie=O.anchor=E?E.anchor:c("");let{patchFlag:le,dynamicChildren:we,slotScopeIds:Ne}=O;({}).NODE_ENV!=="production"&&(Vs||le&2048)&&(le=0,re=!1,we=null),Ne&&(Q=Q?Q.concat(Ne):Ne),E==null?(i(X,k,F),i(Ie,k,F),de(O.children||[],k,Ie,H,z,ae,Q,re)):le>0&&le&64&&we&&E.dynamicChildren?($e(E.dynamicChildren,we,k,H,z,ae,Q),{}.NODE_ENV!=="production"?Vo(E,O):(O.key!=null||H&&O===H.subTree)&&Vo(E,O,!0)):ge(E,O,k,Ie,H,z,ae,Q,re)},G=(E,O,k,F,H,z,ae,Q,re)=>{O.slotScopeIds=Q,E==null?O.shapeFlag&512?H.ctx.activate(O,k,F,ae,re):Y(O,k,F,H,z,ae,re):pe(E,O,re)},Y=(E,O,k,F,H,z,ae)=>{const Q=E.component=pb(E,F,H);if({}.NODE_ENV!=="production"&&Q.type.__hmrId&&Gv(Q),{}.NODE_ENV!=="production"&&(Ri(E),pr(Q,"mount")),Po(E)&&(Q.ctx.renderer=Ue),{}.NODE_ENV!=="production"&&pr(Q,"init"),gb(Q,!1,ae),{}.NODE_ENV!=="production"&&mr(Q,"init"),{}.NODE_ENV!=="production"&&Vs&&(E.el=null),Q.asyncDep){if(H&&H.registerDep(Q,j,ae),!E.el){const re=Q.subTree=x(Ot);W(null,re,O,k)}}else j(Q,E,O,k,H,z,ae);({}).NODE_ENV!=="production"&&(Pi(),mr(Q,"mount"))},pe=(E,O,k)=>{const F=O.component=E.component;if(ob(E,O,k))if(F.asyncDep&&!F.asyncResolved){({}).NODE_ENV!=="production"&&Ri(O),J(F,O,k),{}.NODE_ENV!=="production"&&Pi();return}else F.next=O,F.update();else O.el=E.el,F.vnode=O},j=(E,O,k,F,H,z,ae)=>{const Q=()=>{if(E.isMounted){let{next:le,bu:we,u:Ne,parent:He,vnode:it}=E;{const qt=Wf(E);if(qt){le&&(le.el=it.el,J(E,le,ae)),qt.asyncDep.then(()=>{E.isUnmounted||Q()});return}}let st=le,Ft;({}).NODE_ENV!=="production"&&Ri(le||E.vnode),mn(E,!1),le?(le.el=it.el,J(E,le,ae)):le=it,we&&Vn(we),(Ft=le.props&&le.props.onVnodeBeforeUpdate)&&Zs(Ft,He,le,it),mn(E,!0),{}.NODE_ENV!=="production"&&pr(E,"render");const Dt=nu(E);({}).NODE_ENV!=="production"&&mr(E,"render");const ss=E.subTree;E.subTree=Dt,{}.NODE_ENV!=="production"&&pr(E,"patch"),A(ss,Dt,p(ss.el),fe(ss),E,H,z),{}.NODE_ENV!=="production"&&mr(E,"patch"),le.el=Dt.el,st===null&&ib(E,Dt.el),Ne&&Zt(Ne,H),(Ft=le.props&&le.props.onVnodeUpdated)&&Zt(()=>Zs(Ft,He,le,it),H),{}.NODE_ENV!=="production"&&ef(E),{}.NODE_ENV!=="production"&&Pi()}else{let le;const{el:we,props:Ne}=O,{bm:He,m:it,parent:st,root:Ft,type:Dt}=E,ss=Bn(O);if(mn(E,!1),He&&Vn(He),!ss&&(le=Ne&&Ne.onVnodeBeforeMount)&&Zs(le,st,O),mn(E,!0),we&&Be){const qt=()=>{({}).NODE_ENV!=="production"&&pr(E,"render"),E.subTree=nu(E),{}.NODE_ENV!=="production"&&mr(E,"render"),{}.NODE_ENV!=="production"&&pr(E,"hydrate"),Be(we,E.subTree,E,H,null),{}.NODE_ENV!=="production"&&mr(E,"hydrate")};ss&&Dt.__asyncHydrate?Dt.__asyncHydrate(we,E,qt):qt()}else{Ft.ce&&Ft.ce._def.shadowRoot!==!1&&Ft.ce._injectChildStyle(Dt),{}.NODE_ENV!=="production"&&pr(E,"render");const qt=E.subTree=nu(E);({}).NODE_ENV!=="production"&&mr(E,"render"),{}.NODE_ENV!=="production"&&pr(E,"patch"),A(null,qt,k,F,E,H,z),{}.NODE_ENV!=="production"&&mr(E,"patch"),O.el=qt.el}if(it&&Zt(it,H),!ss&&(le=Ne&&Ne.onVnodeMounted)){const qt=O;Zt(()=>Zs(le,st,qt),H)}(O.shapeFlag&256||st&&Bn(st.vnode)&&st.vnode.shapeFlag&256)&&E.a&&Zt(E.a,H),E.isMounted=!0,{}.NODE_ENV!=="production"&&Qv(E),O=k=F=null}};E.scope.on();const re=E.effect=new bd(Q);E.scope.off();const X=E.update=re.run.bind(re),Ie=E.job=re.runIfDirty.bind(re);Ie.i=E,Ie.id=E.uid,re.scheduler=()=>ki(Ie),mn(E,!0),{}.NODE_ENV!=="production"&&(re.onTrack=E.rtc?le=>Vn(E.rtc,le):void 0,re.onTrigger=E.rtg?le=>Vn(E.rtg,le):void 0),X()},J=(E,O,k)=>{O.component=E;const F=E.vnode.props;E.vnode=O,E.next=null,ky(E,O.props,F,k),jy(E,O.children,k),Ps(),Kd(E),Ms()},ge=(E,O,k,F,H,z,ae,Q,re=!1)=>{const X=E&&E.children,Ie=E?E.shapeFlag:0,le=O.children,{patchFlag:we,shapeFlag:Ne}=O;if(we>0){if(we&128){_e(X,le,k,F,H,z,ae,Q,re);return}else if(we&256){ee(X,le,k,F,H,z,ae,Q,re);return}}Ne&8?(Ie&16&&V(X,H,z),le!==X&&h(k,le)):Ie&16?Ne&16?_e(X,le,k,F,H,z,ae,Q,re):V(X,H,z,!0):(Ie&8&&h(k,""),Ne&16&&de(le,k,F,H,z,ae,Q,re))},ee=(E,O,k,F,H,z,ae,Q,re)=>{E=E||Mn,O=O||Mn;const X=E.length,Ie=O.length,le=Math.min(X,Ie);let we;for(we=0;we<le;we++){const Ne=O[we]=re?Br(O[we]):Ls(O[we]);A(E[we],Ne,k,null,H,z,ae,Q,re)}X>Ie?V(E,H,z,!0,!1,le):de(O,k,F,H,z,ae,Q,re,le)},_e=(E,O,k,F,H,z,ae,Q,re)=>{let X=0;const Ie=O.length;let le=E.length-1,we=Ie-1;for(;X<=le&&X<=we;){const Ne=E[X],He=O[X]=re?Br(O[X]):Ls(O[X]);if(_n(Ne,He))A(Ne,He,k,null,H,z,ae,Q,re);else break;X++}for(;X<=le&&X<=we;){const Ne=E[le],He=O[we]=re?Br(O[we]):Ls(O[we]);if(_n(Ne,He))A(Ne,He,k,null,H,z,ae,Q,re);else break;le--,we--}if(X>le){if(X<=we){const Ne=we+1,He=Ne<Ie?O[Ne].el:F;for(;X<=we;)A(null,O[X]=re?Br(O[X]):Ls(O[X]),k,He,H,z,ae,Q,re),X++}}else if(X>we)for(;X<=le;)Ye(E[X],H,z,!0),X++;else{const Ne=X,He=X,it=new Map;for(X=He;X<=we;X++){const kt=O[X]=re?Br(O[X]):Ls(O[X]);kt.key!=null&&({}.NODE_ENV!=="production"&&it.has(kt.key)&&Z("Duplicate keys found during update:",JSON.stringify(kt.key),"Make sure keys are unique."),it.set(kt.key,X))}let st,Ft=0;const Dt=we-He+1;let ss=!1,qt=0;const xr=new Array(Dt);for(X=0;X<Dt;X++)xr[X]=0;for(X=Ne;X<=le;X++){const kt=E[X];if(Ft>=Dt){Ye(kt,H,z,!0);continue}let Es;if(kt.key!=null)Es=it.get(kt.key);else for(st=He;st<=we;st++)if(xr[st-He]===0&&_n(kt,O[st])){Es=st;break}Es===void 0?Ye(kt,H,z,!0):(xr[Es-He]=X+1,Es>=qt?qt=Es:ss=!0,A(kt,O[Es],k,null,H,z,ae,Q,re),Ft++)}const eo=ss?Yy(xr):Mn;for(st=eo.length-1,X=Dt-1;X>=0;X--){const kt=He+X,Es=O[kt],Ia=kt+1<Ie?O[kt+1].el:F;xr[X]===0?A(null,Es,k,Ia,H,z,ae,Q,re):ss&&(st<0||X!==eo[st]?Se(Es,k,Ia,2):st--)}}},Se=(E,O,k,F,H=null)=>{const{el:z,type:ae,transition:Q,children:re,shapeFlag:X}=E;if(X&6){Se(E.component.subTree,O,k,F);return}if(X&128){E.suspense.move(O,k,F);return}if(X&64){ae.move(E,O,k,Ue);return}if(ae===je){i(z,O,k);for(let le=0;le<re.length;le++)Se(re[le],O,k,F);i(E.anchor,O,k);return}if(ae===$o){ye(E,O,k);return}if(F!==2&&X&1&&Q)if(F===0)Q.beforeEnter(z),i(z,O,k),Zt(()=>Q.enter(z),H);else{const{leave:le,delayLeave:we,afterLeave:Ne}=Q,He=()=>{E.ctx.isUnmounted?n(z):i(z,O,k)},it=()=>{le(z,()=>{He(),Ne&&Ne()})};we?we(z,He,it):it()}else i(z,O,k)},Ye=(E,O,k,F=!1,H=!1)=>{const{type:z,props:ae,ref:Q,children:re,dynamicChildren:X,shapeFlag:Ie,patchFlag:le,dirs:we,cacheIndex:Ne}=E;if(le===-2&&(H=!1),Q!=null&&(Ps(),Ro(Q,null,k,E,!0),Ms()),Ne!=null&&(O.renderCache[Ne]=void 0),Ie&256){O.ctx.deactivate(E);return}const He=Ie&1&&we,it=!Bn(E);let st;if(it&&(st=ae&&ae.onVnodeBeforeUnmount)&&Zs(st,O,E),Ie&6)Mt(E.component,k,F);else{if(Ie&128){E.suspense.unmount(k,F);return}He&&fn(E,null,O,"beforeUnmount"),Ie&64?E.type.remove(E,O,k,Ue,F):X&&!X.hasOnce&&(z!==je||le>0&&le&64)?V(X,O,k,!1,!0):(z===je&&le&384||!H&&Ie&16)&&V(re,O,k),F&&yt(E)}(it&&(st=ae&&ae.onVnodeUnmounted)||He)&&Zt(()=>{st&&Zs(st,O,E),He&&fn(E,null,O,"unmounted")},k)},yt=E=>{const{type:O,el:k,anchor:F,transition:H}=E;if(O===je){({}).NODE_ENV!=="production"&&E.patchFlag>0&&E.patchFlag&2048&&H&&!H.persisted?E.children.forEach(ae=>{ae.type===Ot?n(ae.el):yt(ae)}):ot(k,F);return}if(O===$o){se(E);return}const z=()=>{n(k),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(E.shapeFlag&1&&H&&!H.persisted){const{leave:ae,delayLeave:Q}=H,re=()=>ae(k,z);Q?Q(E.el,z,re):re()}else z()},ot=(E,O)=>{let k;for(;E!==O;)k=v(E),n(E),E=k;n(O)},Mt=(E,O,k)=>{({}).NODE_ENV!=="production"&&E.type.__hmrId&&zv(E);const{bum:F,scope:H,job:z,subTree:ae,um:Q,m:re,a:X,parent:Ie,slots:{__:le}}=E;jf(re),jf(X),F&&Vn(F),Ie&&Ee(le)&&le.forEach(we=>{Ie.renderCache[we]=void 0}),H.stop(),z&&(z.flags|=8,Ye(ae,E,O,k)),Q&&Zt(Q,O),Zt(()=>{E.isUnmounted=!0},O),O&&O.pendingBranch&&!O.isUnmounted&&E.asyncDep&&!E.asyncResolved&&E.suspenseId===O.pendingId&&(O.deps--,O.deps===0&&O.resolve()),{}.NODE_ENV!=="production"&&ey(E)},V=(E,O,k,F=!1,H=!1,z=0)=>{for(let ae=z;ae<E.length;ae++)Ye(E[ae],O,k,F,H)},fe=E=>{if(E.shapeFlag&6)return fe(E.component.subTree);if(E.shapeFlag&128)return E.suspense.next();const O=v(E.anchor||E.el),k=O&&O[nf];return k?v(k):O};let ce=!1;const Ce=(E,O,k)=>{E==null?O._vnode&&Ye(O._vnode,null,null,!0):A(O._vnode||null,E,O,null,null,null,k),O._vnode=E,ce||(ce=!0,Kd(),Xd(),ce=!1)},Ue={p:A,um:Ye,m:Se,r:yt,mt:Y,mc:de,pc:ge,pbc:$e,n:fe,o:e};let ft,Be;return t&&([ft,Be]=t(Ue)),{render:Ce,hydrate:ft,createApp:Ry(Ce,ft)}}function tu({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function mn({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Xy(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Vo(e,t,s=!1){const i=e.children,n=t.children;if(Ee(i)&&Ee(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Br(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&Vo(u,c)),c.type===Lo&&(c.el=u.el),c.type===Ot&&!c.el&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.el&&(c.el.__vnode=c)}}function Yy(e){const t=e.slice(),s=[0];let i,n,a,u,c;const f=e.length;for(i=0;i<f;i++){const g=e[i];if(g!==0){if(n=s[s.length-1],e[n]<g){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<g?a=c+1:u=c;g<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function Wf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Wf(t)}function jf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jy=Symbol.for("v-scx"),Qy=()=>{{const e=hr(Jy);return e||{}.NODE_ENV!=="production"&&Z("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Zy(e,t){return su(e,null,t)}function Hn(e,t,s){return{}.NODE_ENV!=="production"&&!Ae(t)&&Z("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),su(e,t,s)}function su(e,t,s=lt){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Z('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Z('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Z('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=vt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Z);const f=t&&i||!t&&a!=="post";let g;if(qo){if(a==="sync"){const C=Qy();g=C.__watcherHandles||(C.__watcherHandles=[])}else if(!f){const C=()=>{};return C.stop=Nt,C.resume=Nt,C.pause=Nt,C}}const h=Pt;c.call=(C,P,A)=>ks(C,h,P,A);let p=!1;a==="post"?c.scheduler=C=>{Zt(C,h&&h.suspense)}:a!=="sync"&&(p=!0,c.scheduler=(C,P)=>{P?C():ki(C)}),c.augmentJob=C=>{t&&(C.flags|=4),p&&(C.flags|=2,h&&(C.id=h.uid,C.i=h))};const v=Lv(e,t,c);return qo&&(g?g.push(v):f&&v()),v}function eb(e,t,s){const i=this.proxy,n=gt(e)?e.includes(".")?Gf(i,e):()=>i[e]:e.bind(i,i);let a;Ae(t)?a=t:(a=t.handler,s=t);const u=Bo(this),c=su(n,a.bind(i),s);return u(),c}function Gf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const tb=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${kr(t)}Modifiers`];function sb(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||lt;if({}.NODE_ENV!=="production"){const{emitsOptions:h,propsOptions:[p]}=e;if(h)if(!(t in h))(!p||!(on(Kt(t))in p))&&Z(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${on(Kt(t))}" prop.`);else{const v=h[t];Ae(v)&&(v(...s)||Z(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&tb(i,t.slice(7));if(u&&(u.trim&&(n=s.map(h=>gt(h)?h.trim():h)),u.number&&(n=s.map(Ei))),{}.NODE_ENV!=="production"&&ry(e,t,n),{}.NODE_ENV!=="production"){const h=t.toLowerCase();h!==t&&i[on(h)]&&Z(`Event "${h}" is emitted in component ${Qi(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${kr(t)}" instead of "${t}".`)}let c,f=i[c=on(t)]||i[c=on(Kt(t))];!f&&a&&(f=i[c=on(kr(t))]),f&&ks(f,e,6,n);const g=i[c+"Once"];if(g){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,ks(g,e,6,n)}}function zf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!Ae(e)){const f=g=>{const h=zf(g,t,!0);h&&(c=!0,vt(u,h))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!a&&!c?(rt(e)&&i.set(e,null),null):(Ee(a)?a.forEach(f=>u[f]=null):vt(u,a),rt(e)&&i.set(e,u),u)}function Gi(e,t){return!e||!go(t)?!1:(t=t.slice(2).replace(/Once$/,""),tt(e,t[0].toLowerCase()+t.slice(1))||tt(e,kr(t))||tt(e,t))}let ru=!1;function zi(){ru=!0}function nu(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:f,render:g,renderCache:h,props:p,data:v,setupState:C,ctx:P,inheritAttrs:A}=e,oe=$i(e);let W,ie;({}).NODE_ENV!=="production"&&(ru=!1);try{if(s.shapeFlag&4){const se=n||i,be={}.NODE_ENV!=="production"&&C.__isScriptSetup?new Proxy(se,{get(xe,ke,de){return Z(`Property '${String(ke)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(xe,ke,de)}}):se;W=Ls(g.call(be,se,h,{}.NODE_ENV!=="production"?Ks(p):p,C,v,P)),ie=c}else{const se=t;({}).NODE_ENV!=="production"&&c===p&&zi(),W=Ls(se.length>1?se({}.NODE_ENV!=="production"?Ks(p):p,{}.NODE_ENV!=="production"?{get attrs(){return zi(),Ks(c)},slots:u,emit:f}:{attrs:c,slots:u,emit:f}):se({}.NODE_ENV!=="production"?Ks(p):p,null)),ie=t.props?c:rb(c)}}catch(se){Fo.length=0,Io(se,e,1),W=x(Ot)}let q=W,ye;if({}.NODE_ENV!=="production"&&W.patchFlag>0&&W.patchFlag&2048&&([q,ye]=Kf(W)),ie&&A!==!1){const se=Object.keys(ie),{shapeFlag:be}=q;if(se.length){if(be&7)a&&se.some(yi)&&(ie=nb(ie,a)),q=Qs(q,ie,!1,!0);else if({}.NODE_ENV!=="production"&&!ru&&q.type!==Ot){const xe=Object.keys(c),ke=[],de=[];for(let De=0,$e=xe.length;De<$e;De++){const ue=xe[De];go(ue)?yi(ue)||ke.push(ue[2].toLowerCase()+ue.slice(3)):de.push(ue)}de.length&&Z(`Extraneous non-props attributes (${de.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),ke.length&&Z(`Extraneous non-emits event listeners (${ke.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!Xf(q)&&Z("Runtime directive used on component with non-element root node. The directives will not function as intended."),q=Qs(q,null,!1,!0),q.dirs=q.dirs?q.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!Xf(q)&&Z("Component inside <Transition> renders non-element root node that cannot be animated."),Ao(q,s.transition)),{}.NODE_ENV!=="production"&&ye?ye(q):W=q,$i(oe),W}const Kf=e=>{const t=e.children,s=e.dynamicChildren,i=ou(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return Kf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Ls(i),u]};function ou(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(gn(n)){if(n.type!==Ot||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return ou(s.children)}}else return}return s}const rb=e=>{let t;for(const s in e)(s==="class"||s==="style"||go(s))&&((t||(t={}))[s]=e[s]);return t},nb=(e,t)=>{const s={};for(const i in e)(!yi(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},Xf=e=>e.shapeFlag&7||e.type===Ot;function ob(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:f}=t,g=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&Vs||t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return i?Yf(i,u,g):!!u;if(f&8){const h=t.dynamicProps;for(let p=0;p<h.length;p++){const v=h[p];if(u[v]!==i[v]&&!Gi(g,v))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?Yf(i,u,g):!0:!!u;return!1}function Yf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Gi(s,a))return!0}return!1}function ib({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const Jf=e=>e.__isSuspense;function ab(e,t){t&&t.pendingBranch?Ee(e)?t.effects.push(...e):t.effects.push(e):zd(e)}const je=Symbol.for("v-fgt"),Lo=Symbol.for("v-txt"),Ot=Symbol.for("v-cmt"),$o=Symbol.for("v-stc"),Fo=[];let ds=null;function S(e=!1){Fo.push(ds=e?null:[])}function lb(){Fo.pop(),ds=Fo[Fo.length-1]||null}let Uo=1;function Qf(e,t=!1){Uo+=e,e<0&&ds&&t&&(ds.hasOnce=!0)}function Zf(e){return e.dynamicChildren=Uo>0?ds||Mn:null,lb(),Uo>0&&ds&&ds.push(e),e}function N(e,t,s,i,n,a){return Zf(m(e,t,s,i,n,a,!0))}function ct(e,t,s,i,n){return Zf(x(e,t,s,i,n,!0))}function gn(e){return e?e.__v_isVNode===!0:!1}function _n(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=Vi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const ub=(...e)=>th(...e),eh=({key:e})=>e??null,Ki=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?gt(e)||It(e)||Ae(e)?{i:wt,r:e,k:t,f:!!s}:e:null);function m(e,t=null,s=null,i=0,n=null,a=e===je?0:1,u=!1,c=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&eh(t),ref:t&&Ki(t),scopeId:sf,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:wt};return c?(au(f,s),a&128&&e.normalize(f)):s&&(f.shapeFlag|=gt(s)?8:16),{}.NODE_ENV!=="production"&&f.key!==f.key&&Z("VNode created with invalid key (NaN). VNode type:",f.type),Uo>0&&!u&&ds&&(f.patchFlag>0||a&6)&&f.patchFlag!==32&&ds.push(f),f}const x={}.NODE_ENV!=="production"?ub:th;function th(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===by)&&({}.NODE_ENV!=="production"&&!e&&Z(`Invalid vnode type when creating vnode: ${e}.`),e=Ot),gn(e)){const c=Qs(e,t,!0);return s&&au(c,s),Uo>0&&!a&&ds&&(c.shapeFlag&6?ds[ds.indexOf(e)]=c:ds.push(c)),c.patchFlag=-2,c}if(lh(e)&&(e=e.__vccOpts),t){t=cb(t);let{class:c,style:f}=t;c&&!gt(c)&&(t.class=me(c)),rt(f)&&(Di(f)&&!Ee(f)&&(f=vt({},f)),t.style=As(f))}const u=gt(e)?1:Jf(e)?128:of(e)?64:rt(e)?4:Ae(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&Di(e)&&(e=Ve(e),Z("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),m(e,t,s,i,n,u,a,!0)}function cb(e){return e?Di(e)||kf(e)?vt({},e):e:null}function Qs(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:f}=e,g=t?db(n||{},t):n,h={__v_isVNode:!0,__v_skip:!0,type:e.type,props:g,key:g&&eh(g),ref:t&&t.ref?s&&a?Ee(a)?a.concat(Ki(t)):[a,Ki(t)]:Ki(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&Ee(c)?c.map(sh):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==je?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qs(e.ssContent),ssFallback:e.ssFallback&&Qs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&i&&Ao(h,f.clone(h)),h}function sh(e){const t=Qs(e);return Ee(e.children)&&(t.children=e.children.map(sh)),t}function Ze(e=" ",t=0){return x(Lo,null,e,t)}function iu(e,t){const s=x($o,null,e);return s.staticCount=t,s}function te(e="",t=!1){return t?(S(),ct(Ot,null,e)):x(Ot,null,e)}function Ls(e){return e==null||typeof e=="boolean"?x(Ot):Ee(e)?x(je,null,e.slice()):gn(e)?Br(e):x(Lo,null,String(e))}function Br(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qs(e)}function au(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(Ee(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),au(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!kf(t)?t._ctx=wt:n===3&&wt&&(wt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Ae(t)?(t={default:t,_ctx:wt},s=32):(t=String(t),i&64?(s=16,t=[Ze(t)]):s=8);e.children=t,e.shapeFlag|=s}function db(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=me([t.class,i.class]));else if(n==="style")t.style=As([t.style,i.style]);else if(go(n)){const a=t[n],u=i[n];u&&a!==u&&!(Ee(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Zs(e,t,s,i=null){ks(e,t,7,[s,i])}const fb=Rf();let hb=0;function pb(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||fb,a={uid:hb++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new yd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Lf(i,n),emitsOptions:zf(i,n),emit:null,emitted:null,propsDefaults:lt,inheritAttrs:i.inheritAttrs,ctx:lt,data:lt,props:lt,attrs:lt,slots:lt,refs:lt,setupState:lt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=Cy(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=sb.bind(null,a),e.ce&&e.ce(a),a}let Pt=null;const Xi=()=>Pt||wt;let Yi,lu;{const e=bo(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Yi=t("__VUE_INSTANCE_SETTERS__",s=>Pt=s),lu=t("__VUE_SSR_SETTERS__",s=>qo=s)}const Bo=e=>{const t=Pt;return Yi(e),e.scope.on(),()=>{e.scope.off(),Yi(t)}},rh=()=>{Pt&&Pt.scope.off(),Yi(null)},mb=ur("slot,component");function uu(e,{isNativeTag:t}){(mb(e)||t(e))&&Z("Do not use built-in or reserved HTML elements as component id: "+e)}function nh(e){return e.vnode.shapeFlag&4}let qo=!1;function gb(e,t=!1,s=!1){t&&lu(t);const{props:i,children:n}=e.vnode,a=nh(e);Py(e,i,a,t),Wy(e,n,s||t);const u=a?_b(e,t):void 0;return t&&lu(!1),u}function _b(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&uu(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)uu(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)rf(a[u])}i.compilerOptions&&vb()&&Z('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Sf),{}.NODE_ENV!=="production"&&wy(e);const{setup:n}=i;if(n){Ps();const a=e.setupContext=n.length>1?bb(e):null,u=Bo(e),c=$n(n,e,0,[{}.NODE_ENV!=="production"?Ks(e.props):e.props,a]),f=_l(c);if(Ms(),u(),(f||e.sp)&&!Bn(e)&&yf(e),f){if(c.then(rh,rh),t)return c.then(g=>{oh(e,g,t)}).catch(g=>{Io(g,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const g=(s=i.name)!=null?s:"Anonymous";Z(`Component <${g}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else oh(e,c,t)}else ih(e,t)}function oh(e,t,s){Ae(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:rt(t)?({}.NODE_ENV!=="production"&&gn(t)&&Z("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=Hd(t),{}.NODE_ENV!=="production"&&Oy(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Z(`setup() should return an object. Received: ${t===null?"null":typeof t}`),ih(e,s)}let cu;const vb=()=>!cu;function ih(e,t,s){const i=e.type;if(!e.render){if(!t&&cu&&!i.render){const n=i.template||Xl(e).template;if(n){({}).NODE_ENV!=="production"&&pr(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:f}=i,g=vt(vt({isCustomElement:a,delimiters:c},u),f);i.render=cu(n,g),{}.NODE_ENV!=="production"&&mr(e,"compile")}}e.render=i.render||Nt}{const n=Bo(e);Ps();try{Sy(e)}finally{Ms(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===Nt&&!t&&(i.template?Z('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Z("Component is missing template or render function: ",i))}const ah={}.NODE_ENV!=="production"?{get(e,t){return zi(),Tt(e,"get",""),e[t]},set(){return Z("setupContext.attrs is readonly."),!1},deleteProperty(){return Z("setupContext.attrs is readonly."),!1}}:{get(e,t){return Tt(e,"get",""),e[t]}};function yb(e){return new Proxy(e.slots,{get(t,s){return Tt(e,"get","$slots"),t[s]}})}function bb(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Z("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(Ee(s)?i="array":It(s)&&(i="ref")),i!=="object"&&Z(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,ah))},get slots(){return i||(i=yb(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,ah),slots:e.slots,emit:e.emit,expose:t}}function Ji(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Hd(Pl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in pn)return pn[s](e)},has(t,s){return s in t||s in pn}})):e.proxy}const Eb=/(?:^|[-_])(\w)/g,Cb=e=>e.replace(Eb,t=>t.toUpperCase()).replace(/[-_]/g,"");function du(e,t=!0){return Ae(e)?e.displayName||e.name:e.name||t&&e.__name}function Qi(e,t,s=!1){let i=du(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?Cb(i):s?"App":"Anonymous"}function lh(e){return Ae(e)&&"__vccOpts"in e}const $s=(e,t)=>{const s=kv(e,t,qo);if({}.NODE_ENV!=="production"){const i=Xi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function fu(e,t,s){const i=arguments.length;return i===2?rt(t)&&!Ee(t)?gn(t)?x(e,null,[t]):x(e,t):x(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&gn(s)&&(s=[s]),x(e,t,s))}function wb(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(p){if(!rt(p))return null;if(p.__isVue)return["div",e,"VueInstance"];if(It(p)){Ps();const v=p.value;return Ms(),["div",{},["span",e,h(p)],"<",c(v),">"]}else{if(ln(p))return["div",{},["span",e,Xt(p)?"ShallowReactive":"Reactive"],"<",c(p),`>${Xs(p)?" (readonly)":""}`];if(Xs(p))return["div",{},["span",e,Xt(p)?"ShallowReadonly":"Readonly"],"<",c(p),">"]}return null},hasBody(p){return p&&p.__isVue},body(p){if(p&&p.__isVue)return["div",{},...a(p.$)]}};function a(p){const v=[];p.type.props&&p.props&&v.push(u("props",Ve(p.props))),p.setupState!==lt&&v.push(u("setup",p.setupState)),p.data!==lt&&v.push(u("data",Ve(p.data)));const C=f(p,"computed");C&&v.push(u("computed",C));const P=f(p,"inject");return P&&v.push(u("injected",P)),v.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:p}]]),v}function u(p,v){return v=vt({},v),Object.keys(v).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},p],["div",{style:"padding-left:1.25em"},...Object.keys(v).map(C=>["div",{},["span",i,C+": "],c(v[C],!1)])]]:["span",{}]}function c(p,v=!0){return typeof p=="number"?["span",t,p]:typeof p=="string"?["span",s,JSON.stringify(p)]:typeof p=="boolean"?["span",i,p]:rt(p)?["object",{object:v?Ve(p):p}]:["span",s,String(p)]}function f(p,v){const C=p.type;if(Ae(C))return;const P={};for(const A in p.ctx)g(C,A,v)&&(P[A]=p.ctx[A]);return P}function g(p,v,C){const P=p[C];if(Ee(P)&&P.includes(v)||rt(P)&&v in P||p.extends&&g(p.extends,v,C)||p.mixins&&p.mixins.some(A=>g(A,v,C)))return!0}function h(p){return Xt(p)?"ShallowRef":p.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const uh="3.5.17",er={}.NODE_ENV!=="production"?Z:Nt;/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let hu;const ch=typeof window<"u"&&window.trustedTypes;if(ch)try{hu=ch.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&er(`Error creating trusted types policy: ${e}`)}const dh=hu?e=>hu.createHTML(e):e=>e,Ob="http://www.w3.org/2000/svg",xb="http://www.w3.org/1998/Math/MathML",gr=typeof document<"u"?document:null,fh=gr&&gr.createElement("template"),Sb={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?gr.createElementNS(Ob,e):t==="mathml"?gr.createElementNS(xb,e):s?gr.createElement(e,{is:s}):gr.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>gr.createTextNode(e),createComment:e=>gr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>gr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{fh.innerHTML=dh(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=fh.content;if(i==="svg"||i==="mathml"){const f=c.firstChild;for(;f.firstChild;)c.appendChild(f.firstChild);c.removeChild(f)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},qr="transition",Ho="animation",Wo=Symbol("_vtc"),hh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ib=vt({},ff,hh),ph=(e=>(e.displayName="Transition",e.props=Ib,e))((e,{slots:t})=>fu(ly,Db(e),t)),vn=(e,t=[])=>{Ee(e)?e.forEach(s=>s(...t)):e&&e(...t)},mh=e=>e?Ee(e)?e.some(t=>t.length>1):e.length>1:!1;function Db(e){const t={};for(const ue in e)ue in hh||(t[ue]=e[ue]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:f=a,appearActiveClass:g=u,appearToClass:h=c,leaveFromClass:p=`${s}-leave-from`,leaveActiveClass:v=`${s}-leave-active`,leaveToClass:C=`${s}-leave-to`}=e,P=Nb(n),A=P&&P[0],oe=P&&P[1],{onBeforeEnter:W,onEnter:ie,onEnterCancelled:q,onLeave:ye,onLeaveCancelled:se,onBeforeAppear:be=W,onAppear:xe=ie,onAppearCancelled:ke=q}=t,de=(ue,R,G,Y)=>{ue._enterCancelled=Y,yn(ue,R?h:c),yn(ue,R?g:u),G&&G()},De=(ue,R)=>{ue._isLeaving=!1,yn(ue,p),yn(ue,C),yn(ue,v),R&&R()},$e=ue=>(R,G)=>{const Y=ue?xe:ie,pe=()=>de(R,ue,G);vn(Y,[R,pe]),gh(()=>{yn(R,ue?f:a),_r(R,ue?h:c),mh(Y)||_h(R,i,A,pe)})};return vt(t,{onBeforeEnter(ue){vn(W,[ue]),_r(ue,a),_r(ue,u)},onBeforeAppear(ue){vn(be,[ue]),_r(ue,f),_r(ue,g)},onEnter:$e(!1),onAppear:$e(!0),onLeave(ue,R){ue._isLeaving=!0;const G=()=>De(ue,R);_r(ue,p),ue._enterCancelled?(_r(ue,v),bh()):(bh(),_r(ue,v)),gh(()=>{ue._isLeaving&&(yn(ue,p),_r(ue,C),mh(ye)||_h(ue,i,oe,G))}),vn(ye,[ue,G])},onEnterCancelled(ue){de(ue,!1,void 0,!0),vn(q,[ue])},onAppearCancelled(ue){de(ue,!0,void 0,!0),vn(ke,[ue])},onLeaveCancelled(ue){De(ue),vn(se,[ue])}})}function Nb(e){if(e==null)return null;if(rt(e))return[pu(e.enter),pu(e.leave)];{const t=pu(e);return[t,t]}}function pu(e){const t=Q_(e);return{}.NODE_ENV!=="production"&&qv(t,"<transition> explicit duration"),t}function _r(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[Wo]||(e[Wo]=new Set)).add(t)}function yn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[Wo];s&&(s.delete(t),s.size||(e[Wo]=void 0))}function gh(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Tb=0;function _h(e,t,s,i){const n=e._endId=++Tb,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:f}=Ab(e,t);if(!u)return i();const g=u+"end";let h=0;const p=()=>{e.removeEventListener(g,v),a()},v=C=>{C.target===e&&++h>=f&&p()};setTimeout(()=>{h<f&&p()},c+1),e.addEventListener(g,v)}function Ab(e,t){const s=window.getComputedStyle(e),i=P=>(s[P]||"").split(", "),n=i(`${qr}Delay`),a=i(`${qr}Duration`),u=vh(n,a),c=i(`${Ho}Delay`),f=i(`${Ho}Duration`),g=vh(c,f);let h=null,p=0,v=0;t===qr?u>0&&(h=qr,p=u,v=a.length):t===Ho?g>0&&(h=Ho,p=g,v=f.length):(p=Math.max(u,g),h=p>0?u>g?qr:Ho:null,v=h?h===qr?a.length:f.length:0);const C=h===qr&&/\b(transform|all)(,|$)/.test(i(`${qr}Property`).toString());return{type:h,timeout:p,propCount:v,hasTransform:C}}function vh(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>yh(s)+yh(e[i])))}function yh(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function bh(){return document.body.offsetHeight}function Rb(e,t,s){const i=e[Wo];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Zi=Symbol("_vod"),Eh=Symbol("_vsh"),mu={beforeMount(e,{value:t},{transition:s}){e[Zi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):jo(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),jo(e,!0),i.enter(e)):i.leave(e,()=>{jo(e,!1)}):jo(e,t))},beforeUnmount(e,{value:t}){jo(e,t)}};({}).NODE_ENV!=="production"&&(mu.name="show");function jo(e,t){e.style.display=t?e[Zi]:"none",e[Eh]=!t}const Pb=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Mb=/(^|;)\s*display\s*:/;function kb(e,t,s){const i=e.style,n=gt(s);let a=!1;if(s&&!n){if(t)if(gt(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&ea(i,c,"")}else for(const u in t)s[u]==null&&ea(i,u,"");for(const u in s)u==="display"&&(a=!0),ea(i,u,s[u])}else if(n){if(t!==s){const u=i[Pb];u&&(s+=";"+u),i.cssText=s,a=Mb.test(s)}}else t&&e.removeAttribute("style");Zi in e&&(e[Zi]=a?i.display:"",e[Eh]&&(i.display="none"))}const Vb=/[^\\];\s*$/,Ch=/\s*!important$/;function ea(e,t,s){if(Ee(s))s.forEach(i=>ea(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Vb.test(s)&&er(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Lb(e,t);Ch.test(s)?e.setProperty(kr(i),s.replace(Ch,""),"important"):e[i]=s}}const wh=["Webkit","Moz","ms"],gu={};function Lb(e,t){const s=gu[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return gu[t]=i;i=nn(i);for(let n=0;n<wh.length;n++){const a=wh[n]+i;if(a in e)return gu[t]=a}return t}const Oh="http://www.w3.org/1999/xlink";function xh(e,t,s,i,n,a=uv(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Oh,t.slice(6,t.length)):e.setAttributeNS(Oh,t,s):s==null||a&&!gd(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Ts(s)?String(s):s)}function Sh(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?dh(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(c!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=gd(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&er(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function Hr(e,t,s,i){e.addEventListener(t,s,i)}function $b(e,t,s,i){e.removeEventListener(t,s,i)}const Ih=Symbol("_vei");function Fb(e,t,s,i,n=null){const a=e[Ih]||(e[Ih]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Nh(i,t):i;else{const[c,f]=Ub(t);if(i){const g=a[t]=Hb({}.NODE_ENV!=="production"?Nh(i,t):i,n);Hr(e,c,g,f)}else u&&($b(e,c,u,f),a[t]=void 0)}}const Dh=/(?:Once|Passive|Capture)$/;function Ub(e){let t;if(Dh.test(e)){t={};let i;for(;i=e.match(Dh);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):kr(e.slice(2)),t]}let _u=0;const Bb=Promise.resolve(),qb=()=>_u||(Bb.then(()=>_u=0),_u=Date.now());function Hb(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;ks(Wb(i,s.value),t,5,[i])};return s.value=e,s.attached=qb(),s}function Nh(e,t){return Ae(e)||Ee(e)?e:(er(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),Nt)}function Wb(e,t){if(Ee(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Th=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,jb=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Rb(e,i,u):t==="style"?kb(e,s,i):go(t)?yi(t)||Fb(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Gb(e,t,i,u))?(Sh(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&xh(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!gt(i))?Sh(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),xh(e,t,i,u))};function Gb(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Th(t)&&Ae(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Th(t)&&gt(s)?!1:t in e}const Wn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Ee(t)?s=>Vn(t,s):t};function zb(e){e.target.composing=!0}function Ah(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const vr=Symbol("_assign"),yr={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[vr]=Wn(n);const a=i||n.props&&n.props.type==="number";Hr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=Ei(c)),e[vr](c)}),s&&Hr(e,"change",()=>{e.value=e.value.trim()}),t||(Hr(e,"compositionstart",zb),Hr(e,"compositionend",Ah),Hr(e,"change",Ah))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[vr]=Wn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?Ei(e.value):e.value,f=t??"";c!==f&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===f)||(e.value=f))}},Rh={deep:!0,created(e,t,s){e[vr]=Wn(s),Hr(e,"change",()=>{const i=e._modelValue,n=Go(e),a=e.checked,u=e[vr];if(Ee(i)){const c=bl(i,n),f=c!==-1;if(a&&!f)u(i.concat(n));else if(!a&&f){const g=[...i];g.splice(c,1),u(g)}}else if(kn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(Vh(e,a))})},mounted:Ph,beforeUpdate(e,t,s){e[vr]=Wn(s),Ph(e,t,s)}};function Ph(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(Ee(t))n=bl(t,i.props.value)>-1;else if(kn(t))n=t.has(i.props.value);else{if(t===s)return;n=Eo(t,Vh(e,!0))}e.checked!==n&&(e.checked=n)}const Mh={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=kn(t);Hr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?Ei(Go(u)):Go(u));e[vr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,Vl(()=>{e._assigning=!1})}),e[vr]=Wn(i)},mounted(e,{value:t}){kh(e,t)},beforeUpdate(e,t,s){e[vr]=Wn(s)},updated(e,{value:t}){e._assigning||kh(e,t)}};function kh(e,t){const s=e.multiple,i=Ee(t);if(s&&!i&&!kn(t)){({}).NODE_ENV!=="production"&&er(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Go(u);if(s)if(i){const f=typeof c;f==="string"||f==="number"?u.selected=t.some(g=>String(g)===String(c)):u.selected=bl(t,c)>-1}else u.selected=t.has(c);else if(Eo(Go(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Go(e){return"_value"in e?e._value:e.value}function Vh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Kb=["ctrl","shift","alt","meta"],Xb={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Kb.some(s=>e[`${s}Key`]&&!t.includes(s))},es=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=Xb[t[u]];if(c&&c(n,t))return}return e(n,...a)})},Yb=vt({patchProp:jb},Sb);let Lh;function Jb(){return Lh||(Lh=zy(Yb))}const Qb=(...e)=>{const t=Jb().createApp(...e);({}).NODE_ENV!=="production"&&(eE(t),tE(t));const{mount:s}=t;return t.mount=i=>{const n=sE(i);if(!n)return;const a=t._component;!Ae(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Zb(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Zb(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function eE(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>iv(t)||av(t)||lv(t),writable:!1})}function tE(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){er("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return er(i),s},set(){er(i)}})}}function sE(e){if(gt(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&er(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&er('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function rE(){wb()}({}).NODE_ENV!=="production"&&rE();var nE=!1;function oE(){return $h().__VUE_DEVTOOLS_GLOBAL_HOOK__}function $h(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const iE=typeof Proxy=="function",aE="devtools-plugin:setup",lE="plugin:settings:set";let jn,vu;function uE(){var e;return jn!==void 0||(typeof window<"u"&&window.performance?(jn=!0,vu=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(jn=!0,vu=globalThis.perf_hooks.performance):jn=!1),jn}function cE(){return uE()?vu.now():Date.now()}class dE{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return cE()}},s&&s.on(lE,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...f)=>{this.onQueue.push({method:c,args:f})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...f)=>(this.targetQueue.push({method:c,args:f,resolve:()=>{}}),this.fallbacks[c](...f)):(...f)=>new Promise(g=>{this.targetQueue.push({method:c,args:f,resolve:g})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function yu(e,t){const s=e,i=$h(),n=oE(),a=iE&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(aE,e,t);else{const u=a?new dE(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const fE={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var bn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(bn||(bn={}));const bu=typeof window<"u",Fh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function hE(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function Eu(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){qh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function Uh(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ta(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const sa=typeof navigator=="object"?navigator:{userAgent:""},Bh=(()=>/Macintosh/.test(sa.userAgent)&&/AppleWebKit/.test(sa.userAgent)&&!/Safari/.test(sa.userAgent))(),qh=bu?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!Bh?pE:"msSaveOrOpenBlob"in sa?mE:gE:()=>{};function pE(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?Uh(i.href)?Eu(e,t,s):(i.target="_blank",ta(i)):ta(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ta(i)},0))}function mE(e,t="download",s){if(typeof e=="string")if(Uh(e))Eu(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ta(i)})}else navigator.msSaveOrOpenBlob(hE(e,s),t)}function gE(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return Eu(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(Fh.HTMLElement))||"safari"in Fh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||Bh)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let f=c.result;if(typeof f!="string")throw i=null,new Error("Wrong reader.result type");f=u?f:f.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=f:location.assign(f),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function $t(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function Cu(e){return"_a"in e&&"install"in e}function Hh(){if(!("clipboard"in navigator))return $t("Your browser doesn't support the Clipboard API","error"),!0}function Wh(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?($t('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function _E(e){if(!Hh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),$t("Global state copied to clipboard.")}catch(t){if(Wh(t))return;$t("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function vE(e){if(!Hh())try{jh(e,JSON.parse(await navigator.clipboard.readText())),$t("Global state pasted from clipboard.")}catch(t){if(Wh(t))return;$t("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function yE(e){try{qh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){$t("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let br;function bE(){br||(br=document.createElement("input"),br.type="file",br.accept=".json");function e(){return new Promise((t,s)=>{br.onchange=async()=>{const i=br.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},br.oncancel=()=>t(null),br.onerror=s,br.click()})}return e}async function EE(e){try{const s=await bE()();if(!s)return;const{text:i,file:n}=s;jh(e,JSON.parse(i)),$t(`Global state imported from "${n.name}".`)}catch(t){$t("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function jh(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Fs(e){return{_custom:{display:e}}}const Gh="🍍 Pinia (root)",ra="_root";function CE(e){return Cu(e)?{id:ra,label:Gh}:{id:e.$id,label:e.$id}}function wE(e){if(Cu(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,f)=>(c[f]=u[f],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function OE(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Fs(e.type),key:Fs(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function xE(e){switch(e){case bn.direct:return"mutation";case bn.patchFunction:return"$patch";case bn.patchObject:return"$patch";default:return"unknown"}}let Gn=!0;const na=[],En="pinia:mutations",jt="pinia",{assign:SE}=Object,oa=e=>"🍍 "+e;function IE(e,t){yu({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:na,app:e},s=>{typeof s.now!="function"&&$t("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:En,label:"Pinia 🍍",color:15064968}),s.addInspector({id:jt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{_E(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await vE(t),s.sendInspectorTree(jt),s.sendInspectorState(jt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{yE(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await EE(t),s.sendInspectorTree(jt),s.sendInspectorState(jt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?$t(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),$t(`Store "${i}" reset.`)):$t(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:oa(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Ve(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((f,g)=>(f[g]=c.$state[g],f),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:oa(c.$id),key:"getters",editable:!1,value:c._getters.reduce((f,g)=>{try{f[g]=c[g]}catch(h){f[g]=h}return f},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===jt){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):Gh.toLowerCase().includes(i.filter.toLowerCase())):n).map(CE)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===jt){const n=i.nodeId===ra?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==ra&&(globalThis.$store=Ve(n)),i.state=wE(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===jt){const a=i.nodeId===ra?t:t._s.get(i.nodeId);if(!a)return $t(`store "${i.nodeId}" not found`,"error");const{path:u}=i;Cu(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Gn=!1,i.set(a,u,i.state.value),Gn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return $t(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return $t(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Gn=!1,i.set(a,u,i.state.value),Gn=!0}})})}function DE(e,t){na.includes(oa(t.$id))||na.push(oa(t.$id)),yu({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:na,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:f,args:g})=>{const h=zh++;s.addTimelineEvent({layerId:En,event:{time:i(),title:"🛫 "+f,subtitle:"start",data:{store:Fs(t.$id),action:Fs(f),args:g},groupId:h}}),u(p=>{Wr=void 0,s.addTimelineEvent({layerId:En,event:{time:i(),title:"🛬 "+f,subtitle:"end",data:{store:Fs(t.$id),action:Fs(f),args:g,result:p},groupId:h}})}),c(p=>{Wr=void 0,s.addTimelineEvent({layerId:En,event:{time:i(),logType:"error",title:"💥 "+f,subtitle:"end",data:{store:Fs(t.$id),action:Fs(f),args:g,error:p},groupId:h}})})},!0),t._customProperties.forEach(u=>{Hn(()=>Lr(t[u]),(c,f)=>{s.notifyComponentUpdate(),s.sendInspectorState(jt),Gn&&s.addTimelineEvent({layerId:En,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:f},groupId:Wr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},f)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(jt),!Gn)return;const g={time:i(),title:xE(c),data:SE({store:Fs(t.$id)},OE(u)),groupId:Wr};c===bn.patchFunction?g.subtitle="⤵️":c===bn.patchObject?g.subtitle="🧩":u&&!Array.isArray(u)&&(g.subtitle=u.type),u&&(g.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:En,event:g})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=Pl(u=>{n(u),s.addTimelineEvent({layerId:En,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Fs(t.$id),info:Fs("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(jt),s.sendInspectorState(jt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(jt),s.sendInspectorState(jt),s.getSettings().logStoreChanges&&$t(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(jt),s.sendInspectorState(jt),s.getSettings().logStoreChanges&&$t(`"${t.$id}" store installed 🆕`)})}let zh=0,Wr;function Kh(e,t,s){const i=t.reduce((n,a)=>(n[a]=Ve(e)[a],n),{});for(const n in i)e[n]=function(){const a=zh,u=s?new Proxy(e,{get(...f){return Wr=a,Reflect.get(...f)},set(...f){return Wr=a,Reflect.set(...f)}}):e;Wr=a;const c=i[n].apply(u,arguments);return Wr=void 0,c}}function NE({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){Kh(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Ve(t)._hotUpdate=function(n){i.apply(this,arguments),Kh(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}DE(e,t)}}function TE(){const e=dv(!0),t=e.run(()=>Bd({}));let s=[],i=[];const n=Pl({install(a){n._a=a,a.provide(fE,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&bu&&IE(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!nE?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&bu&&typeof Proxy<"u"&&n.use(NE),n}const f3="",Le=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},AE={name:"App",mounted(){}},RE={id:"app"};function PE(e,t,s,i,n,a){const u=L("router-view");return S(),N("div",RE,[x(u)])}const ME=Le(AE,[["render",PE]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Er=typeof document<"u";function Xh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function kE(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Xh(e.default)}const nt=Object.assign;function wu(e,t){const s={};for(const i in t){const n=t[i];s[i]=fs(n)?n.map(e):e(n)}return s}const zo=()=>{},fs=Array.isArray;function ze(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const Yh=/#/g,VE=/&/g,LE=/\//g,$E=/=/g,FE=/\?/g,Jh=/\+/g,UE=/%5B/g,BE=/%5D/g,Qh=/%5E/g,qE=/%60/g,Zh=/%7B/g,HE=/%7C/g,ep=/%7D/g,WE=/%20/g;function Ou(e){return encodeURI(""+e).replace(HE,"|").replace(UE,"[").replace(BE,"]")}function jE(e){return Ou(e).replace(Zh,"{").replace(ep,"}").replace(Qh,"^")}function xu(e){return Ou(e).replace(Jh,"%2B").replace(WE,"+").replace(Yh,"%23").replace(VE,"%26").replace(qE,"`").replace(Zh,"{").replace(ep,"}").replace(Qh,"^")}function GE(e){return xu(e).replace($E,"%3D")}function zE(e){return Ou(e).replace(Yh,"%23").replace(FE,"%3F")}function KE(e){return e==null?"":zE(e).replace(LE,"%2F")}function zn(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&ze(`Error decoding "${e}". Using original value`)}return""+e}const XE=/\/$/,YE=e=>e.replace(XE,"");function Su(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let f=t.indexOf("?");return c<f&&c>=0&&(f=-1),f>-1&&(i=t.slice(0,f),a=t.slice(f+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=ZE(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:zn(u)}}function JE(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function tp(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function sp(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&jr(t.matched[i],s.matched[n])&&rp(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function jr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function rp(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!QE(e[s],t[s]))return!1;return!0}function QE(e,t){return fs(e)?np(e,t):fs(t)?np(t,e):e===t}function np(e,t){return fs(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function ZE(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return ze(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Gr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ko;(function(e){e.pop="pop",e.push="push"})(Ko||(Ko={}));var Xo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Xo||(Xo={}));function e0(e){if(!e)if(Er){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),YE(e)}const t0=/^[^#]+#/;function s0(e,t){return e.replace(t0,"#")+t}function r0(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const ia=()=>({left:window.scrollX,top:window.scrollY});function n0(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){ze(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{ze(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&ze(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=r0(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function op(e,t){return(history.state?history.state.position-t:-1)+e}const Iu=new Map;function o0(e,t){Iu.set(e,t)}function i0(e){const t=Iu.get(e);return Iu.delete(e),t}let a0=()=>location.protocol+"//"+location.host;function ip(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,f=n.slice(c);return f[0]!=="/"&&(f="/"+f),tp(f,"")}return tp(s,e)+i+n}function l0(e,t,s,i){let n=[],a=[],u=null;const c=({state:v})=>{const C=ip(e,location),P=s.value,A=t.value;let oe=0;if(v){if(s.value=C,t.value=v,u&&u===P){u=null;return}oe=A?v.position-A.position:0}else i(C);n.forEach(W=>{W(s.value,P,{delta:oe,type:Ko.pop,direction:oe?oe>0?Xo.forward:Xo.back:Xo.unknown})})};function f(){u=s.value}function g(v){n.push(v);const C=()=>{const P=n.indexOf(v);P>-1&&n.splice(P,1)};return a.push(C),C}function h(){const{history:v}=window;v.state&&v.replaceState(nt({},v.state,{scroll:ia()}),"")}function p(){for(const v of a)v();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",h)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",h,{passive:!0}),{pauseListeners:f,listen:g,destroy:p}}function ap(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?ia():null}}function u0(e){const{history:t,location:s}=window,i={value:ip(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(f,g,h){const p=e.indexOf("#"),v=p>-1?(s.host&&document.querySelector("base")?e:e.slice(p))+f:a0()+e+f;try{t[h?"replaceState":"pushState"](g,"",v),n.value=g}catch(C){({}).NODE_ENV!=="production"?ze("Error with push/replace State",C):console.error(C),s[h?"replace":"assign"](v)}}function u(f,g){const h=nt({},t.state,ap(n.value.back,f,n.value.forward,!0),g,{position:n.value.position});a(f,h,!0),i.value=f}function c(f,g){const h=nt({},n.value,t.state,{forward:f,scroll:ia()});({}).NODE_ENV!=="production"&&!t.state&&ze(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(h.current,h,!0);const p=nt({},ap(i.value,f,null),{position:h.position+1},g);a(f,p,!1),i.value=f}return{location:i,state:n,push:c,replace:u}}function c0(e){e=e0(e);const t=u0(e),s=l0(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=nt({location:"",base:e,go:i,createHref:s0.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function aa(e){return typeof e=="string"||e&&typeof e=="object"}function lp(e){return typeof e=="string"||typeof e=="symbol"}const Du=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var up;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(up||(up={}));const d0={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${h0(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Kn(e,t){return{}.NODE_ENV!=="production"?nt(new Error(d0[e](t)),{type:e,[Du]:!0},t):nt(new Error,{type:e,[Du]:!0},t)}function Cr(e,t){return e instanceof Error&&Du in e&&(t==null||!!(e.type&t))}const f0=["params","query","hash"];function h0(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of f0)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const cp="[^/]+?",p0={sensitive:!1,strict:!1,start:!0,end:!0},m0=/[.+*?^${}()[\]/\\]/g;function g0(e,t){const s=nt({},p0,t),i=[];let n=s.start?"^":"";const a=[];for(const g of e){const h=g.length?[]:[90];s.strict&&!g.length&&(n+="/");for(let p=0;p<g.length;p++){const v=g[p];let C=40+(s.sensitive?.25:0);if(v.type===0)p||(n+="/"),n+=v.value.replace(m0,"\\$&"),C+=40;else if(v.type===1){const{value:P,repeatable:A,optional:oe,regexp:W}=v;a.push({name:P,repeatable:A,optional:oe});const ie=W||cp;if(ie!==cp){C+=10;try{new RegExp(`(${ie})`)}catch(ye){throw new Error(`Invalid custom RegExp for param "${P}" (${ie}): `+ye.message)}}let q=A?`((?:${ie})(?:/(?:${ie}))*)`:`(${ie})`;p||(q=oe&&g.length<2?`(?:/${q})`:"/"+q),oe&&(q+="?"),n+=q,C+=20,oe&&(C+=-8),A&&(C+=-20),ie===".*"&&(C+=-50)}h.push(C)}i.push(h)}if(s.strict&&s.end){const g=i.length-1;i[g][i[g].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(g){const h=g.match(u),p={};if(!h)return null;for(let v=1;v<h.length;v++){const C=h[v]||"",P=a[v-1];p[P.name]=C&&P.repeatable?C.split("/"):C}return p}function f(g){let h="",p=!1;for(const v of e){(!p||!h.endsWith("/"))&&(h+="/"),p=!1;for(const C of v)if(C.type===0)h+=C.value;else if(C.type===1){const{value:P,repeatable:A,optional:oe}=C,W=P in g?g[P]:"";if(fs(W)&&!A)throw new Error(`Provided param "${P}" is an array but it is not repeatable (* or + modifiers)`);const ie=fs(W)?W.join("/"):W;if(!ie)if(oe)v.length<2&&(h.endsWith("/")?h=h.slice(0,-1):p=!0);else throw new Error(`Missing required param "${P}"`);h+=ie}}return h||"/"}return{re:u,score:i,keys:a,parse:c,stringify:f}}function _0(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function dp(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=_0(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(fp(i))return 1;if(fp(n))return-1}return n.length-i.length}function fp(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const v0={type:0,value:""},y0=/[a-zA-Z0-9_]/;function b0(e){if(!e)return[[]];if(e==="/")return[[v0]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(C){throw new Error(`ERR (${s})/"${g}": ${C}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,f,g="",h="";function p(){g&&(s===0?a.push({type:0,value:g}):s===1||s===2||s===3?(a.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${g}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:g,regexp:h,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),g="")}function v(){g+=f}for(;c<e.length;){if(f=e[c++],f==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:f==="/"?(g&&p(),u()):f===":"?(p(),s=1):v();break;case 4:v(),s=i;break;case 1:f==="("?s=2:y0.test(f)?v():(p(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&c--);break;case 2:f===")"?h[h.length-1]=="\\"?h=h.slice(0,-1)+f:s=3:h+=f;break;case 3:p(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&c--,h="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${g}"`),p(),u(),n}function E0(e,t,s){const i=g0(b0(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&ze(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=nt(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function C0(e,t){const s=[],i=new Map;t=gp({strict:!1,end:!0,sensitive:!1},t);function n(p){return i.get(p)}function a(p,v,C){const P=!C,A=pp(p);({}).NODE_ENV!=="production"&&S0(A,v),A.aliasOf=C&&C.record;const oe=gp(t,p),W=[A];if("alias"in p){const ye=typeof p.alias=="string"?[p.alias]:p.alias;for(const se of ye)W.push(pp(nt({},A,{components:C?C.record.components:A.components,path:se,aliasOf:C?C.record:A})))}let ie,q;for(const ye of W){const{path:se}=ye;if(v&&se[0]!=="/"){const be=v.record.path,xe=be[be.length-1]==="/"?"":"/";ye.path=v.record.path+(se&&xe+se)}if({}.NODE_ENV!=="production"&&ye.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(ie=E0(ye,v,oe),{}.NODE_ENV!=="production"&&v&&se[0]==="/"&&D0(ie,v),C?(C.alias.push(ie),{}.NODE_ENV!=="production"&&x0(C,ie)):(q=q||ie,q!==ie&&q.alias.push(ie),P&&p.name&&!mp(ie)&&({}.NODE_ENV!=="production"&&I0(p,v),u(p.name))),_p(ie)&&f(ie),A.children){const be=A.children;for(let xe=0;xe<be.length;xe++)a(be[xe],ie,C&&C.children[xe])}C=C||ie}return q?()=>{u(q)}:zo}function u(p){if(lp(p)){const v=i.get(p);v&&(i.delete(p),s.splice(s.indexOf(v),1),v.children.forEach(u),v.alias.forEach(u))}else{const v=s.indexOf(p);v>-1&&(s.splice(v,1),p.record.name&&i.delete(p.record.name),p.children.forEach(u),p.alias.forEach(u))}}function c(){return s}function f(p){const v=N0(p,s);s.splice(v,0,p),p.record.name&&!mp(p)&&i.set(p.record.name,p)}function g(p,v){let C,P={},A,oe;if("name"in p&&p.name){if(C=i.get(p.name),!C)throw Kn(1,{location:p});if({}.NODE_ENV!=="production"){const q=Object.keys(p.params||{}).filter(ye=>!C.keys.find(se=>se.name===ye));q.length&&ze(`Discarded invalid param(s) "${q.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}oe=C.record.name,P=nt(hp(v.params,C.keys.filter(q=>!q.optional).concat(C.parent?C.parent.keys.filter(q=>q.optional):[]).map(q=>q.name)),p.params&&hp(p.params,C.keys.map(q=>q.name))),A=C.stringify(P)}else if(p.path!=null)A=p.path,{}.NODE_ENV!=="production"&&!A.startsWith("/")&&ze(`The Matcher cannot resolve relative paths but received "${A}". Unless you directly called \`matcher.resolve("${A}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),C=s.find(q=>q.re.test(A)),C&&(P=C.parse(A),oe=C.record.name);else{if(C=v.name?i.get(v.name):s.find(q=>q.re.test(v.path)),!C)throw Kn(1,{location:p,currentLocation:v});oe=C.record.name,P=nt({},v.params,p.params),A=C.stringify(P)}const W=[];let ie=C;for(;ie;)W.unshift(ie.record),ie=ie.parent;return{name:oe,path:A,params:P,matched:W,meta:O0(W)}}e.forEach(p=>a(p));function h(){s.length=0,i.clear()}return{addRoute:a,resolve:g,removeRoute:u,clearRoutes:h,getRoutes:c,getRecordMatcher:n}}function hp(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function pp(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:w0(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function w0(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function mp(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function O0(e){return e.reduce((t,s)=>nt(t,s.meta),{})}function gp(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function Nu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function x0(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(Nu.bind(null,s)))return ze(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(Nu.bind(null,s)))return ze(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function S0(e,t){t&&t.record.name&&!e.name&&!e.path&&ze(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function I0(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function D0(e,t){for(const s of t.keys)if(!e.keys.find(Nu.bind(null,s)))return ze(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function N0(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;dp(e,t[a])<0?i=a:s=a+1}const n=T0(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&ze(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function T0(e){let t=e;for(;t=t.parent;)if(_p(t)&&dp(e,t)===0)return t}function _p({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function A0(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(Jh," "),u=a.indexOf("="),c=zn(u<0?a:a.slice(0,u)),f=u<0?null:zn(a.slice(u+1));if(c in t){let g=t[c];fs(g)||(g=t[c]=[g]),g.push(f)}else t[c]=f}return t}function vp(e){let t="";for(let s in e){const i=e[s];if(s=GE(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(fs(i)?i.map(a=>a&&xu(a)):[i&&xu(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function R0(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=fs(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const P0=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),yp=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Tu=Symbol({}.NODE_ENV!=="production"?"router":""),bp=Symbol({}.NODE_ENV!=="production"?"route location":""),Au=Symbol({}.NODE_ENV!=="production"?"router view location":"");function Yo(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function zr(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,f)=>{const g=v=>{v===!1?f(Kn(4,{from:s,to:t})):v instanceof Error?f(v):aa(v)?f(Kn(2,{from:t,to:v})):(u&&i.enterCallbacks[n]===u&&typeof v=="function"&&u.push(v),c())},h=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?M0(g,t,s):g));let p=Promise.resolve(h);if(e.length<3&&(p=p.then(g)),{}.NODE_ENV!=="production"&&e.length>2){const v=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof h=="object"&&"then"in h)p=p.then(C=>g._called?C:(ze(v),Promise.reject(new Error("Invalid navigation guard"))));else if(h!==void 0&&!g._called){ze(v),f(new Error("Invalid navigation guard"));return}}p.catch(v=>f(v))})}function M0(e,t,s){let i=0;return function(){i++===1&&ze(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function Ru(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&ze(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let f=u.components[c];if({}.NODE_ENV!=="production"){if(!f||typeof f!="object"&&typeof f!="function")throw ze(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(f)}".`),new Error("Invalid route component");if("then"in f){ze(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const g=f;f=()=>g}else f.__asyncLoader&&!f.__warnedDefineAsync&&(f.__warnedDefineAsync=!0,ze(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(Xh(f)){const h=(f.__vccOpts||f)[t];h&&a.push(zr(h,s,i,u,c,n))}else{let g=f();({}).NODE_ENV!=="production"&&!("catch"in g)&&(ze(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),g=Promise.resolve(g)),a.push(()=>g.then(h=>{if(!h)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const p=kE(h)?h.default:h;u.mods[c]=h,u.components[c]=p;const C=(p.__vccOpts||p)[t];return C&&zr(C,s,i,u,c,n)()}))}}}return a}function Ep(e){const t=hr(Tu),s=hr(bp);let i=!1,n=null;const a=$s(()=>{const h=Lr(e.to);return{}.NODE_ENV!=="production"&&(!i||h!==n)&&(aa(h)||(i?ze(`Invalid value for prop "to" in useLink()
- to:`,h,`
- previous to:`,n,`
- props:`,e):ze(`Invalid value for prop "to" in useLink()
- to:`,h,`
- props:`,e)),n=h,i=!0),t.resolve(h)}),u=$s(()=>{const{matched:h}=a.value,{length:p}=h,v=h[p-1],C=s.matched;if(!v||!C.length)return-1;const P=C.findIndex(jr.bind(null,v));if(P>-1)return P;const A=Cp(h[p-2]);return p>1&&Cp(v)===A&&C[C.length-1].path!==A?C.findIndex(jr.bind(null,h[p-2])):P}),c=$s(()=>u.value>-1&&$0(s.params,a.value.params)),f=$s(()=>u.value>-1&&u.value===s.matched.length-1&&rp(s.params,a.value.params));function g(h={}){if(L0(h)){const p=t[Lr(e.replace)?"replace":"push"](Lr(e.to)).catch(zo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}if({}.NODE_ENV!=="production"&&Er){const h=Xi();if(h){const p={route:a.value,isActive:c.value,isExactActive:f.value,error:null};h.__vrl_devtools=h.__vrl_devtools||[],h.__vrl_devtools.push(p),Zy(()=>{p.route=a.value,p.isActive=c.value,p.isExactActive=f.value,p.error=aa(Lr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:$s(()=>a.value.href),isActive:c,isExactActive:f,navigate:g}}function k0(e){return e.length===1?e[0]:e}const V0=vf({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ep,setup(e,{slots:t}){const s=Si(Ep(e)),{options:i}=hr(Tu),n=$s(()=>({[wp(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[wp(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&k0(t.default(s));return e.custom?a:fu("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function L0(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function $0(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!fs(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function Cp(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const wp=(e,t,s)=>e??t??s,F0=vf({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&B0();const i=hr(Au),n=$s(()=>e.route||i.value),a=hr(yp,0),u=$s(()=>{let g=Lr(a);const{matched:h}=n.value;let p;for(;(p=h[g])&&!p.components;)g++;return g}),c=$s(()=>n.value.matched[u.value]);Wi(yp,$s(()=>u.value+1)),Wi(P0,c),Wi(Au,n);const f=Bd();return Hn(()=>[f.value,c.value,e.name],([g,h,p],[v,C,P])=>{h&&(h.instances[p]=g,C&&C!==h&&g&&g===v&&(h.leaveGuards.size||(h.leaveGuards=C.leaveGuards),h.updateGuards.size||(h.updateGuards=C.updateGuards))),g&&h&&(!C||!jr(h,C)||!v)&&(h.enterCallbacks[p]||[]).forEach(A=>A(g))},{flush:"post"}),()=>{const g=n.value,h=e.name,p=c.value,v=p&&p.components[h];if(!v)return Op(s.default,{Component:v,route:g});const C=p.props[h],P=C?C===!0?g.params:typeof C=="function"?C(g):C:null,oe=fu(v,nt({},P,t,{onVnodeUnmounted:W=>{W.component.isUnmounted&&(p.instances[h]=null)},ref:f}));if({}.NODE_ENV!=="production"&&Er&&oe.ref){const W={depth:u.value,name:p.name,path:p.path,meta:p.meta};(fs(oe.ref)?oe.ref.map(q=>q.i):[oe.ref.i]).forEach(q=>{q.__vrv_devtools=W})}return Op(s.default,{Component:oe,route:g})||oe}}});function Op(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const U0=F0;function B0(){const e=Xi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";ze(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Jo(e,t){const s=nt({},e,{matched:e.matched.map(i=>Q0(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function la(e){return{_custom:{display:e}}}let q0=0;function H0(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=q0++;yu({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((h,p)=>{h.instanceData&&h.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Jo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:h,componentInstance:p})=>{if(p.__vrv_devtools){const v=p.__vrv_devtools;h.tags.push({label:(v.name?`${v.name.toString()}: `:"")+v.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:xp})}fs(p.__vrl_devtools)&&(p.__devtoolsApi=n,p.__vrl_devtools.forEach(v=>{let C=v.route.path,P=Dp,A="",oe=0;v.error?(C=v.error,P=K0,oe=X0):v.isExactActive?(P=Ip,A="This is exactly active"):v.isActive&&(P=Sp,A="This link is active"),h.tags.push({label:C,textColor:oe,tooltip:A,backgroundColor:P})}))}),Hn(t.currentRoute,()=>{f(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((h,p)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:p.fullPath,logType:"error",time:n.now(),data:{error:h},groupId:p.meta.__navigationId}})});let u=0;t.beforeEach((h,p)=>{const v={guard:la("beforeEach"),from:Jo(p,"Current Location during this navigation"),to:Jo(h,"Target location")};Object.defineProperty(h.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:h.fullPath,data:v,groupId:h.meta.__navigationId}})}),t.afterEach((h,p,v)=>{const C={guard:la("afterEach")};v?(C.failure={_custom:{type:Error,readOnly:!0,display:v?v.message:"",tooltip:"Navigation Failure",value:v}},C.status=la("❌")):C.status=la("✅"),C.from=Jo(p,"Current Location during this navigation"),C.to=Jo(h,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:h.fullPath,time:n.now(),data:C,logType:v?"warning":"default",groupId:h.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function f(){if(!g)return;const h=g;let p=s.getRoutes().filter(v=>!v.parent||!v.parent.record.components);p.forEach(Ap),h.filter&&(p=p.filter(v=>Pu(v,h.filter.toLowerCase()))),p.forEach(v=>Tp(v,t.currentRoute.value)),h.rootNodes=p.map(Np)}let g;n.on.getInspectorTree(h=>{g=h,h.app===e&&h.inspectorId===c&&f()}),n.on.getInspectorState(h=>{if(h.app===e&&h.inspectorId===c){const v=s.getRoutes().find(C=>C.record.__vd_id===h.nodeId);v&&(h.state={options:j0(v)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function W0(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function j0(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${W0(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const xp=15485081,Sp=2450411,Ip=8702998,G0=2282478,Dp=16486972,z0=6710886,K0=16704226,X0=12131356;function Np(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:G0}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Dp}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:xp}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Ip}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:Sp}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:z0});let i=s.__vd_id;return i==null&&(i=String(Y0++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Np)}}let Y0=0;const J0=/^\/(.*)\/([a-z]*)$/;function Tp(e,t){const s=t.matched.length&&jr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>jr(i,e.record))),e.children.forEach(i=>Tp(i,t))}function Ap(e){e.__vd_match=!1,e.children.forEach(Ap)}function Pu(e,t){const s=String(e.re).match(J0);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>Pu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=zn(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>Pu(u,t))}function Q0(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Z0(e){const t=C0(e.routes,e),s=e.parseQuery||A0,i=e.stringifyQuery||vp,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=Yo(),u=Yo(),c=Yo(),f=Av(Gr);let g=Gr;Er&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const h=wu.bind(null,V=>""+V),p=wu.bind(null,KE),v=wu.bind(null,zn);function C(V,fe){let ce,Ce;return lp(V)?(ce=t.getRecordMatcher(V),{}.NODE_ENV!=="production"&&!ce&&ze(`Parent route "${String(V)}" not found when adding child route`,fe),Ce=fe):Ce=V,t.addRoute(Ce,ce)}function P(V){const fe=t.getRecordMatcher(V);fe?t.removeRoute(fe):{}.NODE_ENV!=="production"&&ze(`Cannot remove non-existent route "${String(V)}"`)}function A(){return t.getRoutes().map(V=>V.record)}function oe(V){return!!t.getRecordMatcher(V)}function W(V,fe){if(fe=nt({},fe||f.value),typeof V=="string"){const E=Su(s,V,fe.path),O=t.resolve({path:E.path},fe),k=n.createHref(E.fullPath);return{}.NODE_ENV!=="production"&&(k.startsWith("//")?ze(`Location "${V}" resolved to "${k}". A resolved location cannot start with multiple slashes.`):O.matched.length||ze(`No match found for location with path "${V}"`)),nt(E,O,{params:v(O.params),hash:zn(E.hash),redirectedFrom:void 0,href:k})}if({}.NODE_ENV!=="production"&&!aa(V))return ze(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,V),W({});let ce;if(V.path!=null)({}).NODE_ENV!=="production"&&"params"in V&&!("name"in V)&&Object.keys(V.params).length&&ze(`Path "${V.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ce=nt({},V,{path:Su(s,V.path,fe.path).path});else{const E=nt({},V.params);for(const O in E)E[O]==null&&delete E[O];ce=nt({},V,{params:p(E)}),fe.params=p(fe.params)}const Ce=t.resolve(ce,fe),Ue=V.hash||"";({}).NODE_ENV!=="production"&&Ue&&!Ue.startsWith("#")&&ze(`A \`hash\` should always start with the character "#". Replace "${Ue}" with "#${Ue}".`),Ce.params=h(v(Ce.params));const ft=JE(i,nt({},V,{hash:jE(Ue),path:Ce.path})),Be=n.createHref(ft);return{}.NODE_ENV!=="production"&&(Be.startsWith("//")?ze(`Location "${V}" resolved to "${Be}". A resolved location cannot start with multiple slashes.`):Ce.matched.length||ze(`No match found for location with path "${V.path!=null?V.path:V}"`)),nt({fullPath:ft,hash:Ue,query:i===vp?R0(V.query):V.query||{}},Ce,{redirectedFrom:void 0,href:Be})}function ie(V){return typeof V=="string"?Su(s,V,f.value.path):nt({},V)}function q(V,fe){if(g!==V)return Kn(8,{from:fe,to:V})}function ye(V){return xe(V)}function se(V){return ye(nt(ie(V),{replace:!0}))}function be(V){const fe=V.matched[V.matched.length-1];if(fe&&fe.redirect){const{redirect:ce}=fe;let Ce=typeof ce=="function"?ce(V):ce;if(typeof Ce=="string"&&(Ce=Ce.includes("?")||Ce.includes("#")?Ce=ie(Ce):{path:Ce},Ce.params={}),{}.NODE_ENV!=="production"&&Ce.path==null&&!("name"in Ce))throw ze(`Invalid redirect found:
${JSON.stringify(Ce,null,2)}
 when navigating to "${V.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return nt({query:V.query,hash:V.hash,params:Ce.path!=null?{}:V.params},Ce)}}function xe(V,fe){const ce=g=W(V),Ce=f.value,Ue=V.state,ft=V.force,Be=V.replace===!0,E=be(ce);if(E)return xe(nt(ie(E),{state:typeof E=="object"?nt({},Ue,E.state):Ue,force:ft,replace:Be}),fe||ce);const O=ce;O.redirectedFrom=fe;let k;return!ft&&sp(i,Ce,ce)&&(k=Kn(16,{to:O,from:Ce}),_e(Ce,Ce,!0,!1)),(k?Promise.resolve(k):De(O,Ce)).catch(F=>Cr(F)?Cr(F,2)?F:ee(F):J(F,O,Ce)).then(F=>{if(F){if(Cr(F,2))return{}.NODE_ENV!=="production"&&sp(i,W(F.to),O)&&fe&&(fe._count=fe._count?fe._count+1:1)>30?(ze(`Detected a possibly infinite redirection in a navigation guard when going from "${Ce.fullPath}" to "${O.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):xe(nt({replace:Be},ie(F.to),{state:typeof F.to=="object"?nt({},Ue,F.to.state):Ue,force:ft}),fe||O)}else F=ue(O,Ce,!0,Be,Ue);return $e(O,Ce,F),F})}function ke(V,fe){const ce=q(V,fe);return ce?Promise.reject(ce):Promise.resolve()}function de(V){const fe=yt.values().next().value;return fe&&typeof fe.runWithContext=="function"?fe.runWithContext(V):V()}function De(V,fe){let ce;const[Ce,Ue,ft]=eC(V,fe);ce=Ru(Ce.reverse(),"beforeRouteLeave",V,fe);for(const E of Ce)E.leaveGuards.forEach(O=>{ce.push(zr(O,V,fe))});const Be=ke.bind(null,V,fe);return ce.push(Be),Mt(ce).then(()=>{ce=[];for(const E of a.list())ce.push(zr(E,V,fe));return ce.push(Be),Mt(ce)}).then(()=>{ce=Ru(Ue,"beforeRouteUpdate",V,fe);for(const E of Ue)E.updateGuards.forEach(O=>{ce.push(zr(O,V,fe))});return ce.push(Be),Mt(ce)}).then(()=>{ce=[];for(const E of ft)if(E.beforeEnter)if(fs(E.beforeEnter))for(const O of E.beforeEnter)ce.push(zr(O,V,fe));else ce.push(zr(E.beforeEnter,V,fe));return ce.push(Be),Mt(ce)}).then(()=>(V.matched.forEach(E=>E.enterCallbacks={}),ce=Ru(ft,"beforeRouteEnter",V,fe,de),ce.push(Be),Mt(ce))).then(()=>{ce=[];for(const E of u.list())ce.push(zr(E,V,fe));return ce.push(Be),Mt(ce)}).catch(E=>Cr(E,8)?E:Promise.reject(E))}function $e(V,fe,ce){c.list().forEach(Ce=>de(()=>Ce(V,fe,ce)))}function ue(V,fe,ce,Ce,Ue){const ft=q(V,fe);if(ft)return ft;const Be=fe===Gr,E=Er?history.state:{};ce&&(Ce||Be?n.replace(V.fullPath,nt({scroll:Be&&E&&E.scroll},Ue)):n.push(V.fullPath,Ue)),f.value=V,_e(V,fe,ce,Be),ee()}let R;function G(){R||(R=n.listen((V,fe,ce)=>{if(!ot.listening)return;const Ce=W(V),Ue=be(Ce);if(Ue){xe(nt(Ue,{replace:!0,force:!0}),Ce).catch(zo);return}g=Ce;const ft=f.value;Er&&o0(op(ft.fullPath,ce.delta),ia()),De(Ce,ft).catch(Be=>Cr(Be,12)?Be:Cr(Be,2)?(xe(nt(ie(Be.to),{force:!0}),Ce).then(E=>{Cr(E,20)&&!ce.delta&&ce.type===Ko.pop&&n.go(-1,!1)}).catch(zo),Promise.reject()):(ce.delta&&n.go(-ce.delta,!1),J(Be,Ce,ft))).then(Be=>{Be=Be||ue(Ce,ft,!1),Be&&(ce.delta&&!Cr(Be,8)?n.go(-ce.delta,!1):ce.type===Ko.pop&&Cr(Be,20)&&n.go(-1,!1)),$e(Ce,ft,Be)}).catch(zo)}))}let Y=Yo(),pe=Yo(),j;function J(V,fe,ce){ee(V);const Ce=pe.list();return Ce.length?Ce.forEach(Ue=>Ue(V,fe,ce)):({}.NODE_ENV!=="production"&&ze("uncaught error during route navigation:"),console.error(V)),Promise.reject(V)}function ge(){return j&&f.value!==Gr?Promise.resolve():new Promise((V,fe)=>{Y.add([V,fe])})}function ee(V){return j||(j=!V,G(),Y.list().forEach(([fe,ce])=>V?ce(V):fe()),Y.reset()),V}function _e(V,fe,ce,Ce){const{scrollBehavior:Ue}=e;if(!Er||!Ue)return Promise.resolve();const ft=!ce&&i0(op(V.fullPath,0))||(Ce||!ce)&&history.state&&history.state.scroll||null;return Vl().then(()=>Ue(V,fe,ft)).then(Be=>Be&&n0(Be)).catch(Be=>J(Be,V,fe))}const Se=V=>n.go(V);let Ye;const yt=new Set,ot={currentRoute:f,listening:!0,addRoute:C,removeRoute:P,clearRoutes:t.clearRoutes,hasRoute:oe,getRoutes:A,resolve:W,options:e,push:ye,replace:se,go:Se,back:()=>Se(-1),forward:()=>Se(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:pe.add,isReady:ge,install(V){const fe=this;V.component("RouterLink",V0),V.component("RouterView",U0),V.config.globalProperties.$router=fe,Object.defineProperty(V.config.globalProperties,"$route",{enumerable:!0,get:()=>Lr(f)}),Er&&!Ye&&f.value===Gr&&(Ye=!0,ye(n.location).catch(Ue=>{({}).NODE_ENV!=="production"&&ze("Unexpected error when starting the router:",Ue)}));const ce={};for(const Ue in Gr)Object.defineProperty(ce,Ue,{get:()=>f.value[Ue],enumerable:!0});V.provide(Tu,fe),V.provide(bp,Fd(ce)),V.provide(Au,f);const Ce=V.unmount;yt.add(V),V.unmount=function(){yt.delete(V),yt.size<1&&(g=Gr,R&&R(),R=null,f.value=Gr,Ye=!1,j=!1),Ce()},{}.NODE_ENV!=="production"&&Er&&H0(V,fe,t)}};function Mt(V){return V.reduce((fe,ce)=>fe.then(()=>de(ce)),Promise.resolve())}return ot}function eC(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(g=>jr(g,c))?i.push(c):s.push(c));const f=e.matched[u];f&&(t.matched.find(g=>jr(g,f))||n.push(f))}return[s,i,n]}const Ke=async(e,t)=>{const s={methodname:e,args:Object.assign({},t)};try{return await H_.call([s])[0]}catch(i){throw W_.exception(i),i}};async function tC(e={}){try{return await Ke("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw t}}async function sC(e){try{return await Ke("local_offermanager_get",{id:e})}catch(t){throw t}}async function Rp(e){try{return await Ke("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",status:e.status||0,audienceids:e.audiences||[]})}catch(t){throw t}}async function rC(e){try{return await Ke("local_offermanager_delete",{id:e})}catch(t){throw t}}async function nC(){try{return await Ke("local_offermanager_get_type_options",{})}catch(e){throw e}}async function oC(e,t){try{return await Ke("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw s}}async function iC(e,t,s){try{return await Ke("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw i}}async function aC(e){try{return(await Ke("local_offermanager_get_audiences",{offerid:0})).all_audiences.filter(i=>i.name.toLowerCase().includes(e.toLowerCase())).map(i=>({id:i.id,name:i.name}))}catch(t){throw t}}async function lC(e,t){try{return await Ke("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw s}}async function Mu(e="",t=0){try{return await Ke("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw s}}async function uC(e,t,s="",i=1,n=20){try{return await Ke("local_offermanager_fetch_potential_courses",{offerid:e,categoryid:t,search_string:s||"",page:i,per_page:n,exclude_courseids:[]})}catch(a){throw a}}async function cC(e,t,s="",i=[],n=!1){try{return await Ke("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:s,exclude_courseids:i||[],only_active:n})}catch(a){throw a}}async function dC(e,t){try{return await Ke("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw s}}async function Pp(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="courseClassCount"&&(t.sortBy="class_counter"),await Ke("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw s}}async function fC(e){try{return await Ke("local_offermanager_add_class",e)}catch(t){throw console.error("Erro ao criar turma:",t),t}}async function Mp(e){try{return await Ke("local_offermanager_get_class",{offerclassid:e})}catch(t){throw t}}async function hC(e){try{return await Ke("local_offermanager_get_course",{offercourseid:e})}catch(t){throw t}}async function pC(e){try{return await Ke("local_offermanager_get_classes",{offercourseid:e})}catch(t){throw console.error("Error fetching:",t),t}}async function mC(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","enablehirearchyrestriction","hirearchyrestrictiondivisions","hirearchyrestrictionsectors","hirearchyrestrictiongroups","hirearchyrestrictiondealerships","modality","maxusersdealership"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return t.forEach(n=>{n in e.optional_fields&&(s.optional_fields[n]=e.optional_fields[n])}),"enrol"in s&&delete s.enrol,await Ke("local_offermanager_update_class",s)}catch(t){throw t}}async function gC(e){try{return await Ke("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw t}}async function _C(e,t=0,s="",i=[]){try{return await Ke("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw n}}async function vC(){try{return await Ke("local_offermanager_get_situation_list",{})}catch(e){throw e}}async function yC(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Ke("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw s}}async function bC(e){try{return await Ke("local_offermanager_get_duplication_courses",{offerclassid:e})}catch(t){throw t}}async function ku(e){try{return await Ke("local_offermanager_get_course_roles",{offercourseid:e})}catch(t){throw t}}async function EC(e=!0){try{return await Ke("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw t}}async function CC(e,t){try{return await Ke("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw s}}async function wC(){try{return await Ke("local_offermanager_get_hierarchy_divisions",{})}catch(e){throw e}}async function OC(e){try{return await Ke("local_offermanager_get_hierarchy_sectors",{divisionids:e})}catch(t){throw t}}async function xC(e){try{return await Ke("local_offermanager_get_hierarchy_groups",{sectorids:e})}catch(t){throw t}}async function SC(e){try{return await Ke("local_offermanager_get_hierarchy_dealerships",{groupids:e})}catch(t){throw t}}var Qo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ua={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */ua.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",f="__lodash_hash_undefined__",g=500,h="__lodash_placeholder__",p=1,v=2,C=4,P=1,A=2,oe=1,W=2,ie=4,q=8,ye=16,se=32,be=64,xe=128,ke=256,de=512,De=30,$e="...",ue=800,R=16,G=1,Y=2,pe=3,j=1/0,J=9007199254740991,ge=17976931348623157e292,ee=0/0,_e=**********,Se=_e-1,Ye=_e>>>1,yt=[["ary",xe],["bind",oe],["bindKey",W],["curry",q],["curryRight",ye],["flip",de],["partial",se],["partialRight",be],["rearg",ke]],ot="[object Arguments]",Mt="[object Array]",V="[object AsyncFunction]",fe="[object Boolean]",ce="[object Date]",Ce="[object DOMException]",Ue="[object Error]",ft="[object Function]",Be="[object GeneratorFunction]",E="[object Map]",O="[object Number]",k="[object Null]",F="[object Object]",H="[object Promise]",z="[object Proxy]",ae="[object RegExp]",Q="[object Set]",re="[object String]",X="[object Symbol]",Ie="[object Undefined]",le="[object WeakMap]",we="[object WeakSet]",Ne="[object ArrayBuffer]",He="[object DataView]",it="[object Float32Array]",st="[object Float64Array]",Ft="[object Int8Array]",Dt="[object Int16Array]",ss="[object Int32Array]",qt="[object Uint8Array]",xr="[object Uint8ClampedArray]",eo="[object Uint16Array]",kt="[object Uint32Array]",Es=/\b__p \+= '';/g,Ia=/\b(__p \+=) '' \+/g,PP=/(__e\(.*?\)|\b__t\)) \+\n'';/g,hm=/&(?:amp|lt|gt|quot|#39);/g,pm=/[&<>"']/g,MP=RegExp(hm.source),kP=RegExp(pm.source),VP=/<%-([\s\S]+?)%>/g,LP=/<%([\s\S]+?)%>/g,mm=/<%=([\s\S]+?)%>/g,$P=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,FP=/^\w*$/,UP=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ec=/[\\^$.*+?()[\]{}|]/g,BP=RegExp(ec.source),tc=/^\s+/,qP=/\s/,HP=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,WP=/\{\n\/\* \[wrapped with (.+)\] \*/,jP=/,? & /,GP=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,zP=/[()=,{}\[\]\/\s]/,KP=/\\(\\)?/g,XP=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,gm=/\w*$/,YP=/^[-+]0x[0-9a-f]+$/i,JP=/^0b[01]+$/i,QP=/^\[object .+?Constructor\]$/,ZP=/^0o[0-7]+$/i,e2=/^(?:0|[1-9]\d*)$/,t2=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Da=/($^)/,s2=/['\n\r\u2028\u2029\\]/g,Na="\\ud800-\\udfff",r2="\\u0300-\\u036f",n2="\\ufe20-\\ufe2f",o2="\\u20d0-\\u20ff",_m=r2+n2+o2,vm="\\u2700-\\u27bf",ym="a-z\\xdf-\\xf6\\xf8-\\xff",i2="\\xac\\xb1\\xd7\\xf7",a2="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",l2="\\u2000-\\u206f",u2=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",bm="A-Z\\xc0-\\xd6\\xd8-\\xde",Em="\\ufe0e\\ufe0f",Cm=i2+a2+l2+u2,sc="['’]",c2="["+Na+"]",wm="["+Cm+"]",Ta="["+_m+"]",Om="\\d+",d2="["+vm+"]",xm="["+ym+"]",Sm="[^"+Na+Cm+Om+vm+ym+bm+"]",rc="\\ud83c[\\udffb-\\udfff]",f2="(?:"+Ta+"|"+rc+")",Im="[^"+Na+"]",nc="(?:\\ud83c[\\udde6-\\uddff]){2}",oc="[\\ud800-\\udbff][\\udc00-\\udfff]",to="["+bm+"]",Dm="\\u200d",Nm="(?:"+xm+"|"+Sm+")",h2="(?:"+to+"|"+Sm+")",Tm="(?:"+sc+"(?:d|ll|m|re|s|t|ve))?",Am="(?:"+sc+"(?:D|LL|M|RE|S|T|VE))?",Rm=f2+"?",Pm="["+Em+"]?",p2="(?:"+Dm+"(?:"+[Im,nc,oc].join("|")+")"+Pm+Rm+")*",m2="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",g2="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Mm=Pm+Rm+p2,_2="(?:"+[d2,nc,oc].join("|")+")"+Mm,v2="(?:"+[Im+Ta+"?",Ta,nc,oc,c2].join("|")+")",y2=RegExp(sc,"g"),b2=RegExp(Ta,"g"),ic=RegExp(rc+"(?="+rc+")|"+v2+Mm,"g"),E2=RegExp([to+"?"+xm+"+"+Tm+"(?="+[wm,to,"$"].join("|")+")",h2+"+"+Am+"(?="+[wm,to+Nm,"$"].join("|")+")",to+"?"+Nm+"+"+Tm,to+"+"+Am,g2,m2,Om,_2].join("|"),"g"),C2=RegExp("["+Dm+Na+_m+Em+"]"),w2=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,O2=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],x2=-1,_t={};_t[it]=_t[st]=_t[Ft]=_t[Dt]=_t[ss]=_t[qt]=_t[xr]=_t[eo]=_t[kt]=!0,_t[ot]=_t[Mt]=_t[Ne]=_t[fe]=_t[He]=_t[ce]=_t[Ue]=_t[ft]=_t[E]=_t[O]=_t[F]=_t[ae]=_t[Q]=_t[re]=_t[le]=!1;var mt={};mt[ot]=mt[Mt]=mt[Ne]=mt[He]=mt[fe]=mt[ce]=mt[it]=mt[st]=mt[Ft]=mt[Dt]=mt[ss]=mt[E]=mt[O]=mt[F]=mt[ae]=mt[Q]=mt[re]=mt[X]=mt[qt]=mt[xr]=mt[eo]=mt[kt]=!0,mt[Ue]=mt[ft]=mt[le]=!1;var S2={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},I2={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},D2={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},N2={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},T2=parseFloat,A2=parseInt,km=typeof Qo=="object"&&Qo&&Qo.Object===Object&&Qo,R2=typeof self=="object"&&self&&self.Object===Object&&self,Ht=km||R2||Function("return this")(),ac=t&&!t.nodeType&&t,xn=ac&&!0&&e&&!e.nodeType&&e,Vm=xn&&xn.exports===ac,lc=Vm&&km.process,Cs=function(){try{var D=xn&&xn.require&&xn.require("util").types;return D||lc&&lc.binding&&lc.binding("util")}catch{}}(),Lm=Cs&&Cs.isArrayBuffer,$m=Cs&&Cs.isDate,Fm=Cs&&Cs.isMap,Um=Cs&&Cs.isRegExp,Bm=Cs&&Cs.isSet,qm=Cs&&Cs.isTypedArray;function ps(D,$,M){switch(M.length){case 0:return D.call($);case 1:return D.call($,M[0]);case 2:return D.call($,M[0],M[1]);case 3:return D.call($,M[0],M[1],M[2])}return D.apply($,M)}function P2(D,$,M,ve){for(var Fe=-1,at=D==null?0:D.length;++Fe<at;){var Vt=D[Fe];$(ve,Vt,M(Vt),D)}return ve}function ws(D,$){for(var M=-1,ve=D==null?0:D.length;++M<ve&&$(D[M],M,D)!==!1;);return D}function M2(D,$){for(var M=D==null?0:D.length;M--&&$(D[M],M,D)!==!1;);return D}function Hm(D,$){for(var M=-1,ve=D==null?0:D.length;++M<ve;)if(!$(D[M],M,D))return!1;return!0}function Kr(D,$){for(var M=-1,ve=D==null?0:D.length,Fe=0,at=[];++M<ve;){var Vt=D[M];$(Vt,M,D)&&(at[Fe++]=Vt)}return at}function Aa(D,$){var M=D==null?0:D.length;return!!M&&so(D,$,0)>-1}function uc(D,$,M){for(var ve=-1,Fe=D==null?0:D.length;++ve<Fe;)if(M($,D[ve]))return!0;return!1}function bt(D,$){for(var M=-1,ve=D==null?0:D.length,Fe=Array(ve);++M<ve;)Fe[M]=$(D[M],M,D);return Fe}function Xr(D,$){for(var M=-1,ve=$.length,Fe=D.length;++M<ve;)D[Fe+M]=$[M];return D}function cc(D,$,M,ve){var Fe=-1,at=D==null?0:D.length;for(ve&&at&&(M=D[++Fe]);++Fe<at;)M=$(M,D[Fe],Fe,D);return M}function k2(D,$,M,ve){var Fe=D==null?0:D.length;for(ve&&Fe&&(M=D[--Fe]);Fe--;)M=$(M,D[Fe],Fe,D);return M}function dc(D,$){for(var M=-1,ve=D==null?0:D.length;++M<ve;)if($(D[M],M,D))return!0;return!1}var V2=fc("length");function L2(D){return D.split("")}function $2(D){return D.match(GP)||[]}function Wm(D,$,M){var ve;return M(D,function(Fe,at,Vt){if($(Fe,at,Vt))return ve=at,!1}),ve}function Ra(D,$,M,ve){for(var Fe=D.length,at=M+(ve?1:-1);ve?at--:++at<Fe;)if($(D[at],at,D))return at;return-1}function so(D,$,M){return $===$?Y2(D,$,M):Ra(D,jm,M)}function F2(D,$,M,ve){for(var Fe=M-1,at=D.length;++Fe<at;)if(ve(D[Fe],$))return Fe;return-1}function jm(D){return D!==D}function Gm(D,$){var M=D==null?0:D.length;return M?pc(D,$)/M:ee}function fc(D){return function($){return $==null?s:$[D]}}function hc(D){return function($){return D==null?s:D[$]}}function zm(D,$,M,ve,Fe){return Fe(D,function(at,Vt,pt){M=ve?(ve=!1,at):$(M,at,Vt,pt)}),M}function U2(D,$){var M=D.length;for(D.sort($);M--;)D[M]=D[M].value;return D}function pc(D,$){for(var M,ve=-1,Fe=D.length;++ve<Fe;){var at=$(D[ve]);at!==s&&(M=M===s?at:M+at)}return M}function mc(D,$){for(var M=-1,ve=Array(D);++M<D;)ve[M]=$(M);return ve}function B2(D,$){return bt($,function(M){return[M,D[M]]})}function Km(D){return D&&D.slice(0,Qm(D)+1).replace(tc,"")}function ms(D){return function($){return D($)}}function gc(D,$){return bt($,function(M){return D[M]})}function ni(D,$){return D.has($)}function Xm(D,$){for(var M=-1,ve=D.length;++M<ve&&so($,D[M],0)>-1;);return M}function Ym(D,$){for(var M=D.length;M--&&so($,D[M],0)>-1;);return M}function q2(D,$){for(var M=D.length,ve=0;M--;)D[M]===$&&++ve;return ve}var H2=hc(S2),W2=hc(I2);function j2(D){return"\\"+N2[D]}function G2(D,$){return D==null?s:D[$]}function ro(D){return C2.test(D)}function z2(D){return w2.test(D)}function K2(D){for(var $,M=[];!($=D.next()).done;)M.push($.value);return M}function _c(D){var $=-1,M=Array(D.size);return D.forEach(function(ve,Fe){M[++$]=[Fe,ve]}),M}function Jm(D,$){return function(M){return D($(M))}}function Yr(D,$){for(var M=-1,ve=D.length,Fe=0,at=[];++M<ve;){var Vt=D[M];(Vt===$||Vt===h)&&(D[M]=h,at[Fe++]=M)}return at}function Pa(D){var $=-1,M=Array(D.size);return D.forEach(function(ve){M[++$]=ve}),M}function X2(D){var $=-1,M=Array(D.size);return D.forEach(function(ve){M[++$]=[ve,ve]}),M}function Y2(D,$,M){for(var ve=M-1,Fe=D.length;++ve<Fe;)if(D[ve]===$)return ve;return-1}function J2(D,$,M){for(var ve=M+1;ve--;)if(D[ve]===$)return ve;return ve}function no(D){return ro(D)?Z2(D):V2(D)}function qs(D){return ro(D)?eM(D):L2(D)}function Qm(D){for(var $=D.length;$--&&qP.test(D.charAt($)););return $}var Q2=hc(D2);function Z2(D){for(var $=ic.lastIndex=0;ic.test(D);)++$;return $}function eM(D){return D.match(ic)||[]}function tM(D){return D.match(E2)||[]}var sM=function D($){$=$==null?Ht:oo.defaults(Ht.Object(),$,oo.pick(Ht,O2));var M=$.Array,ve=$.Date,Fe=$.Error,at=$.Function,Vt=$.Math,pt=$.Object,vc=$.RegExp,rM=$.String,Os=$.TypeError,Ma=M.prototype,nM=at.prototype,io=pt.prototype,ka=$["__core-js_shared__"],Va=nM.toString,ht=io.hasOwnProperty,oM=0,Zm=function(){var r=/[^.]+$/.exec(ka&&ka.keys&&ka.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),La=io.toString,iM=Va.call(pt),aM=Ht._,lM=vc("^"+Va.call(ht).replace(ec,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),$a=Vm?$.Buffer:s,Jr=$.Symbol,Fa=$.Uint8Array,eg=$a?$a.allocUnsafe:s,Ua=Jm(pt.getPrototypeOf,pt),tg=pt.create,sg=io.propertyIsEnumerable,Ba=Ma.splice,rg=Jr?Jr.isConcatSpreadable:s,oi=Jr?Jr.iterator:s,Sn=Jr?Jr.toStringTag:s,qa=function(){try{var r=An(pt,"defineProperty");return r({},"",{}),r}catch{}}(),uM=$.clearTimeout!==Ht.clearTimeout&&$.clearTimeout,cM=ve&&ve.now!==Ht.Date.now&&ve.now,dM=$.setTimeout!==Ht.setTimeout&&$.setTimeout,Ha=Vt.ceil,Wa=Vt.floor,yc=pt.getOwnPropertySymbols,fM=$a?$a.isBuffer:s,ng=$.isFinite,hM=Ma.join,pM=Jm(pt.keys,pt),Lt=Vt.max,Gt=Vt.min,mM=ve.now,gM=$.parseInt,og=Vt.random,_M=Ma.reverse,bc=An($,"DataView"),ii=An($,"Map"),Ec=An($,"Promise"),ao=An($,"Set"),ai=An($,"WeakMap"),li=An(pt,"create"),ja=ai&&new ai,lo={},vM=Rn(bc),yM=Rn(ii),bM=Rn(Ec),EM=Rn(ao),CM=Rn(ai),Ga=Jr?Jr.prototype:s,ui=Ga?Ga.valueOf:s,ig=Ga?Ga.toString:s;function y(r){if(Ct(r)&&!qe(r)&&!(r instanceof Qe)){if(r instanceof xs)return r;if(ht.call(r,"__wrapped__"))return a_(r)}return new xs(r)}var uo=function(){function r(){}return function(o){if(!Et(o))return{};if(tg)return tg(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function za(){}function xs(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}y.templateSettings={escape:VP,evaluate:LP,interpolate:mm,variable:"",imports:{_:y}},y.prototype=za.prototype,y.prototype.constructor=y,xs.prototype=uo(za.prototype),xs.prototype.constructor=xs;function Qe(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=_e,this.__views__=[]}function wM(){var r=new Qe(this.__wrapped__);return r.__actions__=as(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=as(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=as(this.__views__),r}function OM(){if(this.__filtered__){var r=new Qe(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function xM(){var r=this.__wrapped__.value(),o=this.__dir__,l=qe(r),d=o<0,_=l?r.length:0,b=Lk(0,_,this.__views__),w=b.start,I=b.end,T=I-w,U=d?I:w-1,B=this.__iteratees__,K=B.length,he=0,Oe=Gt(T,this.__takeCount__);if(!l||!d&&_==T&&Oe==T)return Tg(r,this.__actions__);var Pe=[];e:for(;T--&&he<Oe;){U+=o;for(var Ge=-1,Me=r[U];++Ge<K;){var Je=B[Ge],et=Je.iteratee,vs=Je.type,os=et(Me);if(vs==Y)Me=os;else if(!os){if(vs==G)continue e;break e}}Pe[he++]=Me}return Pe}Qe.prototype=uo(za.prototype),Qe.prototype.constructor=Qe;function In(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function SM(){this.__data__=li?li(null):{},this.size=0}function IM(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function DM(r){var o=this.__data__;if(li){var l=o[r];return l===f?s:l}return ht.call(o,r)?o[r]:s}function NM(r){var o=this.__data__;return li?o[r]!==s:ht.call(o,r)}function TM(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=li&&o===s?f:o,this}In.prototype.clear=SM,In.prototype.delete=IM,In.prototype.get=DM,In.prototype.has=NM,In.prototype.set=TM;function Sr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function AM(){this.__data__=[],this.size=0}function RM(r){var o=this.__data__,l=Ka(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():Ba.call(o,l,1),--this.size,!0}function PM(r){var o=this.__data__,l=Ka(o,r);return l<0?s:o[l][1]}function MM(r){return Ka(this.__data__,r)>-1}function kM(r,o){var l=this.__data__,d=Ka(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}Sr.prototype.clear=AM,Sr.prototype.delete=RM,Sr.prototype.get=PM,Sr.prototype.has=MM,Sr.prototype.set=kM;function Ir(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function VM(){this.size=0,this.__data__={hash:new In,map:new(ii||Sr),string:new In}}function LM(r){var o=il(this,r).delete(r);return this.size-=o?1:0,o}function $M(r){return il(this,r).get(r)}function FM(r){return il(this,r).has(r)}function UM(r,o){var l=il(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}Ir.prototype.clear=VM,Ir.prototype.delete=LM,Ir.prototype.get=$M,Ir.prototype.has=FM,Ir.prototype.set=UM;function Dn(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new Ir;++o<l;)this.add(r[o])}function BM(r){return this.__data__.set(r,f),this}function qM(r){return this.__data__.has(r)}Dn.prototype.add=Dn.prototype.push=BM,Dn.prototype.has=qM;function Hs(r){var o=this.__data__=new Sr(r);this.size=o.size}function HM(){this.__data__=new Sr,this.size=0}function WM(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function jM(r){return this.__data__.get(r)}function GM(r){return this.__data__.has(r)}function zM(r,o){var l=this.__data__;if(l instanceof Sr){var d=l.__data__;if(!ii||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new Ir(d)}return l.set(r,o),this.size=l.size,this}Hs.prototype.clear=HM,Hs.prototype.delete=WM,Hs.prototype.get=jM,Hs.prototype.has=GM,Hs.prototype.set=zM;function ag(r,o){var l=qe(r),d=!l&&Pn(r),_=!l&&!d&&sn(r),b=!l&&!d&&!_&&po(r),w=l||d||_||b,I=w?mc(r.length,rM):[],T=I.length;for(var U in r)(o||ht.call(r,U))&&!(w&&(U=="length"||_&&(U=="offset"||U=="parent")||b&&(U=="buffer"||U=="byteLength"||U=="byteOffset")||Ar(U,T)))&&I.push(U);return I}function lg(r){var o=r.length;return o?r[Rc(0,o-1)]:s}function KM(r,o){return al(as(r),Nn(o,0,r.length))}function XM(r){return al(as(r))}function Cc(r,o,l){(l!==s&&!Ws(r[o],l)||l===s&&!(o in r))&&Dr(r,o,l)}function ci(r,o,l){var d=r[o];(!(ht.call(r,o)&&Ws(d,l))||l===s&&!(o in r))&&Dr(r,o,l)}function Ka(r,o){for(var l=r.length;l--;)if(Ws(r[l][0],o))return l;return-1}function YM(r,o,l,d){return Qr(r,function(_,b,w){o(d,_,l(_),w)}),d}function ug(r,o){return r&&ir(o,Ut(o),r)}function JM(r,o){return r&&ir(o,us(o),r)}function Dr(r,o,l){o=="__proto__"&&qa?qa(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function wc(r,o){for(var l=-1,d=o.length,_=M(d),b=r==null;++l<d;)_[l]=b?s:rd(r,o[l]);return _}function Nn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Ss(r,o,l,d,_,b){var w,I=o&p,T=o&v,U=o&C;if(l&&(w=_?l(r,d,_,b):l(r)),w!==s)return w;if(!Et(r))return r;var B=qe(r);if(B){if(w=Fk(r),!I)return as(r,w)}else{var K=zt(r),he=K==ft||K==Be;if(sn(r))return Pg(r,I);if(K==F||K==ot||he&&!_){if(w=T||he?{}:Qg(r),!I)return T?Dk(r,JM(w,r)):Ik(r,ug(w,r))}else{if(!mt[K])return _?r:{};w=Uk(r,K,I)}}b||(b=new Hs);var Oe=b.get(r);if(Oe)return Oe;b.set(r,w),I_(r)?r.forEach(function(Me){w.add(Ss(Me,o,l,Me,r,b))}):x_(r)&&r.forEach(function(Me,Je){w.set(Je,Ss(Me,o,l,Je,r,b))});var Pe=U?T?Hc:qc:T?us:Ut,Ge=B?s:Pe(r);return ws(Ge||r,function(Me,Je){Ge&&(Je=Me,Me=r[Je]),ci(w,Je,Ss(Me,o,l,Je,r,b))}),w}function QM(r){var o=Ut(r);return function(l){return cg(l,r,o)}}function cg(r,o,l){var d=l.length;if(r==null)return!d;for(r=pt(r);d--;){var _=l[d],b=o[_],w=r[_];if(w===s&&!(_ in r)||!b(w))return!1}return!0}function dg(r,o,l){if(typeof r!="function")throw new Os(u);return _i(function(){r.apply(s,l)},o)}function di(r,o,l,d){var _=-1,b=Aa,w=!0,I=r.length,T=[],U=o.length;if(!I)return T;l&&(o=bt(o,ms(l))),d?(b=uc,w=!1):o.length>=n&&(b=ni,w=!1,o=new Dn(o));e:for(;++_<I;){var B=r[_],K=l==null?B:l(B);if(B=d||B!==0?B:0,w&&K===K){for(var he=U;he--;)if(o[he]===K)continue e;T.push(B)}else b(o,K,d)||T.push(B)}return T}var Qr=$g(or),fg=$g(xc,!0);function ZM(r,o){var l=!0;return Qr(r,function(d,_,b){return l=!!o(d,_,b),l}),l}function Xa(r,o,l){for(var d=-1,_=r.length;++d<_;){var b=r[d],w=o(b);if(w!=null&&(I===s?w===w&&!_s(w):l(w,I)))var I=w,T=b}return T}function ek(r,o,l,d){var _=r.length;for(l=We(l),l<0&&(l=-l>_?0:_+l),d=d===s||d>_?_:We(d),d<0&&(d+=_),d=l>d?0:N_(d);l<d;)r[l++]=o;return r}function hg(r,o){var l=[];return Qr(r,function(d,_,b){o(d,_,b)&&l.push(d)}),l}function Wt(r,o,l,d,_){var b=-1,w=r.length;for(l||(l=qk),_||(_=[]);++b<w;){var I=r[b];o>0&&l(I)?o>1?Wt(I,o-1,l,d,_):Xr(_,I):d||(_[_.length]=I)}return _}var Oc=Fg(),pg=Fg(!0);function or(r,o){return r&&Oc(r,o,Ut)}function xc(r,o){return r&&pg(r,o,Ut)}function Ya(r,o){return Kr(o,function(l){return Rr(r[l])})}function Tn(r,o){o=en(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[ar(o[l++])];return l&&l==d?r:s}function mg(r,o,l){var d=o(r);return qe(r)?d:Xr(d,l(r))}function rs(r){return r==null?r===s?Ie:k:Sn&&Sn in pt(r)?Vk(r):Xk(r)}function Sc(r,o){return r>o}function tk(r,o){return r!=null&&ht.call(r,o)}function sk(r,o){return r!=null&&o in pt(r)}function rk(r,o,l){return r>=Gt(o,l)&&r<Lt(o,l)}function Ic(r,o,l){for(var d=l?uc:Aa,_=r[0].length,b=r.length,w=b,I=M(b),T=1/0,U=[];w--;){var B=r[w];w&&o&&(B=bt(B,ms(o))),T=Gt(B.length,T),I[w]=!l&&(o||_>=120&&B.length>=120)?new Dn(w&&B):s}B=r[0];var K=-1,he=I[0];e:for(;++K<_&&U.length<T;){var Oe=B[K],Pe=o?o(Oe):Oe;if(Oe=l||Oe!==0?Oe:0,!(he?ni(he,Pe):d(U,Pe,l))){for(w=b;--w;){var Ge=I[w];if(!(Ge?ni(Ge,Pe):d(r[w],Pe,l)))continue e}he&&he.push(Pe),U.push(Oe)}}return U}function nk(r,o,l,d){return or(r,function(_,b,w){o(d,l(_),b,w)}),d}function fi(r,o,l){o=en(o,r),r=s_(r,o);var d=r==null?r:r[ar(Ds(o))];return d==null?s:ps(d,r,l)}function gg(r){return Ct(r)&&rs(r)==ot}function ok(r){return Ct(r)&&rs(r)==Ne}function ik(r){return Ct(r)&&rs(r)==ce}function hi(r,o,l,d,_){return r===o?!0:r==null||o==null||!Ct(r)&&!Ct(o)?r!==r&&o!==o:ak(r,o,l,d,hi,_)}function ak(r,o,l,d,_,b){var w=qe(r),I=qe(o),T=w?Mt:zt(r),U=I?Mt:zt(o);T=T==ot?F:T,U=U==ot?F:U;var B=T==F,K=U==F,he=T==U;if(he&&sn(r)){if(!sn(o))return!1;w=!0,B=!1}if(he&&!B)return b||(b=new Hs),w||po(r)?Xg(r,o,l,d,_,b):Mk(r,o,T,l,d,_,b);if(!(l&P)){var Oe=B&&ht.call(r,"__wrapped__"),Pe=K&&ht.call(o,"__wrapped__");if(Oe||Pe){var Ge=Oe?r.value():r,Me=Pe?o.value():o;return b||(b=new Hs),_(Ge,Me,l,d,b)}}return he?(b||(b=new Hs),kk(r,o,l,d,_,b)):!1}function lk(r){return Ct(r)&&zt(r)==E}function Dc(r,o,l,d){var _=l.length,b=_,w=!d;if(r==null)return!b;for(r=pt(r);_--;){var I=l[_];if(w&&I[2]?I[1]!==r[I[0]]:!(I[0]in r))return!1}for(;++_<b;){I=l[_];var T=I[0],U=r[T],B=I[1];if(w&&I[2]){if(U===s&&!(T in r))return!1}else{var K=new Hs;if(d)var he=d(U,B,T,r,o,K);if(!(he===s?hi(B,U,P|A,d,K):he))return!1}}return!0}function _g(r){if(!Et(r)||Wk(r))return!1;var o=Rr(r)?lM:QP;return o.test(Rn(r))}function uk(r){return Ct(r)&&rs(r)==ae}function ck(r){return Ct(r)&&zt(r)==Q}function dk(r){return Ct(r)&&hl(r.length)&&!!_t[rs(r)]}function vg(r){return typeof r=="function"?r:r==null?cs:typeof r=="object"?qe(r)?Eg(r[0],r[1]):bg(r):U_(r)}function Nc(r){if(!gi(r))return pM(r);var o=[];for(var l in pt(r))ht.call(r,l)&&l!="constructor"&&o.push(l);return o}function fk(r){if(!Et(r))return Kk(r);var o=gi(r),l=[];for(var d in r)d=="constructor"&&(o||!ht.call(r,d))||l.push(d);return l}function Tc(r,o){return r<o}function yg(r,o){var l=-1,d=ls(r)?M(r.length):[];return Qr(r,function(_,b,w){d[++l]=o(_,b,w)}),d}function bg(r){var o=jc(r);return o.length==1&&o[0][2]?e_(o[0][0],o[0][1]):function(l){return l===r||Dc(l,r,o)}}function Eg(r,o){return zc(r)&&Zg(o)?e_(ar(r),o):function(l){var d=rd(l,r);return d===s&&d===o?nd(l,r):hi(o,d,P|A)}}function Ja(r,o,l,d,_){r!==o&&Oc(o,function(b,w){if(_||(_=new Hs),Et(b))hk(r,o,w,l,Ja,d,_);else{var I=d?d(Xc(r,w),b,w+"",r,o,_):s;I===s&&(I=b),Cc(r,w,I)}},us)}function hk(r,o,l,d,_,b,w){var I=Xc(r,l),T=Xc(o,l),U=w.get(T);if(U){Cc(r,l,U);return}var B=b?b(I,T,l+"",r,o,w):s,K=B===s;if(K){var he=qe(T),Oe=!he&&sn(T),Pe=!he&&!Oe&&po(T);B=T,he||Oe||Pe?qe(I)?B=I:xt(I)?B=as(I):Oe?(K=!1,B=Pg(T,!0)):Pe?(K=!1,B=Mg(T,!0)):B=[]:vi(T)||Pn(T)?(B=I,Pn(I)?B=T_(I):(!Et(I)||Rr(I))&&(B=Qg(T))):K=!1}K&&(w.set(T,B),_(B,T,d,b,w),w.delete(T)),Cc(r,l,B)}function Cg(r,o){var l=r.length;if(l)return o+=o<0?l:0,Ar(o,l)?r[o]:s}function wg(r,o,l){o.length?o=bt(o,function(b){return qe(b)?function(w){return Tn(w,b.length===1?b[0]:b)}:b}):o=[cs];var d=-1;o=bt(o,ms(Te()));var _=yg(r,function(b,w,I){var T=bt(o,function(U){return U(b)});return{criteria:T,index:++d,value:b}});return U2(_,function(b,w){return Sk(b,w,l)})}function pk(r,o){return Og(r,o,function(l,d){return nd(r,d)})}function Og(r,o,l){for(var d=-1,_=o.length,b={};++d<_;){var w=o[d],I=Tn(r,w);l(I,w)&&pi(b,en(w,r),I)}return b}function mk(r){return function(o){return Tn(o,r)}}function Ac(r,o,l,d){var _=d?F2:so,b=-1,w=o.length,I=r;for(r===o&&(o=as(o)),l&&(I=bt(r,ms(l)));++b<w;)for(var T=0,U=o[b],B=l?l(U):U;(T=_(I,B,T,d))>-1;)I!==r&&Ba.call(I,T,1),Ba.call(r,T,1);return r}function xg(r,o){for(var l=r?o.length:0,d=l-1;l--;){var _=o[l];if(l==d||_!==b){var b=_;Ar(_)?Ba.call(r,_,1):kc(r,_)}}return r}function Rc(r,o){return r+Wa(og()*(o-r+1))}function gk(r,o,l,d){for(var _=-1,b=Lt(Ha((o-r)/(l||1)),0),w=M(b);b--;)w[d?b:++_]=r,r+=l;return w}function Pc(r,o){var l="";if(!r||o<1||o>J)return l;do o%2&&(l+=r),o=Wa(o/2),o&&(r+=r);while(o);return l}function Xe(r,o){return Yc(t_(r,o,cs),r+"")}function _k(r){return lg(mo(r))}function vk(r,o){var l=mo(r);return al(l,Nn(o,0,l.length))}function pi(r,o,l,d){if(!Et(r))return r;o=en(o,r);for(var _=-1,b=o.length,w=b-1,I=r;I!=null&&++_<b;){var T=ar(o[_]),U=l;if(T==="__proto__"||T==="constructor"||T==="prototype")return r;if(_!=w){var B=I[T];U=d?d(B,T,I):s,U===s&&(U=Et(B)?B:Ar(o[_+1])?[]:{})}ci(I,T,U),I=I[T]}return r}var Sg=ja?function(r,o){return ja.set(r,o),r}:cs,yk=qa?function(r,o){return qa(r,"toString",{configurable:!0,enumerable:!1,value:id(o),writable:!0})}:cs;function bk(r){return al(mo(r))}function Is(r,o,l){var d=-1,_=r.length;o<0&&(o=-o>_?0:_+o),l=l>_?_:l,l<0&&(l+=_),_=o>l?0:l-o>>>0,o>>>=0;for(var b=M(_);++d<_;)b[d]=r[d+o];return b}function Ek(r,o){var l;return Qr(r,function(d,_,b){return l=o(d,_,b),!l}),!!l}function Qa(r,o,l){var d=0,_=r==null?d:r.length;if(typeof o=="number"&&o===o&&_<=Ye){for(;d<_;){var b=d+_>>>1,w=r[b];w!==null&&!_s(w)&&(l?w<=o:w<o)?d=b+1:_=b}return _}return Mc(r,o,cs,l)}function Mc(r,o,l,d){var _=0,b=r==null?0:r.length;if(b===0)return 0;o=l(o);for(var w=o!==o,I=o===null,T=_s(o),U=o===s;_<b;){var B=Wa((_+b)/2),K=l(r[B]),he=K!==s,Oe=K===null,Pe=K===K,Ge=_s(K);if(w)var Me=d||Pe;else U?Me=Pe&&(d||he):I?Me=Pe&&he&&(d||!Oe):T?Me=Pe&&he&&!Oe&&(d||!Ge):Oe||Ge?Me=!1:Me=d?K<=o:K<o;Me?_=B+1:b=B}return Gt(b,Se)}function Ig(r,o){for(var l=-1,d=r.length,_=0,b=[];++l<d;){var w=r[l],I=o?o(w):w;if(!l||!Ws(I,T)){var T=I;b[_++]=w===0?0:w}}return b}function Dg(r){return typeof r=="number"?r:_s(r)?ee:+r}function gs(r){if(typeof r=="string")return r;if(qe(r))return bt(r,gs)+"";if(_s(r))return ig?ig.call(r):"";var o=r+"";return o=="0"&&1/r==-j?"-0":o}function Zr(r,o,l){var d=-1,_=Aa,b=r.length,w=!0,I=[],T=I;if(l)w=!1,_=uc;else if(b>=n){var U=o?null:Rk(r);if(U)return Pa(U);w=!1,_=ni,T=new Dn}else T=o?[]:I;e:for(;++d<b;){var B=r[d],K=o?o(B):B;if(B=l||B!==0?B:0,w&&K===K){for(var he=T.length;he--;)if(T[he]===K)continue e;o&&T.push(K),I.push(B)}else _(T,K,l)||(T!==I&&T.push(K),I.push(B))}return I}function kc(r,o){return o=en(o,r),r=s_(r,o),r==null||delete r[ar(Ds(o))]}function Ng(r,o,l,d){return pi(r,o,l(Tn(r,o)),d)}function Za(r,o,l,d){for(var _=r.length,b=d?_:-1;(d?b--:++b<_)&&o(r[b],b,r););return l?Is(r,d?0:b,d?b+1:_):Is(r,d?b+1:0,d?_:b)}function Tg(r,o){var l=r;return l instanceof Qe&&(l=l.value()),cc(o,function(d,_){return _.func.apply(_.thisArg,Xr([d],_.args))},l)}function Vc(r,o,l){var d=r.length;if(d<2)return d?Zr(r[0]):[];for(var _=-1,b=M(d);++_<d;)for(var w=r[_],I=-1;++I<d;)I!=_&&(b[_]=di(b[_]||w,r[I],o,l));return Zr(Wt(b,1),o,l)}function Ag(r,o,l){for(var d=-1,_=r.length,b=o.length,w={};++d<_;){var I=d<b?o[d]:s;l(w,r[d],I)}return w}function Lc(r){return xt(r)?r:[]}function $c(r){return typeof r=="function"?r:cs}function en(r,o){return qe(r)?r:zc(r,o)?[r]:i_(dt(r))}var Ck=Xe;function tn(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:Is(r,o,l)}var Rg=uM||function(r){return Ht.clearTimeout(r)};function Pg(r,o){if(o)return r.slice();var l=r.length,d=eg?eg(l):new r.constructor(l);return r.copy(d),d}function Fc(r){var o=new r.constructor(r.byteLength);return new Fa(o).set(new Fa(r)),o}function wk(r,o){var l=o?Fc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function Ok(r){var o=new r.constructor(r.source,gm.exec(r));return o.lastIndex=r.lastIndex,o}function xk(r){return ui?pt(ui.call(r)):{}}function Mg(r,o){var l=o?Fc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function kg(r,o){if(r!==o){var l=r!==s,d=r===null,_=r===r,b=_s(r),w=o!==s,I=o===null,T=o===o,U=_s(o);if(!I&&!U&&!b&&r>o||b&&w&&T&&!I&&!U||d&&w&&T||!l&&T||!_)return 1;if(!d&&!b&&!U&&r<o||U&&l&&_&&!d&&!b||I&&l&&_||!w&&_||!T)return-1}return 0}function Sk(r,o,l){for(var d=-1,_=r.criteria,b=o.criteria,w=_.length,I=l.length;++d<w;){var T=kg(_[d],b[d]);if(T){if(d>=I)return T;var U=l[d];return T*(U=="desc"?-1:1)}}return r.index-o.index}function Vg(r,o,l,d){for(var _=-1,b=r.length,w=l.length,I=-1,T=o.length,U=Lt(b-w,0),B=M(T+U),K=!d;++I<T;)B[I]=o[I];for(;++_<w;)(K||_<b)&&(B[l[_]]=r[_]);for(;U--;)B[I++]=r[_++];return B}function Lg(r,o,l,d){for(var _=-1,b=r.length,w=-1,I=l.length,T=-1,U=o.length,B=Lt(b-I,0),K=M(B+U),he=!d;++_<B;)K[_]=r[_];for(var Oe=_;++T<U;)K[Oe+T]=o[T];for(;++w<I;)(he||_<b)&&(K[Oe+l[w]]=r[_++]);return K}function as(r,o){var l=-1,d=r.length;for(o||(o=M(d));++l<d;)o[l]=r[l];return o}function ir(r,o,l,d){var _=!l;l||(l={});for(var b=-1,w=o.length;++b<w;){var I=o[b],T=d?d(l[I],r[I],I,l,r):s;T===s&&(T=r[I]),_?Dr(l,I,T):ci(l,I,T)}return l}function Ik(r,o){return ir(r,Gc(r),o)}function Dk(r,o){return ir(r,Yg(r),o)}function el(r,o){return function(l,d){var _=qe(l)?P2:YM,b=o?o():{};return _(l,r,Te(d,2),b)}}function co(r){return Xe(function(o,l){var d=-1,_=l.length,b=_>1?l[_-1]:s,w=_>2?l[2]:s;for(b=r.length>3&&typeof b=="function"?(_--,b):s,w&&ns(l[0],l[1],w)&&(b=_<3?s:b,_=1),o=pt(o);++d<_;){var I=l[d];I&&r(o,I,d,b)}return o})}function $g(r,o){return function(l,d){if(l==null)return l;if(!ls(l))return r(l,d);for(var _=l.length,b=o?_:-1,w=pt(l);(o?b--:++b<_)&&d(w[b],b,w)!==!1;);return l}}function Fg(r){return function(o,l,d){for(var _=-1,b=pt(o),w=d(o),I=w.length;I--;){var T=w[r?I:++_];if(l(b[T],T,b)===!1)break}return o}}function Nk(r,o,l){var d=o&oe,_=mi(r);function b(){var w=this&&this!==Ht&&this instanceof b?_:r;return w.apply(d?l:this,arguments)}return b}function Ug(r){return function(o){o=dt(o);var l=ro(o)?qs(o):s,d=l?l[0]:o.charAt(0),_=l?tn(l,1).join(""):o.slice(1);return d[r]()+_}}function fo(r){return function(o){return cc($_(L_(o).replace(y2,"")),r,"")}}function mi(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=uo(r.prototype),d=r.apply(l,o);return Et(d)?d:l}}function Tk(r,o,l){var d=mi(r);function _(){for(var b=arguments.length,w=M(b),I=b,T=ho(_);I--;)w[I]=arguments[I];var U=b<3&&w[0]!==T&&w[b-1]!==T?[]:Yr(w,T);if(b-=U.length,b<l)return jg(r,o,tl,_.placeholder,s,w,U,s,s,l-b);var B=this&&this!==Ht&&this instanceof _?d:r;return ps(B,this,w)}return _}function Bg(r){return function(o,l,d){var _=pt(o);if(!ls(o)){var b=Te(l,3);o=Ut(o),l=function(I){return b(_[I],I,_)}}var w=r(o,l,d);return w>-1?_[b?o[w]:w]:s}}function qg(r){return Tr(function(o){var l=o.length,d=l,_=xs.prototype.thru;for(r&&o.reverse();d--;){var b=o[d];if(typeof b!="function")throw new Os(u);if(_&&!w&&ol(b)=="wrapper")var w=new xs([],!0)}for(d=w?d:l;++d<l;){b=o[d];var I=ol(b),T=I=="wrapper"?Wc(b):s;T&&Kc(T[0])&&T[1]==(xe|q|se|ke)&&!T[4].length&&T[9]==1?w=w[ol(T[0])].apply(w,T[3]):w=b.length==1&&Kc(b)?w[I]():w.thru(b)}return function(){var U=arguments,B=U[0];if(w&&U.length==1&&qe(B))return w.plant(B).value();for(var K=0,he=l?o[K].apply(this,U):B;++K<l;)he=o[K].call(this,he);return he}})}function tl(r,o,l,d,_,b,w,I,T,U){var B=o&xe,K=o&oe,he=o&W,Oe=o&(q|ye),Pe=o&de,Ge=he?s:mi(r);function Me(){for(var Je=arguments.length,et=M(Je),vs=Je;vs--;)et[vs]=arguments[vs];if(Oe)var os=ho(Me),ys=q2(et,os);if(d&&(et=Vg(et,d,_,Oe)),b&&(et=Lg(et,b,w,Oe)),Je-=ys,Oe&&Je<U){var St=Yr(et,os);return jg(r,o,tl,Me.placeholder,l,et,St,I,T,U-Je)}var js=K?l:this,Mr=he?js[r]:r;return Je=et.length,I?et=Yk(et,I):Pe&&Je>1&&et.reverse(),B&&T<Je&&(et.length=T),this&&this!==Ht&&this instanceof Me&&(Mr=Ge||mi(Mr)),Mr.apply(js,et)}return Me}function Hg(r,o){return function(l,d){return nk(l,r,o(d),{})}}function sl(r,o){return function(l,d){var _;if(l===s&&d===s)return o;if(l!==s&&(_=l),d!==s){if(_===s)return d;typeof l=="string"||typeof d=="string"?(l=gs(l),d=gs(d)):(l=Dg(l),d=Dg(d)),_=r(l,d)}return _}}function Uc(r){return Tr(function(o){return o=bt(o,ms(Te())),Xe(function(l){var d=this;return r(o,function(_){return ps(_,d,l)})})})}function rl(r,o){o=o===s?" ":gs(o);var l=o.length;if(l<2)return l?Pc(o,r):o;var d=Pc(o,Ha(r/no(o)));return ro(o)?tn(qs(d),0,r).join(""):d.slice(0,r)}function Ak(r,o,l,d){var _=o&oe,b=mi(r);function w(){for(var I=-1,T=arguments.length,U=-1,B=d.length,K=M(B+T),he=this&&this!==Ht&&this instanceof w?b:r;++U<B;)K[U]=d[U];for(;T--;)K[U++]=arguments[++I];return ps(he,_?l:this,K)}return w}function Wg(r){return function(o,l,d){return d&&typeof d!="number"&&ns(o,l,d)&&(l=d=s),o=Pr(o),l===s?(l=o,o=0):l=Pr(l),d=d===s?o<l?1:-1:Pr(d),gk(o,l,d,r)}}function nl(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Ns(o),l=Ns(l)),r(o,l)}}function jg(r,o,l,d,_,b,w,I,T,U){var B=o&q,K=B?w:s,he=B?s:w,Oe=B?b:s,Pe=B?s:b;o|=B?se:be,o&=~(B?be:se),o&ie||(o&=~(oe|W));var Ge=[r,o,_,Oe,K,Pe,he,I,T,U],Me=l.apply(s,Ge);return Kc(r)&&r_(Me,Ge),Me.placeholder=d,n_(Me,r,o)}function Bc(r){var o=Vt[r];return function(l,d){if(l=Ns(l),d=d==null?0:Gt(We(d),292),d&&ng(l)){var _=(dt(l)+"e").split("e"),b=o(_[0]+"e"+(+_[1]+d));return _=(dt(b)+"e").split("e"),+(_[0]+"e"+(+_[1]-d))}return o(l)}}var Rk=ao&&1/Pa(new ao([,-0]))[1]==j?function(r){return new ao(r)}:ud;function Gg(r){return function(o){var l=zt(o);return l==E?_c(o):l==Q?X2(o):B2(o,r(o))}}function Nr(r,o,l,d,_,b,w,I){var T=o&W;if(!T&&typeof r!="function")throw new Os(u);var U=d?d.length:0;if(U||(o&=~(se|be),d=_=s),w=w===s?w:Lt(We(w),0),I=I===s?I:We(I),U-=_?_.length:0,o&be){var B=d,K=_;d=_=s}var he=T?s:Wc(r),Oe=[r,o,l,d,_,B,K,b,w,I];if(he&&zk(Oe,he),r=Oe[0],o=Oe[1],l=Oe[2],d=Oe[3],_=Oe[4],I=Oe[9]=Oe[9]===s?T?0:r.length:Lt(Oe[9]-U,0),!I&&o&(q|ye)&&(o&=~(q|ye)),!o||o==oe)var Pe=Nk(r,o,l);else o==q||o==ye?Pe=Tk(r,o,I):(o==se||o==(oe|se))&&!_.length?Pe=Ak(r,o,l,d):Pe=tl.apply(s,Oe);var Ge=he?Sg:r_;return n_(Ge(Pe,Oe),r,o)}function zg(r,o,l,d){return r===s||Ws(r,io[l])&&!ht.call(d,l)?o:r}function Kg(r,o,l,d,_,b){return Et(r)&&Et(o)&&(b.set(o,r),Ja(r,o,s,Kg,b),b.delete(o)),r}function Pk(r){return vi(r)?s:r}function Xg(r,o,l,d,_,b){var w=l&P,I=r.length,T=o.length;if(I!=T&&!(w&&T>I))return!1;var U=b.get(r),B=b.get(o);if(U&&B)return U==o&&B==r;var K=-1,he=!0,Oe=l&A?new Dn:s;for(b.set(r,o),b.set(o,r);++K<I;){var Pe=r[K],Ge=o[K];if(d)var Me=w?d(Ge,Pe,K,o,r,b):d(Pe,Ge,K,r,o,b);if(Me!==s){if(Me)continue;he=!1;break}if(Oe){if(!dc(o,function(Je,et){if(!ni(Oe,et)&&(Pe===Je||_(Pe,Je,l,d,b)))return Oe.push(et)})){he=!1;break}}else if(!(Pe===Ge||_(Pe,Ge,l,d,b))){he=!1;break}}return b.delete(r),b.delete(o),he}function Mk(r,o,l,d,_,b,w){switch(l){case He:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Ne:return!(r.byteLength!=o.byteLength||!b(new Fa(r),new Fa(o)));case fe:case ce:case O:return Ws(+r,+o);case Ue:return r.name==o.name&&r.message==o.message;case ae:case re:return r==o+"";case E:var I=_c;case Q:var T=d&P;if(I||(I=Pa),r.size!=o.size&&!T)return!1;var U=w.get(r);if(U)return U==o;d|=A,w.set(r,o);var B=Xg(I(r),I(o),d,_,b,w);return w.delete(r),B;case X:if(ui)return ui.call(r)==ui.call(o)}return!1}function kk(r,o,l,d,_,b){var w=l&P,I=qc(r),T=I.length,U=qc(o),B=U.length;if(T!=B&&!w)return!1;for(var K=T;K--;){var he=I[K];if(!(w?he in o:ht.call(o,he)))return!1}var Oe=b.get(r),Pe=b.get(o);if(Oe&&Pe)return Oe==o&&Pe==r;var Ge=!0;b.set(r,o),b.set(o,r);for(var Me=w;++K<T;){he=I[K];var Je=r[he],et=o[he];if(d)var vs=w?d(et,Je,he,o,r,b):d(Je,et,he,r,o,b);if(!(vs===s?Je===et||_(Je,et,l,d,b):vs)){Ge=!1;break}Me||(Me=he=="constructor")}if(Ge&&!Me){var os=r.constructor,ys=o.constructor;os!=ys&&"constructor"in r&&"constructor"in o&&!(typeof os=="function"&&os instanceof os&&typeof ys=="function"&&ys instanceof ys)&&(Ge=!1)}return b.delete(r),b.delete(o),Ge}function Tr(r){return Yc(t_(r,s,c_),r+"")}function qc(r){return mg(r,Ut,Gc)}function Hc(r){return mg(r,us,Yg)}var Wc=ja?function(r){return ja.get(r)}:ud;function ol(r){for(var o=r.name+"",l=lo[o],d=ht.call(lo,o)?l.length:0;d--;){var _=l[d],b=_.func;if(b==null||b==r)return _.name}return o}function ho(r){var o=ht.call(y,"placeholder")?y:r;return o.placeholder}function Te(){var r=y.iteratee||ad;return r=r===ad?vg:r,arguments.length?r(arguments[0],arguments[1]):r}function il(r,o){var l=r.__data__;return Hk(o)?l[typeof o=="string"?"string":"hash"]:l.map}function jc(r){for(var o=Ut(r),l=o.length;l--;){var d=o[l],_=r[d];o[l]=[d,_,Zg(_)]}return o}function An(r,o){var l=G2(r,o);return _g(l)?l:s}function Vk(r){var o=ht.call(r,Sn),l=r[Sn];try{r[Sn]=s;var d=!0}catch{}var _=La.call(r);return d&&(o?r[Sn]=l:delete r[Sn]),_}var Gc=yc?function(r){return r==null?[]:(r=pt(r),Kr(yc(r),function(o){return sg.call(r,o)}))}:cd,Yg=yc?function(r){for(var o=[];r;)Xr(o,Gc(r)),r=Ua(r);return o}:cd,zt=rs;(bc&&zt(new bc(new ArrayBuffer(1)))!=He||ii&&zt(new ii)!=E||Ec&&zt(Ec.resolve())!=H||ao&&zt(new ao)!=Q||ai&&zt(new ai)!=le)&&(zt=function(r){var o=rs(r),l=o==F?r.constructor:s,d=l?Rn(l):"";if(d)switch(d){case vM:return He;case yM:return E;case bM:return H;case EM:return Q;case CM:return le}return o});function Lk(r,o,l){for(var d=-1,_=l.length;++d<_;){var b=l[d],w=b.size;switch(b.type){case"drop":r+=w;break;case"dropRight":o-=w;break;case"take":o=Gt(o,r+w);break;case"takeRight":r=Lt(r,o-w);break}}return{start:r,end:o}}function $k(r){var o=r.match(WP);return o?o[1].split(jP):[]}function Jg(r,o,l){o=en(o,r);for(var d=-1,_=o.length,b=!1;++d<_;){var w=ar(o[d]);if(!(b=r!=null&&l(r,w)))break;r=r[w]}return b||++d!=_?b:(_=r==null?0:r.length,!!_&&hl(_)&&Ar(w,_)&&(qe(r)||Pn(r)))}function Fk(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&ht.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Qg(r){return typeof r.constructor=="function"&&!gi(r)?uo(Ua(r)):{}}function Uk(r,o,l){var d=r.constructor;switch(o){case Ne:return Fc(r);case fe:case ce:return new d(+r);case He:return wk(r,l);case it:case st:case Ft:case Dt:case ss:case qt:case xr:case eo:case kt:return Mg(r,l);case E:return new d;case O:case re:return new d(r);case ae:return Ok(r);case Q:return new d;case X:return xk(r)}}function Bk(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(HP,`{
/* [wrapped with `+o+`] */
`)}function qk(r){return qe(r)||Pn(r)||!!(rg&&r&&r[rg])}function Ar(r,o){var l=typeof r;return o=o??J,!!o&&(l=="number"||l!="symbol"&&e2.test(r))&&r>-1&&r%1==0&&r<o}function ns(r,o,l){if(!Et(l))return!1;var d=typeof o;return(d=="number"?ls(l)&&Ar(o,l.length):d=="string"&&o in l)?Ws(l[o],r):!1}function zc(r,o){if(qe(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||_s(r)?!0:FP.test(r)||!$P.test(r)||o!=null&&r in pt(o)}function Hk(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function Kc(r){var o=ol(r),l=y[o];if(typeof l!="function"||!(o in Qe.prototype))return!1;if(r===l)return!0;var d=Wc(l);return!!d&&r===d[0]}function Wk(r){return!!Zm&&Zm in r}var jk=ka?Rr:dd;function gi(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||io;return r===l}function Zg(r){return r===r&&!Et(r)}function e_(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in pt(l))}}function Gk(r){var o=dl(r,function(d){return l.size===g&&l.clear(),d}),l=o.cache;return o}function zk(r,o){var l=r[1],d=o[1],_=l|d,b=_<(oe|W|xe),w=d==xe&&l==q||d==xe&&l==ke&&r[7].length<=o[8]||d==(xe|ke)&&o[7].length<=o[8]&&l==q;if(!(b||w))return r;d&oe&&(r[2]=o[2],_|=l&oe?0:ie);var I=o[3];if(I){var T=r[3];r[3]=T?Vg(T,I,o[4]):I,r[4]=T?Yr(r[3],h):o[4]}return I=o[5],I&&(T=r[5],r[5]=T?Lg(T,I,o[6]):I,r[6]=T?Yr(r[5],h):o[6]),I=o[7],I&&(r[7]=I),d&xe&&(r[8]=r[8]==null?o[8]:Gt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=_,r}function Kk(r){var o=[];if(r!=null)for(var l in pt(r))o.push(l);return o}function Xk(r){return La.call(r)}function t_(r,o,l){return o=Lt(o===s?r.length-1:o,0),function(){for(var d=arguments,_=-1,b=Lt(d.length-o,0),w=M(b);++_<b;)w[_]=d[o+_];_=-1;for(var I=M(o+1);++_<o;)I[_]=d[_];return I[o]=l(w),ps(r,this,I)}}function s_(r,o){return o.length<2?r:Tn(r,Is(o,0,-1))}function Yk(r,o){for(var l=r.length,d=Gt(o.length,l),_=as(r);d--;){var b=o[d];r[d]=Ar(b,l)?_[b]:s}return r}function Xc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var r_=o_(Sg),_i=dM||function(r,o){return Ht.setTimeout(r,o)},Yc=o_(yk);function n_(r,o,l){var d=o+"";return Yc(r,Bk(d,Jk($k(d),l)))}function o_(r){var o=0,l=0;return function(){var d=mM(),_=R-(d-l);if(l=d,_>0){if(++o>=ue)return arguments[0]}else o=0;return r.apply(s,arguments)}}function al(r,o){var l=-1,d=r.length,_=d-1;for(o=o===s?d:o;++l<o;){var b=Rc(l,_),w=r[b];r[b]=r[l],r[l]=w}return r.length=o,r}var i_=Gk(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(UP,function(l,d,_,b){o.push(_?b.replace(KP,"$1"):d||l)}),o});function ar(r){if(typeof r=="string"||_s(r))return r;var o=r+"";return o=="0"&&1/r==-j?"-0":o}function Rn(r){if(r!=null){try{return Va.call(r)}catch{}try{return r+""}catch{}}return""}function Jk(r,o){return ws(yt,function(l){var d="_."+l[0];o&l[1]&&!Aa(r,d)&&r.push(d)}),r.sort()}function a_(r){if(r instanceof Qe)return r.clone();var o=new xs(r.__wrapped__,r.__chain__);return o.__actions__=as(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function Qk(r,o,l){(l?ns(r,o,l):o===s)?o=1:o=Lt(We(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var _=0,b=0,w=M(Ha(d/o));_<d;)w[b++]=Is(r,_,_+=o);return w}function Zk(r){for(var o=-1,l=r==null?0:r.length,d=0,_=[];++o<l;){var b=r[o];b&&(_[d++]=b)}return _}function eV(){var r=arguments.length;if(!r)return[];for(var o=M(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return Xr(qe(l)?as(l):[l],Wt(o,1))}var tV=Xe(function(r,o){return xt(r)?di(r,Wt(o,1,xt,!0)):[]}),sV=Xe(function(r,o){var l=Ds(o);return xt(l)&&(l=s),xt(r)?di(r,Wt(o,1,xt,!0),Te(l,2)):[]}),rV=Xe(function(r,o){var l=Ds(o);return xt(l)&&(l=s),xt(r)?di(r,Wt(o,1,xt,!0),s,l):[]});function nV(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:We(o),Is(r,o<0?0:o,d)):[]}function oV(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:We(o),o=d-o,Is(r,0,o<0?0:o)):[]}function iV(r,o){return r&&r.length?Za(r,Te(o,3),!0,!0):[]}function aV(r,o){return r&&r.length?Za(r,Te(o,3),!0):[]}function lV(r,o,l,d){var _=r==null?0:r.length;return _?(l&&typeof l!="number"&&ns(r,o,l)&&(l=0,d=_),ek(r,o,l,d)):[]}function l_(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var _=l==null?0:We(l);return _<0&&(_=Lt(d+_,0)),Ra(r,Te(o,3),_)}function u_(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var _=d-1;return l!==s&&(_=We(l),_=l<0?Lt(d+_,0):Gt(_,d-1)),Ra(r,Te(o,3),_,!0)}function c_(r){var o=r==null?0:r.length;return o?Wt(r,1):[]}function uV(r){var o=r==null?0:r.length;return o?Wt(r,j):[]}function cV(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:We(o),Wt(r,o)):[]}function dV(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var _=r[o];d[_[0]]=_[1]}return d}function d_(r){return r&&r.length?r[0]:s}function fV(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var _=l==null?0:We(l);return _<0&&(_=Lt(d+_,0)),so(r,o,_)}function hV(r){var o=r==null?0:r.length;return o?Is(r,0,-1):[]}var pV=Xe(function(r){var o=bt(r,Lc);return o.length&&o[0]===r[0]?Ic(o):[]}),mV=Xe(function(r){var o=Ds(r),l=bt(r,Lc);return o===Ds(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Ic(l,Te(o,2)):[]}),gV=Xe(function(r){var o=Ds(r),l=bt(r,Lc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Ic(l,s,o):[]});function _V(r,o){return r==null?"":hM.call(r,o)}function Ds(r){var o=r==null?0:r.length;return o?r[o-1]:s}function vV(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var _=d;return l!==s&&(_=We(l),_=_<0?Lt(d+_,0):Gt(_,d-1)),o===o?J2(r,o,_):Ra(r,jm,_,!0)}function yV(r,o){return r&&r.length?Cg(r,We(o)):s}var bV=Xe(f_);function f_(r,o){return r&&r.length&&o&&o.length?Ac(r,o):r}function EV(r,o,l){return r&&r.length&&o&&o.length?Ac(r,o,Te(l,2)):r}function CV(r,o,l){return r&&r.length&&o&&o.length?Ac(r,o,s,l):r}var wV=Tr(function(r,o){var l=r==null?0:r.length,d=wc(r,o);return xg(r,bt(o,function(_){return Ar(_,l)?+_:_}).sort(kg)),d});function OV(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,_=[],b=r.length;for(o=Te(o,3);++d<b;){var w=r[d];o(w,d,r)&&(l.push(w),_.push(d))}return xg(r,_),l}function Jc(r){return r==null?r:_M.call(r)}function xV(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&ns(r,o,l)?(o=0,l=d):(o=o==null?0:We(o),l=l===s?d:We(l)),Is(r,o,l)):[]}function SV(r,o){return Qa(r,o)}function IV(r,o,l){return Mc(r,o,Te(l,2))}function DV(r,o){var l=r==null?0:r.length;if(l){var d=Qa(r,o);if(d<l&&Ws(r[d],o))return d}return-1}function NV(r,o){return Qa(r,o,!0)}function TV(r,o,l){return Mc(r,o,Te(l,2),!0)}function AV(r,o){var l=r==null?0:r.length;if(l){var d=Qa(r,o,!0)-1;if(Ws(r[d],o))return d}return-1}function RV(r){return r&&r.length?Ig(r):[]}function PV(r,o){return r&&r.length?Ig(r,Te(o,2)):[]}function MV(r){var o=r==null?0:r.length;return o?Is(r,1,o):[]}function kV(r,o,l){return r&&r.length?(o=l||o===s?1:We(o),Is(r,0,o<0?0:o)):[]}function VV(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:We(o),o=d-o,Is(r,o<0?0:o,d)):[]}function LV(r,o){return r&&r.length?Za(r,Te(o,3),!1,!0):[]}function $V(r,o){return r&&r.length?Za(r,Te(o,3)):[]}var FV=Xe(function(r){return Zr(Wt(r,1,xt,!0))}),UV=Xe(function(r){var o=Ds(r);return xt(o)&&(o=s),Zr(Wt(r,1,xt,!0),Te(o,2))}),BV=Xe(function(r){var o=Ds(r);return o=typeof o=="function"?o:s,Zr(Wt(r,1,xt,!0),s,o)});function qV(r){return r&&r.length?Zr(r):[]}function HV(r,o){return r&&r.length?Zr(r,Te(o,2)):[]}function WV(r,o){return o=typeof o=="function"?o:s,r&&r.length?Zr(r,s,o):[]}function Qc(r){if(!(r&&r.length))return[];var o=0;return r=Kr(r,function(l){if(xt(l))return o=Lt(l.length,o),!0}),mc(o,function(l){return bt(r,fc(l))})}function h_(r,o){if(!(r&&r.length))return[];var l=Qc(r);return o==null?l:bt(l,function(d){return ps(o,s,d)})}var jV=Xe(function(r,o){return xt(r)?di(r,o):[]}),GV=Xe(function(r){return Vc(Kr(r,xt))}),zV=Xe(function(r){var o=Ds(r);return xt(o)&&(o=s),Vc(Kr(r,xt),Te(o,2))}),KV=Xe(function(r){var o=Ds(r);return o=typeof o=="function"?o:s,Vc(Kr(r,xt),s,o)}),XV=Xe(Qc);function YV(r,o){return Ag(r||[],o||[],ci)}function JV(r,o){return Ag(r||[],o||[],pi)}var QV=Xe(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,h_(r,l)});function p_(r){var o=y(r);return o.__chain__=!0,o}function ZV(r,o){return o(r),r}function ll(r,o){return o(r)}var eL=Tr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,_=function(b){return wc(b,r)};return o>1||this.__actions__.length||!(d instanceof Qe)||!Ar(l)?this.thru(_):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:ll,args:[_],thisArg:s}),new xs(d,this.__chain__).thru(function(b){return o&&!b.length&&b.push(s),b}))});function tL(){return p_(this)}function sL(){return new xs(this.value(),this.__chain__)}function rL(){this.__values__===s&&(this.__values__=D_(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function nL(){return this}function oL(r){for(var o,l=this;l instanceof za;){var d=a_(l);d.__index__=0,d.__values__=s,o?_.__wrapped__=d:o=d;var _=d;l=l.__wrapped__}return _.__wrapped__=r,o}function iL(){var r=this.__wrapped__;if(r instanceof Qe){var o=r;return this.__actions__.length&&(o=new Qe(this)),o=o.reverse(),o.__actions__.push({func:ll,args:[Jc],thisArg:s}),new xs(o,this.__chain__)}return this.thru(Jc)}function aL(){return Tg(this.__wrapped__,this.__actions__)}var lL=el(function(r,o,l){ht.call(r,l)?++r[l]:Dr(r,l,1)});function uL(r,o,l){var d=qe(r)?Hm:ZM;return l&&ns(r,o,l)&&(o=s),d(r,Te(o,3))}function cL(r,o){var l=qe(r)?Kr:hg;return l(r,Te(o,3))}var dL=Bg(l_),fL=Bg(u_);function hL(r,o){return Wt(ul(r,o),1)}function pL(r,o){return Wt(ul(r,o),j)}function mL(r,o,l){return l=l===s?1:We(l),Wt(ul(r,o),l)}function m_(r,o){var l=qe(r)?ws:Qr;return l(r,Te(o,3))}function g_(r,o){var l=qe(r)?M2:fg;return l(r,Te(o,3))}var gL=el(function(r,o,l){ht.call(r,l)?r[l].push(o):Dr(r,l,[o])});function _L(r,o,l,d){r=ls(r)?r:mo(r),l=l&&!d?We(l):0;var _=r.length;return l<0&&(l=Lt(_+l,0)),pl(r)?l<=_&&r.indexOf(o,l)>-1:!!_&&so(r,o,l)>-1}var vL=Xe(function(r,o,l){var d=-1,_=typeof o=="function",b=ls(r)?M(r.length):[];return Qr(r,function(w){b[++d]=_?ps(o,w,l):fi(w,o,l)}),b}),yL=el(function(r,o,l){Dr(r,l,o)});function ul(r,o){var l=qe(r)?bt:yg;return l(r,Te(o,3))}function bL(r,o,l,d){return r==null?[]:(qe(o)||(o=o==null?[]:[o]),l=d?s:l,qe(l)||(l=l==null?[]:[l]),wg(r,o,l))}var EL=el(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function CL(r,o,l){var d=qe(r)?cc:zm,_=arguments.length<3;return d(r,Te(o,4),l,_,Qr)}function wL(r,o,l){var d=qe(r)?k2:zm,_=arguments.length<3;return d(r,Te(o,4),l,_,fg)}function OL(r,o){var l=qe(r)?Kr:hg;return l(r,fl(Te(o,3)))}function xL(r){var o=qe(r)?lg:_k;return o(r)}function SL(r,o,l){(l?ns(r,o,l):o===s)?o=1:o=We(o);var d=qe(r)?KM:vk;return d(r,o)}function IL(r){var o=qe(r)?XM:bk;return o(r)}function DL(r){if(r==null)return 0;if(ls(r))return pl(r)?no(r):r.length;var o=zt(r);return o==E||o==Q?r.size:Nc(r).length}function NL(r,o,l){var d=qe(r)?dc:Ek;return l&&ns(r,o,l)&&(o=s),d(r,Te(o,3))}var TL=Xe(function(r,o){if(r==null)return[];var l=o.length;return l>1&&ns(r,o[0],o[1])?o=[]:l>2&&ns(o[0],o[1],o[2])&&(o=[o[0]]),wg(r,Wt(o,1),[])}),cl=cM||function(){return Ht.Date.now()};function AL(r,o){if(typeof o!="function")throw new Os(u);return r=We(r),function(){if(--r<1)return o.apply(this,arguments)}}function __(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,Nr(r,xe,s,s,s,s,o)}function v_(r,o){var l;if(typeof o!="function")throw new Os(u);return r=We(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var Zc=Xe(function(r,o,l){var d=oe;if(l.length){var _=Yr(l,ho(Zc));d|=se}return Nr(r,d,o,l,_)}),y_=Xe(function(r,o,l){var d=oe|W;if(l.length){var _=Yr(l,ho(y_));d|=se}return Nr(o,d,r,l,_)});function b_(r,o,l){o=l?s:o;var d=Nr(r,q,s,s,s,s,s,o);return d.placeholder=b_.placeholder,d}function E_(r,o,l){o=l?s:o;var d=Nr(r,ye,s,s,s,s,s,o);return d.placeholder=E_.placeholder,d}function C_(r,o,l){var d,_,b,w,I,T,U=0,B=!1,K=!1,he=!0;if(typeof r!="function")throw new Os(u);o=Ns(o)||0,Et(l)&&(B=!!l.leading,K="maxWait"in l,b=K?Lt(Ns(l.maxWait)||0,o):b,he="trailing"in l?!!l.trailing:he);function Oe(St){var js=d,Mr=_;return d=_=s,U=St,w=r.apply(Mr,js),w}function Pe(St){return U=St,I=_i(Je,o),B?Oe(St):w}function Ge(St){var js=St-T,Mr=St-U,B_=o-js;return K?Gt(B_,b-Mr):B_}function Me(St){var js=St-T,Mr=St-U;return T===s||js>=o||js<0||K&&Mr>=b}function Je(){var St=cl();if(Me(St))return et(St);I=_i(Je,Ge(St))}function et(St){return I=s,he&&d?Oe(St):(d=_=s,w)}function vs(){I!==s&&Rg(I),U=0,d=T=_=I=s}function os(){return I===s?w:et(cl())}function ys(){var St=cl(),js=Me(St);if(d=arguments,_=this,T=St,js){if(I===s)return Pe(T);if(K)return Rg(I),I=_i(Je,o),Oe(T)}return I===s&&(I=_i(Je,o)),w}return ys.cancel=vs,ys.flush=os,ys}var RL=Xe(function(r,o){return dg(r,1,o)}),PL=Xe(function(r,o,l){return dg(r,Ns(o)||0,l)});function ML(r){return Nr(r,de)}function dl(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new Os(u);var l=function(){var d=arguments,_=o?o.apply(this,d):d[0],b=l.cache;if(b.has(_))return b.get(_);var w=r.apply(this,d);return l.cache=b.set(_,w)||b,w};return l.cache=new(dl.Cache||Ir),l}dl.Cache=Ir;function fl(r){if(typeof r!="function")throw new Os(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function kL(r){return v_(2,r)}var VL=Ck(function(r,o){o=o.length==1&&qe(o[0])?bt(o[0],ms(Te())):bt(Wt(o,1),ms(Te()));var l=o.length;return Xe(function(d){for(var _=-1,b=Gt(d.length,l);++_<b;)d[_]=o[_].call(this,d[_]);return ps(r,this,d)})}),ed=Xe(function(r,o){var l=Yr(o,ho(ed));return Nr(r,se,s,o,l)}),w_=Xe(function(r,o){var l=Yr(o,ho(w_));return Nr(r,be,s,o,l)}),LL=Tr(function(r,o){return Nr(r,ke,s,s,s,o)});function $L(r,o){if(typeof r!="function")throw new Os(u);return o=o===s?o:We(o),Xe(r,o)}function FL(r,o){if(typeof r!="function")throw new Os(u);return o=o==null?0:Lt(We(o),0),Xe(function(l){var d=l[o],_=tn(l,0,o);return d&&Xr(_,d),ps(r,this,_)})}function UL(r,o,l){var d=!0,_=!0;if(typeof r!="function")throw new Os(u);return Et(l)&&(d="leading"in l?!!l.leading:d,_="trailing"in l?!!l.trailing:_),C_(r,o,{leading:d,maxWait:o,trailing:_})}function BL(r){return __(r,1)}function qL(r,o){return ed($c(o),r)}function HL(){if(!arguments.length)return[];var r=arguments[0];return qe(r)?r:[r]}function WL(r){return Ss(r,C)}function jL(r,o){return o=typeof o=="function"?o:s,Ss(r,C,o)}function GL(r){return Ss(r,p|C)}function zL(r,o){return o=typeof o=="function"?o:s,Ss(r,p|C,o)}function KL(r,o){return o==null||cg(r,o,Ut(o))}function Ws(r,o){return r===o||r!==r&&o!==o}var XL=nl(Sc),YL=nl(function(r,o){return r>=o}),Pn=gg(function(){return arguments}())?gg:function(r){return Ct(r)&&ht.call(r,"callee")&&!sg.call(r,"callee")},qe=M.isArray,JL=Lm?ms(Lm):ok;function ls(r){return r!=null&&hl(r.length)&&!Rr(r)}function xt(r){return Ct(r)&&ls(r)}function QL(r){return r===!0||r===!1||Ct(r)&&rs(r)==fe}var sn=fM||dd,ZL=$m?ms($m):ik;function e$(r){return Ct(r)&&r.nodeType===1&&!vi(r)}function t$(r){if(r==null)return!0;if(ls(r)&&(qe(r)||typeof r=="string"||typeof r.splice=="function"||sn(r)||po(r)||Pn(r)))return!r.length;var o=zt(r);if(o==E||o==Q)return!r.size;if(gi(r))return!Nc(r).length;for(var l in r)if(ht.call(r,l))return!1;return!0}function s$(r,o){return hi(r,o)}function r$(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?hi(r,o,s,l):!!d}function td(r){if(!Ct(r))return!1;var o=rs(r);return o==Ue||o==Ce||typeof r.message=="string"&&typeof r.name=="string"&&!vi(r)}function n$(r){return typeof r=="number"&&ng(r)}function Rr(r){if(!Et(r))return!1;var o=rs(r);return o==ft||o==Be||o==V||o==z}function O_(r){return typeof r=="number"&&r==We(r)}function hl(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=J}function Et(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function Ct(r){return r!=null&&typeof r=="object"}var x_=Fm?ms(Fm):lk;function o$(r,o){return r===o||Dc(r,o,jc(o))}function i$(r,o,l){return l=typeof l=="function"?l:s,Dc(r,o,jc(o),l)}function a$(r){return S_(r)&&r!=+r}function l$(r){if(jk(r))throw new Fe(a);return _g(r)}function u$(r){return r===null}function c$(r){return r==null}function S_(r){return typeof r=="number"||Ct(r)&&rs(r)==O}function vi(r){if(!Ct(r)||rs(r)!=F)return!1;var o=Ua(r);if(o===null)return!0;var l=ht.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&Va.call(l)==iM}var sd=Um?ms(Um):uk;function d$(r){return O_(r)&&r>=-J&&r<=J}var I_=Bm?ms(Bm):ck;function pl(r){return typeof r=="string"||!qe(r)&&Ct(r)&&rs(r)==re}function _s(r){return typeof r=="symbol"||Ct(r)&&rs(r)==X}var po=qm?ms(qm):dk;function f$(r){return r===s}function h$(r){return Ct(r)&&zt(r)==le}function p$(r){return Ct(r)&&rs(r)==we}var m$=nl(Tc),g$=nl(function(r,o){return r<=o});function D_(r){if(!r)return[];if(ls(r))return pl(r)?qs(r):as(r);if(oi&&r[oi])return K2(r[oi]());var o=zt(r),l=o==E?_c:o==Q?Pa:mo;return l(r)}function Pr(r){if(!r)return r===0?r:0;if(r=Ns(r),r===j||r===-j){var o=r<0?-1:1;return o*ge}return r===r?r:0}function We(r){var o=Pr(r),l=o%1;return o===o?l?o-l:o:0}function N_(r){return r?Nn(We(r),0,_e):0}function Ns(r){if(typeof r=="number")return r;if(_s(r))return ee;if(Et(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=Et(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=Km(r);var l=JP.test(r);return l||ZP.test(r)?A2(r.slice(2),l?2:8):YP.test(r)?ee:+r}function T_(r){return ir(r,us(r))}function _$(r){return r?Nn(We(r),-J,J):r===0?r:0}function dt(r){return r==null?"":gs(r)}var v$=co(function(r,o){if(gi(o)||ls(o)){ir(o,Ut(o),r);return}for(var l in o)ht.call(o,l)&&ci(r,l,o[l])}),A_=co(function(r,o){ir(o,us(o),r)}),ml=co(function(r,o,l,d){ir(o,us(o),r,d)}),y$=co(function(r,o,l,d){ir(o,Ut(o),r,d)}),b$=Tr(wc);function E$(r,o){var l=uo(r);return o==null?l:ug(l,o)}var C$=Xe(function(r,o){r=pt(r);var l=-1,d=o.length,_=d>2?o[2]:s;for(_&&ns(o[0],o[1],_)&&(d=1);++l<d;)for(var b=o[l],w=us(b),I=-1,T=w.length;++I<T;){var U=w[I],B=r[U];(B===s||Ws(B,io[U])&&!ht.call(r,U))&&(r[U]=b[U])}return r}),w$=Xe(function(r){return r.push(s,Kg),ps(R_,s,r)});function O$(r,o){return Wm(r,Te(o,3),or)}function x$(r,o){return Wm(r,Te(o,3),xc)}function S$(r,o){return r==null?r:Oc(r,Te(o,3),us)}function I$(r,o){return r==null?r:pg(r,Te(o,3),us)}function D$(r,o){return r&&or(r,Te(o,3))}function N$(r,o){return r&&xc(r,Te(o,3))}function T$(r){return r==null?[]:Ya(r,Ut(r))}function A$(r){return r==null?[]:Ya(r,us(r))}function rd(r,o,l){var d=r==null?s:Tn(r,o);return d===s?l:d}function R$(r,o){return r!=null&&Jg(r,o,tk)}function nd(r,o){return r!=null&&Jg(r,o,sk)}var P$=Hg(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=La.call(o)),r[o]=l},id(cs)),M$=Hg(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=La.call(o)),ht.call(r,o)?r[o].push(l):r[o]=[l]},Te),k$=Xe(fi);function Ut(r){return ls(r)?ag(r):Nc(r)}function us(r){return ls(r)?ag(r,!0):fk(r)}function V$(r,o){var l={};return o=Te(o,3),or(r,function(d,_,b){Dr(l,o(d,_,b),d)}),l}function L$(r,o){var l={};return o=Te(o,3),or(r,function(d,_,b){Dr(l,_,o(d,_,b))}),l}var $$=co(function(r,o,l){Ja(r,o,l)}),R_=co(function(r,o,l,d){Ja(r,o,l,d)}),F$=Tr(function(r,o){var l={};if(r==null)return l;var d=!1;o=bt(o,function(b){return b=en(b,r),d||(d=b.length>1),b}),ir(r,Hc(r),l),d&&(l=Ss(l,p|v|C,Pk));for(var _=o.length;_--;)kc(l,o[_]);return l});function U$(r,o){return P_(r,fl(Te(o)))}var B$=Tr(function(r,o){return r==null?{}:pk(r,o)});function P_(r,o){if(r==null)return{};var l=bt(Hc(r),function(d){return[d]});return o=Te(o),Og(r,l,function(d,_){return o(d,_[0])})}function q$(r,o,l){o=en(o,r);var d=-1,_=o.length;for(_||(_=1,r=s);++d<_;){var b=r==null?s:r[ar(o[d])];b===s&&(d=_,b=l),r=Rr(b)?b.call(r):b}return r}function H$(r,o,l){return r==null?r:pi(r,o,l)}function W$(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:pi(r,o,l,d)}var M_=Gg(Ut),k_=Gg(us);function j$(r,o,l){var d=qe(r),_=d||sn(r)||po(r);if(o=Te(o,4),l==null){var b=r&&r.constructor;_?l=d?new b:[]:Et(r)?l=Rr(b)?uo(Ua(r)):{}:l={}}return(_?ws:or)(r,function(w,I,T){return o(l,w,I,T)}),l}function G$(r,o){return r==null?!0:kc(r,o)}function z$(r,o,l){return r==null?r:Ng(r,o,$c(l))}function K$(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Ng(r,o,$c(l),d)}function mo(r){return r==null?[]:gc(r,Ut(r))}function X$(r){return r==null?[]:gc(r,us(r))}function Y$(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Ns(l),l=l===l?l:0),o!==s&&(o=Ns(o),o=o===o?o:0),Nn(Ns(r),o,l)}function J$(r,o,l){return o=Pr(o),l===s?(l=o,o=0):l=Pr(l),r=Ns(r),rk(r,o,l)}function Q$(r,o,l){if(l&&typeof l!="boolean"&&ns(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Pr(r),o===s?(o=r,r=0):o=Pr(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var _=og();return Gt(r+_*(o-r+T2("1e-"+((_+"").length-1))),o)}return Rc(r,o)}var Z$=fo(function(r,o,l){return o=o.toLowerCase(),r+(l?V_(o):o)});function V_(r){return od(dt(r).toLowerCase())}function L_(r){return r=dt(r),r&&r.replace(t2,H2).replace(b2,"")}function eF(r,o,l){r=dt(r),o=gs(o);var d=r.length;l=l===s?d:Nn(We(l),0,d);var _=l;return l-=o.length,l>=0&&r.slice(l,_)==o}function tF(r){return r=dt(r),r&&kP.test(r)?r.replace(pm,W2):r}function sF(r){return r=dt(r),r&&BP.test(r)?r.replace(ec,"\\$&"):r}var rF=fo(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),nF=fo(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),oF=Ug("toLowerCase");function iF(r,o,l){r=dt(r),o=We(o);var d=o?no(r):0;if(!o||d>=o)return r;var _=(o-d)/2;return rl(Wa(_),l)+r+rl(Ha(_),l)}function aF(r,o,l){r=dt(r),o=We(o);var d=o?no(r):0;return o&&d<o?r+rl(o-d,l):r}function lF(r,o,l){r=dt(r),o=We(o);var d=o?no(r):0;return o&&d<o?rl(o-d,l)+r:r}function uF(r,o,l){return l||o==null?o=0:o&&(o=+o),gM(dt(r).replace(tc,""),o||0)}function cF(r,o,l){return(l?ns(r,o,l):o===s)?o=1:o=We(o),Pc(dt(r),o)}function dF(){var r=arguments,o=dt(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var fF=fo(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function hF(r,o,l){return l&&typeof l!="number"&&ns(r,o,l)&&(o=l=s),l=l===s?_e:l>>>0,l?(r=dt(r),r&&(typeof o=="string"||o!=null&&!sd(o))&&(o=gs(o),!o&&ro(r))?tn(qs(r),0,l):r.split(o,l)):[]}var pF=fo(function(r,o,l){return r+(l?" ":"")+od(o)});function mF(r,o,l){return r=dt(r),l=l==null?0:Nn(We(l),0,r.length),o=gs(o),r.slice(l,l+o.length)==o}function gF(r,o,l){var d=y.templateSettings;l&&ns(r,o,l)&&(o=s),r=dt(r),o=ml({},o,d,zg);var _=ml({},o.imports,d.imports,zg),b=Ut(_),w=gc(_,b),I,T,U=0,B=o.interpolate||Da,K="__p += '",he=vc((o.escape||Da).source+"|"+B.source+"|"+(B===mm?XP:Da).source+"|"+(o.evaluate||Da).source+"|$","g"),Oe="//# sourceURL="+(ht.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++x2+"]")+`
`;r.replace(he,function(Me,Je,et,vs,os,ys){return et||(et=vs),K+=r.slice(U,ys).replace(s2,j2),Je&&(I=!0,K+=`' +
__e(`+Je+`) +
'`),os&&(T=!0,K+=`';
`+os+`;
__p += '`),et&&(K+=`' +
((__t = (`+et+`)) == null ? '' : __t) +
'`),U=ys+Me.length,Me}),K+=`';
`;var Pe=ht.call(o,"variable")&&o.variable;if(!Pe)K=`with (obj) {
`+K+`
}
`;else if(zP.test(Pe))throw new Fe(c);K=(T?K.replace(Es,""):K).replace(Ia,"$1").replace(PP,"$1;"),K="function("+(Pe||"obj")+`) {
`+(Pe?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(I?", __e = _.escape":"")+(T?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+K+`return __p
}`;var Ge=F_(function(){return at(b,Oe+"return "+K).apply(s,w)});if(Ge.source=K,td(Ge))throw Ge;return Ge}function _F(r){return dt(r).toLowerCase()}function vF(r){return dt(r).toUpperCase()}function yF(r,o,l){if(r=dt(r),r&&(l||o===s))return Km(r);if(!r||!(o=gs(o)))return r;var d=qs(r),_=qs(o),b=Xm(d,_),w=Ym(d,_)+1;return tn(d,b,w).join("")}function bF(r,o,l){if(r=dt(r),r&&(l||o===s))return r.slice(0,Qm(r)+1);if(!r||!(o=gs(o)))return r;var d=qs(r),_=Ym(d,qs(o))+1;return tn(d,0,_).join("")}function EF(r,o,l){if(r=dt(r),r&&(l||o===s))return r.replace(tc,"");if(!r||!(o=gs(o)))return r;var d=qs(r),_=Xm(d,qs(o));return tn(d,_).join("")}function CF(r,o){var l=De,d=$e;if(Et(o)){var _="separator"in o?o.separator:_;l="length"in o?We(o.length):l,d="omission"in o?gs(o.omission):d}r=dt(r);var b=r.length;if(ro(r)){var w=qs(r);b=w.length}if(l>=b)return r;var I=l-no(d);if(I<1)return d;var T=w?tn(w,0,I).join(""):r.slice(0,I);if(_===s)return T+d;if(w&&(I+=T.length-I),sd(_)){if(r.slice(I).search(_)){var U,B=T;for(_.global||(_=vc(_.source,dt(gm.exec(_))+"g")),_.lastIndex=0;U=_.exec(B);)var K=U.index;T=T.slice(0,K===s?I:K)}}else if(r.indexOf(gs(_),I)!=I){var he=T.lastIndexOf(_);he>-1&&(T=T.slice(0,he))}return T+d}function wF(r){return r=dt(r),r&&MP.test(r)?r.replace(hm,Q2):r}var OF=fo(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),od=Ug("toUpperCase");function $_(r,o,l){return r=dt(r),o=l?s:o,o===s?z2(r)?tM(r):$2(r):r.match(o)||[]}var F_=Xe(function(r,o){try{return ps(r,s,o)}catch(l){return td(l)?l:new Fe(l)}}),xF=Tr(function(r,o){return ws(o,function(l){l=ar(l),Dr(r,l,Zc(r[l],r))}),r});function SF(r){var o=r==null?0:r.length,l=Te();return r=o?bt(r,function(d){if(typeof d[1]!="function")throw new Os(u);return[l(d[0]),d[1]]}):[],Xe(function(d){for(var _=-1;++_<o;){var b=r[_];if(ps(b[0],this,d))return ps(b[1],this,d)}})}function IF(r){return QM(Ss(r,p))}function id(r){return function(){return r}}function DF(r,o){return r==null||r!==r?o:r}var NF=qg(),TF=qg(!0);function cs(r){return r}function ad(r){return vg(typeof r=="function"?r:Ss(r,p))}function AF(r){return bg(Ss(r,p))}function RF(r,o){return Eg(r,Ss(o,p))}var PF=Xe(function(r,o){return function(l){return fi(l,r,o)}}),MF=Xe(function(r,o){return function(l){return fi(r,l,o)}});function ld(r,o,l){var d=Ut(o),_=Ya(o,d);l==null&&!(Et(o)&&(_.length||!d.length))&&(l=o,o=r,r=this,_=Ya(o,Ut(o)));var b=!(Et(l)&&"chain"in l)||!!l.chain,w=Rr(r);return ws(_,function(I){var T=o[I];r[I]=T,w&&(r.prototype[I]=function(){var U=this.__chain__;if(b||U){var B=r(this.__wrapped__),K=B.__actions__=as(this.__actions__);return K.push({func:T,args:arguments,thisArg:r}),B.__chain__=U,B}return T.apply(r,Xr([this.value()],arguments))})}),r}function kF(){return Ht._===this&&(Ht._=aM),this}function ud(){}function VF(r){return r=We(r),Xe(function(o){return Cg(o,r)})}var LF=Uc(bt),$F=Uc(Hm),FF=Uc(dc);function U_(r){return zc(r)?fc(ar(r)):mk(r)}function UF(r){return function(o){return r==null?s:Tn(r,o)}}var BF=Wg(),qF=Wg(!0);function cd(){return[]}function dd(){return!1}function HF(){return{}}function WF(){return""}function jF(){return!0}function GF(r,o){if(r=We(r),r<1||r>J)return[];var l=_e,d=Gt(r,_e);o=Te(o),r-=_e;for(var _=mc(d,o);++l<r;)o(l);return _}function zF(r){return qe(r)?bt(r,ar):_s(r)?[r]:as(i_(dt(r)))}function KF(r){var o=++oM;return dt(r)+o}var XF=sl(function(r,o){return r+o},0),YF=Bc("ceil"),JF=sl(function(r,o){return r/o},1),QF=Bc("floor");function ZF(r){return r&&r.length?Xa(r,cs,Sc):s}function e3(r,o){return r&&r.length?Xa(r,Te(o,2),Sc):s}function t3(r){return Gm(r,cs)}function s3(r,o){return Gm(r,Te(o,2))}function r3(r){return r&&r.length?Xa(r,cs,Tc):s}function n3(r,o){return r&&r.length?Xa(r,Te(o,2),Tc):s}var o3=sl(function(r,o){return r*o},1),i3=Bc("round"),a3=sl(function(r,o){return r-o},0);function l3(r){return r&&r.length?pc(r,cs):0}function u3(r,o){return r&&r.length?pc(r,Te(o,2)):0}return y.after=AL,y.ary=__,y.assign=v$,y.assignIn=A_,y.assignInWith=ml,y.assignWith=y$,y.at=b$,y.before=v_,y.bind=Zc,y.bindAll=xF,y.bindKey=y_,y.castArray=HL,y.chain=p_,y.chunk=Qk,y.compact=Zk,y.concat=eV,y.cond=SF,y.conforms=IF,y.constant=id,y.countBy=lL,y.create=E$,y.curry=b_,y.curryRight=E_,y.debounce=C_,y.defaults=C$,y.defaultsDeep=w$,y.defer=RL,y.delay=PL,y.difference=tV,y.differenceBy=sV,y.differenceWith=rV,y.drop=nV,y.dropRight=oV,y.dropRightWhile=iV,y.dropWhile=aV,y.fill=lV,y.filter=cL,y.flatMap=hL,y.flatMapDeep=pL,y.flatMapDepth=mL,y.flatten=c_,y.flattenDeep=uV,y.flattenDepth=cV,y.flip=ML,y.flow=NF,y.flowRight=TF,y.fromPairs=dV,y.functions=T$,y.functionsIn=A$,y.groupBy=gL,y.initial=hV,y.intersection=pV,y.intersectionBy=mV,y.intersectionWith=gV,y.invert=P$,y.invertBy=M$,y.invokeMap=vL,y.iteratee=ad,y.keyBy=yL,y.keys=Ut,y.keysIn=us,y.map=ul,y.mapKeys=V$,y.mapValues=L$,y.matches=AF,y.matchesProperty=RF,y.memoize=dl,y.merge=$$,y.mergeWith=R_,y.method=PF,y.methodOf=MF,y.mixin=ld,y.negate=fl,y.nthArg=VF,y.omit=F$,y.omitBy=U$,y.once=kL,y.orderBy=bL,y.over=LF,y.overArgs=VL,y.overEvery=$F,y.overSome=FF,y.partial=ed,y.partialRight=w_,y.partition=EL,y.pick=B$,y.pickBy=P_,y.property=U_,y.propertyOf=UF,y.pull=bV,y.pullAll=f_,y.pullAllBy=EV,y.pullAllWith=CV,y.pullAt=wV,y.range=BF,y.rangeRight=qF,y.rearg=LL,y.reject=OL,y.remove=OV,y.rest=$L,y.reverse=Jc,y.sampleSize=SL,y.set=H$,y.setWith=W$,y.shuffle=IL,y.slice=xV,y.sortBy=TL,y.sortedUniq=RV,y.sortedUniqBy=PV,y.split=hF,y.spread=FL,y.tail=MV,y.take=kV,y.takeRight=VV,y.takeRightWhile=LV,y.takeWhile=$V,y.tap=ZV,y.throttle=UL,y.thru=ll,y.toArray=D_,y.toPairs=M_,y.toPairsIn=k_,y.toPath=zF,y.toPlainObject=T_,y.transform=j$,y.unary=BL,y.union=FV,y.unionBy=UV,y.unionWith=BV,y.uniq=qV,y.uniqBy=HV,y.uniqWith=WV,y.unset=G$,y.unzip=Qc,y.unzipWith=h_,y.update=z$,y.updateWith=K$,y.values=mo,y.valuesIn=X$,y.without=jV,y.words=$_,y.wrap=qL,y.xor=GV,y.xorBy=zV,y.xorWith=KV,y.zip=XV,y.zipObject=YV,y.zipObjectDeep=JV,y.zipWith=QV,y.entries=M_,y.entriesIn=k_,y.extend=A_,y.extendWith=ml,ld(y,y),y.add=XF,y.attempt=F_,y.camelCase=Z$,y.capitalize=V_,y.ceil=YF,y.clamp=Y$,y.clone=WL,y.cloneDeep=GL,y.cloneDeepWith=zL,y.cloneWith=jL,y.conformsTo=KL,y.deburr=L_,y.defaultTo=DF,y.divide=JF,y.endsWith=eF,y.eq=Ws,y.escape=tF,y.escapeRegExp=sF,y.every=uL,y.find=dL,y.findIndex=l_,y.findKey=O$,y.findLast=fL,y.findLastIndex=u_,y.findLastKey=x$,y.floor=QF,y.forEach=m_,y.forEachRight=g_,y.forIn=S$,y.forInRight=I$,y.forOwn=D$,y.forOwnRight=N$,y.get=rd,y.gt=XL,y.gte=YL,y.has=R$,y.hasIn=nd,y.head=d_,y.identity=cs,y.includes=_L,y.indexOf=fV,y.inRange=J$,y.invoke=k$,y.isArguments=Pn,y.isArray=qe,y.isArrayBuffer=JL,y.isArrayLike=ls,y.isArrayLikeObject=xt,y.isBoolean=QL,y.isBuffer=sn,y.isDate=ZL,y.isElement=e$,y.isEmpty=t$,y.isEqual=s$,y.isEqualWith=r$,y.isError=td,y.isFinite=n$,y.isFunction=Rr,y.isInteger=O_,y.isLength=hl,y.isMap=x_,y.isMatch=o$,y.isMatchWith=i$,y.isNaN=a$,y.isNative=l$,y.isNil=c$,y.isNull=u$,y.isNumber=S_,y.isObject=Et,y.isObjectLike=Ct,y.isPlainObject=vi,y.isRegExp=sd,y.isSafeInteger=d$,y.isSet=I_,y.isString=pl,y.isSymbol=_s,y.isTypedArray=po,y.isUndefined=f$,y.isWeakMap=h$,y.isWeakSet=p$,y.join=_V,y.kebabCase=rF,y.last=Ds,y.lastIndexOf=vV,y.lowerCase=nF,y.lowerFirst=oF,y.lt=m$,y.lte=g$,y.max=ZF,y.maxBy=e3,y.mean=t3,y.meanBy=s3,y.min=r3,y.minBy=n3,y.stubArray=cd,y.stubFalse=dd,y.stubObject=HF,y.stubString=WF,y.stubTrue=jF,y.multiply=o3,y.nth=yV,y.noConflict=kF,y.noop=ud,y.now=cl,y.pad=iF,y.padEnd=aF,y.padStart=lF,y.parseInt=uF,y.random=Q$,y.reduce=CL,y.reduceRight=wL,y.repeat=cF,y.replace=dF,y.result=q$,y.round=i3,y.runInContext=D,y.sample=xL,y.size=DL,y.snakeCase=fF,y.some=NL,y.sortedIndex=SV,y.sortedIndexBy=IV,y.sortedIndexOf=DV,y.sortedLastIndex=NV,y.sortedLastIndexBy=TV,y.sortedLastIndexOf=AV,y.startCase=pF,y.startsWith=mF,y.subtract=a3,y.sum=l3,y.sumBy=u3,y.template=gF,y.times=GF,y.toFinite=Pr,y.toInteger=We,y.toLength=N_,y.toLower=_F,y.toNumber=Ns,y.toSafeInteger=_$,y.toString=dt,y.toUpper=vF,y.trim=yF,y.trimEnd=bF,y.trimStart=EF,y.truncate=CF,y.unescape=wF,y.uniqueId=KF,y.upperCase=OF,y.upperFirst=od,y.each=m_,y.eachRight=g_,y.first=d_,ld(y,function(){var r={};return or(y,function(o,l){ht.call(y.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),y.VERSION=i,ws(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){y[r].placeholder=y}),ws(["drop","take"],function(r,o){Qe.prototype[r]=function(l){l=l===s?1:Lt(We(l),0);var d=this.__filtered__&&!o?new Qe(this):this.clone();return d.__filtered__?d.__takeCount__=Gt(l,d.__takeCount__):d.__views__.push({size:Gt(l,_e),type:r+(d.__dir__<0?"Right":"")}),d},Qe.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),ws(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==G||l==pe;Qe.prototype[r]=function(_){var b=this.clone();return b.__iteratees__.push({iteratee:Te(_,3),type:l}),b.__filtered__=b.__filtered__||d,b}}),ws(["head","last"],function(r,o){var l="take"+(o?"Right":"");Qe.prototype[r]=function(){return this[l](1).value()[0]}}),ws(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");Qe.prototype[r]=function(){return this.__filtered__?new Qe(this):this[l](1)}}),Qe.prototype.compact=function(){return this.filter(cs)},Qe.prototype.find=function(r){return this.filter(r).head()},Qe.prototype.findLast=function(r){return this.reverse().find(r)},Qe.prototype.invokeMap=Xe(function(r,o){return typeof r=="function"?new Qe(this):this.map(function(l){return fi(l,r,o)})}),Qe.prototype.reject=function(r){return this.filter(fl(Te(r)))},Qe.prototype.slice=function(r,o){r=We(r);var l=this;return l.__filtered__&&(r>0||o<0)?new Qe(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=We(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},Qe.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},Qe.prototype.toArray=function(){return this.take(_e)},or(Qe.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),_=y[d?"take"+(o=="last"?"Right":""):o],b=d||/^find/.test(o);_&&(y.prototype[o]=function(){var w=this.__wrapped__,I=d?[1]:arguments,T=w instanceof Qe,U=I[0],B=T||qe(w),K=function(Je){var et=_.apply(y,Xr([Je],I));return d&&he?et[0]:et};B&&l&&typeof U=="function"&&U.length!=1&&(T=B=!1);var he=this.__chain__,Oe=!!this.__actions__.length,Pe=b&&!he,Ge=T&&!Oe;if(!b&&B){w=Ge?w:new Qe(this);var Me=r.apply(w,I);return Me.__actions__.push({func:ll,args:[K],thisArg:s}),new xs(Me,he)}return Pe&&Ge?r.apply(this,I):(Me=this.thru(K),Pe?d?Me.value()[0]:Me.value():Me)})}),ws(["pop","push","shift","sort","splice","unshift"],function(r){var o=Ma[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);y.prototype[r]=function(){var _=arguments;if(d&&!this.__chain__){var b=this.value();return o.apply(qe(b)?b:[],_)}return this[l](function(w){return o.apply(qe(w)?w:[],_)})}}),or(Qe.prototype,function(r,o){var l=y[o];if(l){var d=l.name+"";ht.call(lo,d)||(lo[d]=[]),lo[d].push({name:o,func:l})}}),lo[tl(s,W).name]=[{name:"wrapper",func:s}],Qe.prototype.clone=wM,Qe.prototype.reverse=OM,Qe.prototype.value=xM,y.prototype.at=eL,y.prototype.chain=tL,y.prototype.commit=sL,y.prototype.next=rL,y.prototype.plant=oL,y.prototype.reverse=iL,y.prototype.toJSON=y.prototype.valueOf=y.prototype.value=aL,y.prototype.first=y.prototype.head,oi&&(y.prototype[oi]=nL),y},oo=sM();xn?((xn.exports=oo)._=oo,ac._=oo):Ht._=oo}).call(Qo)}(ua,ua.exports);var tr=ua.exports;const p3="",IC={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},DC={class:"toast-content"};function NC(e,t,s,i,n,a){return S(),ct(oy,{to:"body"},[x(ph,{name:"toast"},{default:Re(()=>[s.show?(S(),N("div",{key:0,class:me(["toast",s.type])},[m("div",DC,[m("i",{class:me(a.icon)},null,2),m("span",null,ne(s.message),1)]),m("div",{class:"toast-progress",style:As(a.progressStyle)},null,4)],2)):te("",!0)]),_:1})])}const sr=Le(IC,[["render",NC],["__scopeId","data-v-4440998c"]]),rr={data(){return{showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},methods:{showToastMessage(e,t="success"){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),typeof e=="object"&&e.message&&(e=e.message),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType=t,this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showInfoMessage(e){this.showToastMessage(e,"info")},showSuccessMessage(e){this.showToastMessage(e,"success")},showWarningMessage(e){this.showToastMessage(e,"warning")},showErrorMessage(e){this.showToastMessage(e,"error")},showDangerMessage(e){this.showToastMessage(e,"error")}}},m3="",TC={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},AC={key:0};function RC(e,t,s,i,n,a){return S(),ct(ph,null,{default:Re(()=>[s.isLoading?(S(),N("div",AC,t[0]||(t[0]=[m("div",{class:"modal-overlay"},null,-1),m("div",{class:"loader-wrapper"},[m("span",{class:"loader",role:"status"},[m("span",{class:"sr-only"},"Carregando...")])],-1)]))):te("",!0)]),_:1})}const Zo=Le(TC,[["render",RC],["__scopeId","data-v-b3cb5b4c"]]),g3="",PC={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function MC(e,t,s,i,n,a){return S(),N("div",{class:me(["filter-row",{"filter-row-inline":s.inline}])},[Rt(e.$slots,"default",{},void 0,!0)],2)}const ca=Le(PC,[["render",MC],["__scopeId","data-v-83bdb425"]]),_3="",kC={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,25,50,100]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},VC={class:"pagination-container mt-3"},LC={class:"pagination-info"},$C=["value"],FC={class:"pagination-text"},UC={class:"pagination-controls"},BC=["disabled"],qC=["onClick"],HC=["disabled"];function WC(e,t,s,i,n,a){return S(),N("div",VC,[m("div",LC,[Jt(m("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(S(!0),N(je,null,At(s.perPageOptions,u=>(S(),N("option",{key:u,value:u},ne(u),9,$C))),128))],544),[[Mh,a.perPageModel]]),m("span",FC," Mostrando de "+ne(a.from)+" até "+ne(a.to)+" de "+ne(s.total)+" resultados ",1)]),m("div",UC,[m("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[m("i",{class:"fas fa-chevron-left"},null,-1)]),8,BC),(S(!0),N(je,null,At(a.visiblePages,u=>(S(),N("button",{key:u,class:me(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},ne(u),11,qC))),128)),m("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[m("i",{class:"fas fa-chevron-right"},null,-1)]),8,HC)])])}const Cn=Le(kC,[["render",WC],["__scopeId","data-v-b3aa038d"]]),v3="",jC={name:"PageHeader",props:{title:{type:String,required:!0}}},GC={class:"page-header"},zC={class:"header-actions"};function KC(e,t,s,i,n,a){return S(),N("div",GC,[m("h2",null,ne(s.title),1),m("div",zC,[Rt(e.$slots,"actions",{},void 0,!0)])])}const Xn=Le(jC,[["render",KC],["__scopeId","data-v-5d6d687f"]]),y3="",XC={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},YC={key:0,class:"filter-label"},JC={class:"filter-input"};function QC(e,t,s,i,n,a){return S(),N("div",{class:me(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(S(),N("div",YC,ne(s.label),1)):te("",!0),m("div",JC,[Rt(e.$slots,"default",{},void 0,!0)])],2)}const da=Le(XC,[["render",QC],["__scopeId","data-v-d7bf1926"]]),b3="",ZC={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},tableClass:String,theadClass:String,tbodyClass:String},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},e1={class:"table-responsive"},t1=["data-value"],s1=["onClick"],r1={key:0},n1=["colspan"],o1=["data-column"];function i1(e,t,s,i,n,a){return S(),N("div",e1,[m("table",{class:me(["table",s.tableClass])},[m("thead",{class:me(s.theadClass)},[m("tr",null,[(S(!0),N(je,null,At(s.headers,u=>(S(),N("th",{key:u.value,class:me([u.value,{"text-right":u.align==="right"}]),style:As(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Rt(e.$slots,"header-select",{key:0},()=>[Ze(ne(u.text),1)],!0):(S(),N(je,{key:1},[Ze(ne(u.text)+" ",1),u.sortable?(S(),N("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[m("i",{class:me(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,s1)):te("",!0)],64))],14,t1))),128))])],2),m("tbody",{class:me(s.tbodyClass)},[s.items.length===0?(S(),N("tr",r1,[m("td",{colspan:s.headers.length,class:"text-center"},[Rt(e.$slots,"empty-state",{},()=>[t[0]||(t[0]=m("div",{class:"empty-state"},[m("span",null,"Nenhum registro encontrado...")],-1))],!0)],8,n1)])):(S(!0),N(je,{key:1},At(s.items,u=>(S(),N("tr",{key:u.id},[(S(!0),N(je,null,At(s.headers,c=>(S(),N("td",{key:c.value,class:me([c.value,{"text-right":c.align==="right"}]),"data-column":c.value},[Rt(e.$slots,"item-"+c.value,{item:u},()=>[Ze(ne(u[c.value]),1)],!0)],10,o1))),128))]))),128))],2)],2)])}const wn=Le(ZC,[["render",i1],["__scopeId","data-v-b3ca7a06"]]),E3="",a1={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},l1=["data-content","aria-label"],u1=["title","aria-label"];function c1(e,t,s,i,n,a){return S(),N("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[m("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,u1)],8,l1)}const fa=Le(a1,[["render",c1],["__scopeId","data-v-6eb219ea"]]),C3="",d1={name:"CustomLabel",components:{HelpIcon:fa},props:{text:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!1},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},className:{type:String,default:""}}},f1=["for"],h1={key:0,class:"icon fa fa-exclamation-circle text-danger fa-fw mr-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"};function p1(e,t,s,i,n,a){const u=L("HelpIcon");return S(),N("div",{class:me(["label-with-help",s.className])},[m("label",{for:s.id,class:me(["form-label",{disabled:s.disabled}])},[Rt(e.$slots,"default",{},()=>[Ze(ne(s.text),1)],!0)],10,f1),s.required?(S(),N("i",h1)):te("",!0),s.help?(S(),ct(u,{key:1,title:`Ajuda com ${s.text.toLowerCase()}`,text:s.help},null,8,["title","text"])):te("",!0)],2)}const wr=Le(d1,[["render",p1],["__scopeId","data-v-7ed7cdf7"]]),w3="",m1={name:"CustomInput",components:{CustomLabel:wr},props:{modelValue:{type:[String,Number],default:""},id:{type:String,required:!1,default:"custom-input-"+Math.random().toString(36).substring(2,9)},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.$emit("validate")}},emits:["update:modelValue","validate"]},g1=["type","placeholder","value","disabled","min","max","id"],_1={key:0,class:"search-icon"},v1={key:2,class:"form-control-feedback invalid-feedback d-block"};function y1(e,t,s,i,n,a){const u=L("CustomLabel");return S(),N("div",{class:"custom-input-container",style:As(a.customWidth)},[s.label?(S(),ct(u,{key:0,text:s.label},null,8,["text"])):te("",!0),m("div",{class:me(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[m("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...c)=>a.handleInput&&a.handleInput(...c)),onBlur:t[1]||(t[1]=(...c)=>a.handleBlur&&a.handleBlur(...c)),disabled:s.disabled,class:me(["form-control",{"is-invalid":s.hasError}]),min:a.isNumberType?0:null,max:s.max,id:s.id},null,42,g1),s.hasSearchIcon?(S(),N("div",_1,t[2]||(t[2]=[m("i",{class:"fas fa-search"},null,-1)]))):te("",!0),a.isDateType?(S(),N("div",{key:1,class:me(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[m("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):te("",!0),s.hasError&&s.errorMessage?(S(),N("div",v1,ne(s.errorMessage),1)):te("",!0)],2)],4)}const On=Le(m1,[["render",y1],["__scopeId","data-v-9953d3ff"]]),O3="",b1={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},E1={class:"select-wrapper"},C1=["value","disabled"],w1=["value"],O1={key:"loading",value:""},x1={key:1,class:"error-message"};function S1(e,t,s,i,n,a){return S(),N("div",{ref:"selectContainer",class:"custom-select-container",style:As(a.customWidth)},[s.label?(S(),N("div",{key:0,class:me(["select-label",{disabled:s.disabled}])},ne(s.label),3)):te("",!0),m("div",E1,[m("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:me(["form-control",{error:s.hasError}]),disabled:s.disabled},[s.isLoading?te("",!0):(S(!0),N(je,{key:0},At(s.options,u=>(S(),N("option",{key:u.value,value:u.value},ne(u.label),9,w1))),128)),s.isLoading?(S(),N("option",O1,"Carregando...")):te("",!0)],42,C1)]),s.hasError&&s.errorMessage?(S(),N("div",x1,ne(s.errorMessage),1)):te("",!0)],4)}const nr=Le(b1,[["render",S1],["__scopeId","data-v-42a8eff7"]]),x3="",I1={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1}},emits:["click"]},D1=["disabled"],N1={key:1,class:"spinner-border spinner-border-sm"},T1={key:2};function A1(e,t,s,i,n,a){return S(),N("button",{class:me(["btn custom-button",[`btn-${s.variant}`]]),disabled:s.disabled||s.isLoading,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(S(),N("i",{key:0,class:me(s.icon)},null,2)):te("",!0),s.isLoading?(S(),N("i",N1)):te("",!0),s.label?(S(),N("span",T1,ne(s.label),1)):te("",!0),Rt(e.$slots,"default",{},void 0,!0)],10,D1)}const hs=Le(I1,[["render",A1],["__scopeId","data-v-482c6327"]]),S3="",R1={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},P1={class:"filter-section"},M1={key:0},k1={class:"filter-content"},V1={key:1,class:"filter-tags"};function L1(e,t,s,i,n,a){return S(),N("div",P1,[s.title?(S(),N("h2",M1,ne(s.title),1)):te("",!0),m("div",k1,[Rt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(S(),N("div",V1,[Rt(e.$slots,"tags",{},void 0,!0)])):te("",!0)])}const kp=Le(R1,[["render",L1],["__scopeId","data-v-1ece8e84"]]),I3="",$1={name:"FilterActions"},F1={class:"filter-actions"};function U1(e,t,s,i,n,a){return S(),N("div",F1,[Rt(e.$slots,"default",{},void 0,!0)])}const Vp=Le($1,[["render",U1],["__scopeId","data-v-68346c90"]]),D3="",B1={name:"CustomCheckbox",components:{HelpIcon:fa},props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},help:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1},confirmBeforeChange:{type:Boolean,default:!1}},emits:["update:modelValue","request-change","validate"],methods:{handleClick(e){const t=!this.modelValue;this.confirmBeforeChange?this.$emit("request-change",t):(this.$emit("update:modelValue",t),this.$emit("validate"))}}},q1=["id","checked","disabled"],H1=["for"];function W1(e,t,s,i,n,a){const u=L("HelpIcon");return S(),N("div",{class:me(["checkbox-container",{disabled:s.disabled}])},[(S(),N("input",{type:"checkbox",id:s.id,key:s.modelValue,checked:s.modelValue,onClick:t[0]||(t[0]=es((...c)=>a.handleClick&&a.handleClick(...c),["prevent"])),class:"custom-checkbox",disabled:s.disabled},null,8,q1)),m("label",{for:s.id,class:me(["checkbox-label",{disabled:s.disabled}])},[Rt(e.$slots,"default",{},()=>[Ze(ne(s.label),1)],!0)],10,H1),s.help?(S(),ct(u,{key:0,title:`Ajuda com ${s.label.toLowerCase()}`,text:s.help},null,8,["title","text"])):te("",!0)],2)}const Yn=Le(B1,[["render",W1],["__scopeId","data-v-b01e2f91"]]),j1={xmlns:"http://www.w3.org/2000/svg",width:"22",height:"23",fill:"none",class:"mr-1"};function G1(e,t){return S(),N("svg",j1,t[0]||(t[0]=[m("rect",{width:"20",height:"20",x:"1",y:"1.4",fill:"#fff",rx:"10"},null,-1),m("rect",{width:"20",height:"20",x:"1",y:"1.4",stroke:"var(--success)","stroke-width":"2",rx:"10"},null,-1),m("path",{fill:"var(--success)",d:"M11.002 1.401a10 10 0 1 1 .001 20 10 10 0 0 1 0-20m4.357 5.94a.94.94 0 0 0-.67.3l-4.34 5.532-2.616-2.619a.939.939 0 0 0-1.325 1.326l3.307 3.308a.937.937 0 0 0 1.349-.024l4.99-6.238a.94.94 0 0 0-.012-1.312h-.002a.94.94 0 0 0-.681-.273"},null,-1)]))}const Lp={render:G1},z1={xmlns:"http://www.w3.org/2000/svg",width:"22",height:"23",fill:"none",class:"mr-1"};function K1(e,t){return S(),N("svg",z1,t[0]||(t[0]=[iu('<g clip-path="url(#a)"><rect width="20" height="20" x="1" y="1.4" fill="#fff" rx="10"></rect><path fill="var(--danger)" d="M11.002 1.401a10 10 0 1 1 .001 20 10 10 0 0 1 0-20m3.75 5.624a.63.63 0 0 0-.442.184l-3.308 3.308L7.695 7.21a.626.626 0 1 0-.885.884l3.309 3.308L6.81 14.71a.627.627 0 0 0 .885.885l3.307-3.31 3.308 3.31a.626.626 0 1 0 .885-.885L11.886 11.4l3.309-3.308a.63.63 0 0 0 0-.884.63.63 0 0 0-.443-.184"></path></g><rect width="20" height="20" x="1" y="1.4" stroke="var(--danger)" stroke-width="2" rx="10"></rect><defs><clipPath id="a"><rect width="20" height="20" x="1" y="1.4" fill="#fff" rx="10"></rect></clipPath></defs>',3)]))}const $p={render:K1},X1={xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"10 10 16 20"};function Y1(e,t){return S(),N("svg",X1,t[0]||(t[0]=[m("path",{stroke:"#fff","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.188 25.59c1.852 0 3.354-1.527 3.354-3.41s-1.502-3.41-3.355-3.41-3.354 1.526-3.354 3.41c0 1.883 1.502 3.41 3.355 3.41"},null,-1),m("path",{stroke:"#fff","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"m11 29.487 4.792-4.872M11 22.667V11.949c0-.517.202-1.013.561-1.378A1.9 1.9 0 0 1 12.917 10h7.666l5.75 5.846v11.692c0 .517-.202 1.013-.561 1.378a1.9 1.9 0 0 1-1.355.571h-6.709"},null,-1)]))}const Fp={render:Y1},N3="",J1={name:"Modal",components:{CustomButton:hs},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},Q1={class:"modal-body"},Z1={key:0,class:"modal-footer"},ew={key:1,class:"modal-footer"};function tw(e,t,s,i,n,a){const u=L("custom-button");return s.show?(S(),N("div",{key:0,class:"modal-overlay",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[m("div",{class:me(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=es(()=>{},["stop"]))},[m("div",Q1,[Rt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(S(),N("div",Z1,[Rt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(S(),N("div",ew,[x(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),x(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):te("",!0)],2)])):te("",!0)}const sw=Le(J1,[["render",tw],["__scopeId","data-v-bcc8f609"]]),T3="",rw={name:"ConfirmationModal",components:{Modal:sw},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)},size:{type:String,default:"sm"}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},nw={key:0,class:"icon-container"},ow={class:"modal-custom-title"},iw={key:1,class:"message-list"},aw={key:0,class:"list-title"},lw={key:2,class:"message"},uw={class:"modal-custom-footer"},cw=["disabled"];function dw(e,t,s,i,n,a){const u=L("modal");return S(),ct(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:s.size,"show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Re(()=>[m("div",{class:me(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(S(),N("div",nw,[m("i",{class:me(a.iconClass)},null,2)])):te("",!0),m("h3",ow,ne(s.title),1),a.hasListContent?(S(),N("div",iw,[s.listTitle?(S(),N("p",aw,ne(s.listTitle),1)):te("",!0),m("ul",null,[(S(!0),N(je,null,At(s.listItems,(c,f)=>(S(),N("li",{key:f},ne(c),1))),128))])])):(S(),N("div",lw,ne(s.message),1)),m("div",uw,[m("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},ne(s.cancelButtonText),1),m("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},ne(s.confirmButtonText),9,cw)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled","size"])}const Jn=Le(rw,[["render",dw],["__scopeId","data-v-2e1eb4fd"]]),A3="",R3="",fw={name:"OfferList",mixins:[rr],components:{Toast:sr,LFLoading:Zo,FilterRow:ca,Pagination:Cn,PageHeader:Xn,CustomTable:wn,CustomInput:On,FilterGroup:da,CustomButton:hs,CustomSelect:nr,FilterSection:kp,FilterActions:Vp,CustomCheckbox:Yn,FileSearchFill:Fp,CircleCheckIcon:Lp,CircleXMarkIcon:$p,ConfirmationModal:Jn},data(){return{inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"OFERTA",value:"name",sortable:!0},{text:"STATUS",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=tr.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await nC();e.types&&(this.typeOptionsEnabled=e.enabled,e.default&&(this.inputFilters.type=e.default),this.typeOptions=e.types.map(t=>({value:t,label:t})))},async loadOffers(){try{this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await tC(e);this.offers=t.offers||[],this.totalOffers=t.total_items||0}catch(e){this.error=e.message}finally{this.loading=!1}},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.$router.push({name:"offer.create"})},navigateToEditOffer(e){this.$router.push({name:"offer.edit",params:{id:e.id.toString()}})},navigateToShowOffer(e){this.$router.push({name:"offer.show",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await rC(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await lC(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())}}},hw={id:"offer-manager-view",class:"offer-manager"},pw={class:"new-offer-container"},mw={key:0,class:"alert alert-danger"},gw=["title"],_w={key:0},vw={key:1},yw={class:"action-buttons"},bw=["onClick"],Ew=["onClick"],Cw=["onClick","disabled","title"],ww={key:0,class:"fas fa-eye text-white"},Ow={key:1,class:"fas fa-eye-slash"},xw=["onClick","disabled","title"];function Sw(e,t,s,i,n,a){var xe,ke,de,De,$e,ue;const u=L("CustomButton"),c=L("PageHeader"),f=L("CustomInput"),g=L("FilterGroup"),h=L("CustomSelect"),p=L("CustomCheckbox"),v=L("FilterActions"),C=L("FilterRow"),P=L("FilterSection"),A=L("CircleCheckIcon"),oe=L("CircleXMarkIcon"),W=L("FileSearchFill"),ie=L("CustomTable"),q=L("Pagination"),ye=L("ConfirmationModal"),se=L("LFLoading"),be=L("Toast");return S(),N("div",hw,[x(c,{title:"Gerenciamento de ofertas"},{actions:Re(()=>[m("div",pw,[x(u,{variant:"primary",icon:"fa-solid fa-plus",label:"Adicionar",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),x(P,{title:"FILTRO"},{default:Re(()=>[x(C,{inline:!0},{default:Re(()=>[x(g,{label:"Oferta"},{default:Re(()=>[x(f,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=R=>n.inputFilters.search=R),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(S(),ct(g,{key:0,label:"Tipo"},{default:Re(()=>[x(h,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=R=>n.inputFilters.type=R),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):te("",!0),x(g,{"is-checkbox":!0},{default:Re(()=>[x(p,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=R=>n.inputFilters.hideInactive=R),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),x(v,null,{default:Re(()=>[x(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(S(),N("div",mw,[t[7]||(t[7]=m("i",{class:"fas fa-exclamation-circle"},null,-1)),Ze(" "+ne(n.error),1)])):te("",!0),x(ie,{tableClass:"table-striped table-hover mb-0",headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Re(({item:R})=>[m("span",{title:R.description},ne(R.description.length>50?R.description.slice(0,50)+"...":R.description),9,gw)]),"item-type":Re(({item:R})=>[Ze(ne(R.type.charAt(0).toUpperCase()+R.type.slice(1)),1)]),"item-status":Re(({item:R})=>[R.status?(S(),N("span",_w,[x(A),t[8]||(t[8]=Ze(" Ativo "))])):(S(),N("span",vw,[x(oe),t[9]||(t[9]=Ze(" Inativo "))]))]),"item-actions":Re(({item:R})=>[m("div",yw,[m("button",{class:"btn-action btn-edit",onClick:G=>a.navigateToShowOffer(R),title:"Visualizar"},[x(W)],8,bw),m("button",{class:"btn-action btn-edit",onClick:G=>a.navigateToEditOffer(R),title:"Editar"},t[10]||(t[10]=[m("i",{class:"fas fa-pencil-alt"},null,-1)]),8,Ew),m("button",{class:me(["btn-action",R.status===1?"btn-deactivate":"btn-activate"]),onClick:G=>a.toggleOfferStatus(R),disabled:R.status===0&&!R.can_activate,title:a.getStatusButtonTitle(R)},[R.status===1?(S(),N("i",ww)):(S(),N("i",Ow))],10,Cw),m("button",{class:"btn-action btn-delete",onClick:G=>a.deleteOffer(R),disabled:!R.can_delete,title:R.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[11]||(t[11]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,xw)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),x(q,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=R=>n.currentPage=R),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=R=>n.perPage=R),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),x(ye,{show:n.showDeleteModal,size:"md",title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=R=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),x(ye,{show:n.showStatusModal,size:"md",title:((xe=n.selectedOffer)==null?void 0:xe.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((ke=n.selectedOffer)==null?void 0:ke.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((de=n.selectedOffer)==null?void 0:de.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((De=n.selectedOffer)==null?void 0:De.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":(($e=n.selectedOffer)==null?void 0:$e.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ue=n.selectedOffer)==null?void 0:ue.status)===1?"warning":"question",onClose:t[6]||(t[6]=R=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),x(se,{"is-loading":n.loading},null,8,["is-loading"]),x(be,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Iw=Le(fw,[["render",Sw],["__scopeId","data-v-a9b8ca79"]]);async function Dw(e={}){try{return await Ke("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"})}catch(t){throw t}}async function Vu(e={}){try{return await Ke("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]})}catch(t){throw t}}async function Nw(e={}){try{return await Ke("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5})}catch(t){throw t}}async function Tw(e,t="",s){try{return await Ke("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s})}catch(i){throw i}}async function Aw(e={}){try{return await Ke("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid})}catch(t){throw t}}async function Rw(e={}){try{return await Ke("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend})}catch(t){throw t}}async function Pw(e){try{return await Ke("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e})===!0?e.map(i=>({id:i,operation_status:!0})):[]}catch(t){throw t}}async function Mw(e,t){try{return await Ke("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]})}catch(s){throw s}}const kw={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",fill:"none",stroke:"var(--primary)","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",viewBox:"0 0 24 24"};function Vw(e,t){return S(),N("svg",kw,t[0]||(t[0]=[m("circle",{cx:"12",cy:"12",r:"3"},null,-1),m("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1"},null,-1)]))}const Lw={render:Vw},$w={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",fill:"none",stroke:"var(--primary)","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",viewBox:"0 0 24 24"};function Fw(e,t){return S(),N("svg",$w,t[0]||(t[0]=[m("circle",{cx:"12",cy:"12",r:"10"},null,-1),m("path",{d:"M12 16v-4M12 8h.01"},null,-1)]))}const Uw={render:Fw},P3="",Bw={name:"FilterTag",props:{readonly:{type:Boolean,default:!1}},emits:["remove"]},qw={key:0,class:"fas fa-times"};function Hw(e,t,s,i,n,a){return S(),N("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=()=>!s.readonly&&e.$emit("remove"))},[s.readonly?te("",!0):(S(),N("i",qw)),Rt(e.$slots,"default",{},void 0,!0)])}const ei=Le(Bw,[["render",Hw],["__scopeId","data-v-1d857df7"]]),M3="",Ww={name:"FilterTags"},jw={class:"filter-tags"};function Gw(e,t,s,i,n,a){return S(),N("div",jw,[Rt(e.$slots,"default",{},void 0,!0)])}const ha=Le(Ww,[["render",Gw],["__scopeId","data-v-d8e54e5f"]]),k3="",zw={name:"Autocomplete",components:{FilterTag:ei,FilterTags:ha,CustomLabel:wr},props:{modelValue:{type:[Array,Object,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){return this.modelValue?this.modelValue.label:""},selectedItems(){return Array.isArray(this.modelValue)?this.modelValue.map(e=>{if(e.value&&e.label!=="")return e;const t=this.items.find(i=>i.value===(e.value||e)),s=(t==null?void 0:t.label)||"";return{value:e.value||e,label:s}}):[]}},created(){this.debouncedSearch=tr.debounce(e=>{this.$emit("search",e)},500)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){if(Array.isArray(this.modelValue)){if(this.modelValue.length===this.items.length){this.$emit("update:modelValue",[]);return}this.$emit("update:modelValue",this.items),this.$emit("select-all")}this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},Kw={class:"autocomplete-container"},Xw={class:"autocomplete-wrapper"},Yw=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls","id"],Jw={key:0,class:"selected-item"},Qw=["title"],Zw=["id"],eO=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],tO={class:"item-label"},sO={key:0,class:"fas fa-check"},rO={key:0,class:"dropdown-item loading-item"},nO={key:1,class:"dropdown-item no-results"},oO={key:0,class:"form-control-feedback invalid-feedback d-block"},iO={key:1,class:"tags-container"};function aO(e,t,s,i,n,a){const u=L("CustomLabel"),c=L("FilterTag"),f=L("FilterTags");return S(),N("div",Kw,[s.label?(S(),ct(u,{key:0,text:s.label,required:s.required,id:`${n.uniqueId}-input`},null,8,["text","required","id"])):te("",!0),m("div",Xw,[m("div",{class:"input-container",style:As({maxWidth:a.inputMaxWidthStyle})},[m("div",{class:me(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[Jt(m("input",{type:"text",class:me(["form-control",{"is-invalid":s.hasError}]),placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=g=>n.searchQuery=g),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,id:`${n.uniqueId}-input`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...g)=>a.handleKeydown&&a.handleKeydown(...g)),onFocus:t[2]||(t[2]=g=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...g)=>a.handleInput&&a.handleInput(...g)),onClick:t[4]||(t[4]=g=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...g)=>a.handleBlur&&a.handleBlur(...g)),ref:"inputElement"},null,42,Yw),[[yr,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(S(),N("div",Jw,[m("span",{class:"selected-text",title:a.getSelectedItemLabel},ne(a.truncateLabel(a.getSelectedItemLabel)),9,Qw),m("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=es((...g)=>a.removeSelectedItem&&a.removeSelectedItem(...g),["stop"]))})])):te("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(S(),N("i",{key:1,class:me(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):te("",!0)],2),n.isOpen?(S(),N("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...g)=>a.handleScroll&&a.handleScroll(...g))},[a.displayItems.length>0?(S(),N(je,{key:0},[(S(!0),N(je,null,At(a.displayItems,(g,h)=>(S(),N("div",{key:g.value==="__ALL__"?"__ALL__":g.value,class:me(["dropdown-item",{active:n.selectedIndex===h,selected:g.value!=="__ALL__"&&(Array.isArray(a.selectedItems)?a.selectedItems.some(p=>p.value===g.value):a.selectedItems===g.value)}]),id:`${n.uniqueId}-option-${h}`,role:"option","data-index":h,"aria-selected":n.selectedIndex===h,tabindex:n.selectedIndex===h?0:-1,onClick:p=>a.selectItem(g),onKeydown:p=>a.handleOptionKeydown(p,g,h),ref_for:!0,ref:"optionElements",title:g.label},[m("span",tO,ne(a.truncateLabel(g.label)),1),g.value!=="__ALL__"&&Array.isArray(a.selectedItems)&&a.selectedItems.some(p=>p.value===g.value)?(S(),N("i",sO)):te("",!0)],42,eO))),128)),s.loading?(S(),N("div",rO,t[8]||(t[8]=[m("span",null,"Carregando mais itens...",-1)]))):te("",!0)],64)):(S(),N("div",nO,ne(s.noResultsText||"Nenhum item disponível"),1))],40,Zw)):te("",!0)],4),s.hasError&&s.errorMessage?(S(),N("div",oO,ne(s.errorMessage),1)):te("",!0),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(S(),N("div",iO,[x(f,null,{default:Re(()=>[(S(!0),N(je,null,At(a.selectedItems,g=>(S(),ct(c,{key:g.value,readonly:s.disabled,onRemove:h=>a.removeItem(g)},{default:Re(()=>[Ze(ne(g.label),1)]),_:2},1032,["readonly","onRemove"]))),128))]),_:1})])):te("",!0)])])}const Or=Le(zw,[["render",aO],["__scopeId","data-v-1edbcb95"]]),lO={name:"EditRolesModal",mixins:[rr],components:{Toast:sr,Autocomplete:Or},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},userId:{type:[Number,String],required:!0},offerUserEnrolId:{type:[Number,String],required:!0},currentUserRoles:{type:[String,Array],required:!0},courseRoles:{type:Array,required:!0}},emits:["close","success"],data(){return{selectedRoles:[],saving:!1,initialLoading:!0}},created(){this.selectedRoles=[...this.currentUserRoles]},methods:{async saveRoles(){if(!this.selectedRoles.length){this.showWarningMessage("Selecione ao menos um papel.");return}this.saving=!0;try{await Mw(this.offerUserEnrolId,this.selectedRoles.map(t=>t.value))===!0?this.$emit("success",{userId:this.userId,newRoles:this.selectedRoles}):this.showWarningMessage("Nenhum papel foi atualizado.")}catch(e){this.showErrorMessage(e)}finally{this.saving=!1}}}},uO={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},cO={class:"modal-content"},dO={class:"modal-header"},fO={class:"modal-body"},hO={class:"row"},pO={class:"col-md-12"},mO={class:"modal-footer"},gO=["isLoading","disabled"],_O=["disabled"];function vO(e,t,s,i,n,a){const u=L("Autocomplete"),c=L("Toast");return S(),N(je,null,[m("div",{class:"modal-overlay",onClick:t[5]||(t[5]=f=>s.closeOnBackdrop?e.$emit("close"):null)},[m("div",uO,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=es(()=>{},["stop"]))},[m("div",cO,[m("div",dO,[t[7]||(t[7]=m("h3",{class:"modal-title"},"Atualizar papéis",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=f=>e.$emit("close"))},t[6]||(t[6]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",fO,[m("div",hO,[m("div",pO,[x(u,{modelValue:n.selectedRoles,"onUpdate:modelValue":t[1]||(t[1]=f=>n.selectedRoles=f),items:s.courseRoles,placeholder:"Pesquisar...",label:"Selecionar os papéis",required:!0,"no-results-text":"Nenhum papel encontrado"},null,8,["modelValue","items"])])])]),m("div",mO,[m("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...f)=>a.saveRoles&&a.saveRoles(...f)),isLoading:n.saving,disabled:n.saving},ne(n.saving?"Salvando...":"Salvar"),9,gO),m("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=f=>e.$emit("close")),disabled:n.saving}," Cancelar ",8,_O)])])],2)])]),x(c,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],64)}const yO=Le(lO,[["render",vO]]),V3="",bO={name:"PageTabs",props:{modelValue:{type:String,default:""},options:{type:Array,required:!1}},computed:{},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},EO={class:"tabs"};function CO(e,t,s,i,n,a){return S(),N("div",EO,t[0]||(t[0]=[iu('<div class="tab active" data-v-c3c14352><a href="settings.php" class="btn" data-v-c3c14352> Usuários inscritos</a></div><div class="tab" data-v-c3c14352><a href="products.php" class="btn" data-v-c3c14352> Lista de espera</a></div><div class="tab" data-v-c3c14352><a href="orders.php" class="btn" data-v-c3c14352> Inscrições revogadas</a></div>',3)]))}const wO=Le(bO,[["render",CO],["__scopeId","data-v-c3c14352"]]),L3="",OO={name:"AddEnrolmentModal",mixins:[rr],components:{Toast:sr,CustomLabel:wr,CustomSelect:nr,Autocomplete:Or,CustomButton:hs},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},offerClass:{type:Object,required:!0},currentUserIds:{type:Array,default:()=>[]}},emits:["close","success"],data(){return{selectedUsers:[],selectedRoleId:"",enrolmentMethod:"manual",roleOptions:[],userOptions:[],enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],saving:!1,loadingRoles:!1,loadingUsers:!1,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roleOptions.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1},excludedUserIds(){return[...this.currentUserIds,...this.selectedUsers.map(e=>e.value)]}},created(){this.getRoles(),this.getPotentialUsersToEnrol("")},methods:{async getRoles(){try{this.loadingRoles=!0;const e=await ku(this.offerClass.offercourseid);this.roleOptions=e.map(s=>({value:s.id,label:s.name}));const t=this.roleOptions.find(s=>s.value===5);t&&(this.selectedRoleId=t.value)}catch(e){this.showErrorMessage(e)}finally{this.loadingRoles=!1}},async getPotentialUsersToEnrol(e){try{this.loadingUsers=!0;const t=await Tw(this.offerClass.id,e,this.excludedUserIds);this.userOptions=t.map(s=>({value:s.id,label:s.fullname}))}catch(t){this.showErrorMessage(t)}finally{this.loadingUsers=!1}},async createUserEnrolments(){if(this.isFormValid)try{this.saving=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(u=>u.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(u=>parseInt(u.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await Nw({offerclassid:parseInt(this.offerClass.id),userids:e,roleid:parseInt(this.selectedRoleId)});this.showResultAlerts=!0;const s=t.filter(u=>u.success),i=s.length,n=i>0?s.filter(u=>u.reenrol):[],a=t.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=n.length>0?n.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.saving=!1}},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(g,h)=>{if(h==="\\t")return g.split("	");if(h===" ")return g.split(/\s+/);{const p=h.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return g.split(new RegExp(p))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(g=>g.includes("userid"))||!u.some(g=>g.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(g=>g.includes("userid")),f=u.findIndex(g=>g.includes("firstname"));for(let g=1;g<i.length;g++){const h=i[g].trim();if(!h)continue;const p=a(h,t);if(p.length>Math.max(c,f)){const v=p[c].trim(),C=p[f].trim();if(v&&C){if(!/^\d+$/.test(v)){console.warn(`Linha ${g+1}: ID inválido '${v}'. Deve ser um número.`);continue}n.push({id:v,name:C})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]}}},xO={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},SO={class:"modal-content"},IO={class:"modal-header"},DO={class:"modal-body"},NO={key:0,class:"result-alerts"},TO={key:1,class:"failed-messages"},AO={key:2,class:"reenrol-messages"},RO={key:1},PO={class:"row"},MO={class:"col-md-6"},kO={class:"form-group"},VO={class:"col-md-6"},LO={class:"form-group"},$O={key:0,class:"row"},FO={class:"col-md-12"},UO={key:1,class:"form-group"},BO={class:"file-name"},qO={class:"file-size"},HO={key:0,class:"csv-users-preview"},WO={class:"preview-header"},jO={class:"selected-users-container"},GO={class:"filter-tags"},zO={key:0,class:"more-users"},KO={class:"csv-info"},XO={class:"csv-example"},YO=["href"],JO={class:"csv-options-row"},QO={class:"csv-option"},ZO={class:"csv-option"},ex={key:0,class:"modal-footer"};function tx(e,t,s,i,n,a){const u=L("CustomLabel"),c=L("CustomSelect"),f=L("Autocomplete"),g=L("CustomButton"),h=L("Toast");return S(),N(je,null,[m("div",{class:"modal-overlay",onClick:t[14]||(t[14]=p=>s.closeOnBackdrop?e.$emit("close"):null)},[m("div",xO,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[13]||(t[13]=es(()=>{},["stop"]))},[m("div",SO,[m("div",IO,[t[16]||(t[16]=m("h3",{class:"modal-title"},"Inscrever usuários na turma",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[15]||(t[15]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",DO,[n.showResultAlerts?(S(),N("div",NO,[n.batchMessage?(S(),N("div",{key:0,class:me(["alert",n.batchMessageType==="success"?"alert-success":"alert-danger"])},[m("i",{class:me(n.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),Ze(" "+ne(n.batchMessage),1)],2)):te("",!0),n.failedMessages.length>0?(S(),N("div",TO,[(S(!0),N(je,null,At(n.failedMessages,(p,v)=>(S(),N("div",{key:v,class:"alert alert-danger"},[t[17]||(t[17]=m("i",{class:"fas fa-exclamation-triangle"},null,-1)),Ze(" "+ne(p),1)]))),128))])):te("",!0),n.reenrolMessages.length>0?(S(),N("div",AO,[(S(!0),N(je,null,At(n.reenrolMessages,(p,v)=>(S(),N("div",{key:v,class:"alert alert-info"},[t[18]||(t[18]=m("i",{class:"fas fa-exclamation-triangle"},null,-1)),Ze(" "+ne(p),1)]))),128))])):te("",!0)])):(S(),N("div",RO,[t[25]||(t[25]=m("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),m("div",PO,[m("div",MO,[m("div",kO,[x(u,{required:"",text:"Forma de matrícula"}),x(c,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=p=>n.enrolmentMethod=p),options:n.enrolmentMethodOptions,required:""},null,8,["modelValue","options"])])]),m("div",VO,[m("div",LO,[x(u,{required:"",text:"Papel para atribuir"}),x(c,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedRoleId=p),options:n.roleOptions,"is-loading":n.loadingRoles,autoOpen:!1,class:"w-100",required:""},null,8,["modelValue","options","is-loading"])])])]),n.enrolmentMethod==="manual"?(S(),N("div",$O,[m("div",FO,[x(f,{modelValue:n.selectedUsers,"onUpdate:modelValue":t[3]||(t[3]=p=>n.selectedUsers=p),items:n.userOptions,placeholder:"Pesquisar...",label:"Selecionar usuários",required:!0,"has-search-icon":!0,loading:n.loadingUsers,"no-results-text":"Nenhum usuário encontrado",onSearch:t[4]||(t[4]=p=>a.getPotentialUsersToEnrol(p))},null,8,["modelValue","items","loading"])])])):te("",!0),n.enrolmentMethod==="batch"?(S(),N("div",UO,[x(u,{required:"",text:"Matricular usuários a partir de um arquivo CSV"}),m("div",{class:me(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=es((...p)=>a.onDragOver&&a.onDragOver(...p),["prevent"])),onDragleave:t[7]||(t[7]=es((...p)=>a.onDragLeave&&a.onDragLeave(...p),["prevent"])),onDrop:t[8]||(t[8]=es((...p)=>a.onDrop&&a.onDrop(...p),["prevent"])),onClick:t[9]||(t[9]=p=>e.$refs.fileInput.click())},[m("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...p)=>a.handleFileSelect&&a.handleFileSelect(...p))},null,544),n.selectedFile?(S(),N(je,{key:1},[t[21]||(t[21]=m("div",{class:"file-icon"},[m("i",{class:"fas fa-file-alt"})],-1)),m("p",BO,ne(n.selectedFile.name),1),m("p",qO," ("+ne(a.formatFileSize(n.selectedFile.size))+") ",1),t[22]||(t[22]=m("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(S(),N(je,{key:0},[t[19]||(t[19]=m("div",{class:"upload-icon"},[m("i",{class:"fas fa-arrow-down"})],-1)),t[20]||(t[20]=m("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(S(),N("div",HO,[m("div",WO,[m("span",null,"Usuários encontrados no arquivo ("+ne(n.csvUsers.length)+"):",1)]),m("div",jO,[m("div",GO,[(S(!0),N(je,null,At(n.csvUsers.slice(0,5),p=>(S(),N("div",{key:p.id,class:"tag badge badge-primary"},ne(p.name),1))),128)),n.csvUsers.length>5?(S(),N("span",zO,"+"+ne(n.csvUsers.length-5)+" mais",1)):te("",!0)])])])):te("",!0),m("div",KO,[t[24]||(t[24]=m("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),m("div",XO,[t[23]||(t[23]=m("span",{class:"example-label"},"Exemplo CSV",-1)),m("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerClass.id}`,class:"example-csv"}," example.csv ",8,YO)]),m("div",JO,[m("div",QO,[x(u,{text:"Delimitador do CSV"}),x(c,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=p=>n.csvDelimiter=p),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),m("div",ZO,[x(u,{text:"Codificação"}),x(c,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=p=>n.csvEncoding=p),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):te("",!0),t[26]||(t[26]=m("div",{class:"required-fields-message mt-5"},[m("hr"),m("div",{class:"form-info"},[Ze(" Este formulário contém campos obrigatórios marcados com "),m("i",{class:"fa fa-exclamation-circle text-danger"})])],-1))]))]),n.showResultAlerts?te("",!0):(S(),N("div",ex,[x(g,{variant:"primary",label:n.saving?"Inscrevendo...":"Inscrever usuários","is-loading":n.saving,disabled:n.saving||!a.isFormValid,onClick:a.createUserEnrolments},null,8,["label","is-loading","disabled","onClick"]),x(g,{variant:"secondary",label:"Cancelar",disabled:n.saving,onClick:t[12]||(t[12]=p=>e.$emit("close"))},null,8,["disabled"])]))])],2)])]),x(h,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],64)}const sx=Le(OO,[["render",tx],["__scopeId","data-v-65737576"]]),$3="",rx={name:"EnrollmentDetailsModal",components:{CustomButton:hs},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{}},nx={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},ox={class:"modal-content"},ix={class:"modal-header"},ax={key:0,class:"modal-body"},lx={class:"details-container"},ux={class:"detail-row"},cx={class:"detail-value"},dx={class:"detail-row"},fx={class:"detail-value"},hx={class:"detail-row"},px={class:"detail-value"},mx={class:"detail-row"},gx={class:"detail-value"},_x={class:"detail-row"},vx={class:"detail-value"},yx={key:1,class:"modal-body no-data"},bx={class:"modal-footer"};function Ex(e,t,s,i,n,a){const u=L("CustomButton");return s.show?(S(),N("div",{key:0,class:"modal-overlay",onClick:t[3]||(t[3]=c=>e.$emit("close"))},[m("div",nx,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=es(()=>{},["stop"]))},[m("div",ox,[m("div",ix,[t[5]||(t[5]=m("h3",{class:"modal-title"},"Informações da matrícula",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[4]||(t[4]=[m("i",{class:"fas fa-times"},null,-1)]))]),s.user?(S(),N("div",ax,[m("div",lx,[m("div",ux,[t[6]||(t[6]=m("div",{class:"detail-label"},"Nome completo",-1)),m("div",cx,ne(s.user.fullName),1)]),m("div",dx,[t[7]||(t[7]=m("div",{class:"detail-label"},"Curso",-1)),m("div",fx,ne(s.courseName),1)]),m("div",hx,[t[8]||(t[8]=m("div",{class:"detail-label"},"Método de inscrição",-1)),m("div",px,ne(s.user.enrolName),1)]),m("div",mx,[t[9]||(t[9]=m("div",{class:"detail-label"},"Estado",-1)),m("div",gx,[m("span",{class:me(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},ne(s.user.statusName),3)])]),m("div",_x,[t[10]||(t[10]=m("div",{class:"detail-label"},"Matrícula criada",-1)),m("div",vx,ne(s.user.timeCreatedFormatted),1)])])])):(S(),N("div",yx,"Nenhum dado disponível")),m("div",bx,[x(u,{variant:"secondary",label:"Cancelar",disabled:e.saving,onClick:t[1]||(t[1]=c=>e.$emit("close"))},null,8,["disabled"])])])],2)])])):te("",!0)}const Cx=Le(rx,[["render",Ex],["__scopeId","data-v-ba5927bb"]]),wx={name:"EditEnrollmentModal",components:{CustomLabel:wr,CustomInput:On,CustomSelect:nr,CustomButton:hs,CustomCheckbox:Yn},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},enrollment:{type:Object,default:null}},emits:["close","success"],data(){return{saving:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},created(){this.enrollment&&this.initializeForm()},watch:{enrollment(e){e&&this.initializeForm()}},methods:{initializeForm(){if(!this.enrollment)return;this.formData.status=this.enrollment.status;const e=this.enrollment.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.enrollment.timeend){const i=new Date(this.enrollment.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.enrollment.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(f=>parseInt(f.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},async saveChanges(){var e;if((e=this.enrollment)!=null&&e.offeruserenrolid)try{this.saving=!0;const t=parseInt(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.showErrorMessage("A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await Aw({offeruserenrolid:this.enrollment.offeruserenrolid,status:t,timestart:s,timeend:i})?this.$emit("success"):this.showErrorMessage("Não foi possível editar a matrícula. Por favor, tente novamente.")}catch(t){this.showErrorMessage(t)}finally{this.saving=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},Ox={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},xx={class:"modal-content"},Sx={class:"modal-header"},Ix={class:"modal-title"},Dx={class:"modal-body"},Nx={class:"row"},Tx={class:"col-md-6"},Ax={class:"form-group"},Rx={class:"row"},Px={class:"col-md-6"},Mx={class:"form-group"},kx={class:"row"},Vx={class:"col-md-5"},Lx={class:"form-group"},$x={class:"col-md-4 ml-md-n3 mt-n1 mt-md-0"},Fx={class:"form-group"},Ux={class:"col-md-3 ml-md-n3 mt-n3 mt-md-0"},Bx={class:"form-group"},qx={class:"row"},Hx={class:"col-md-6"},Wx={class:"form-group"},jx={class:"row"},Gx={class:"col-md-5"},zx={class:"form-group"},Kx={class:"col-md-4 ml-md-n3 mt-n1 mt-md-0"},Xx={class:"form-group"},Yx={class:"col-md-3 ml-md-n3 mt-n3 mt-md-0"},Jx={class:"form-group"},Qx={class:"d-flex flex-column mt-3"},Zx={key:0,class:"text-muted"},eS={key:1,class:"text-muted"},tS={class:"modal-footer"};function sS(e,t,s,i,n,a){const u=L("CustomLabel"),c=L("CustomInput"),f=L("CustomSelect"),g=L("CustomCheckbox"),h=L("CustomButton");return S(),N("div",{class:"modal-overlay",onClick:t[12]||(t[12]=p=>e.$emit("close"))},[m("div",Ox,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[11]||(t[11]=es(()=>{},["stop"]))},[m("div",xx,[m("div",Sx,[m("h3",Ix," Editar matrícula de "+ne(s.enrollment?s.enrollment.fullName:""),1),m("button",{class:"modal-close",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[13]||(t[13]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",Dx,[m("div",Nx,[m("div",Tx,[m("div",Ax,[x(u,{text:"Método de inscrição"}),x(c,{type:"text",modelValue:s.enrollment.enrolName,"onUpdate:modelValue":t[1]||(t[1]=p=>s.enrollment.enrolName=p),disabled:!0},null,8,["modelValue"])])])]),m("div",Rx,[m("div",Px,[m("div",Mx,[x(u,{text:"Estado"}),x(f,{modelValue:n.formData.status,"onUpdate:modelValue":t[2]||(t[2]=p=>n.formData.status=p),options:n.statusOptions},null,8,["modelValue","options"])])])]),m("div",kx,[m("div",Vx,[m("div",Lx,[x(u,{text:"Matrícula começa"}),x(c,{type:"date",modelValue:n.formData.startDateStr,"onUpdate:modelValue":t[3]||(t[3]=p=>n.formData.startDateStr=p),disabled:!n.formData.enableStartDate,required:n.formData.enableStartDate,id:"startDate"},null,8,["modelValue","disabled","required"])])]),m("div",$x,[m("div",Fx,[x(u,{text:" ",className:"d-none d-md-block"}),x(c,{type:"time",modelValue:n.formData.startTimeStr,"onUpdate:modelValue":t[4]||(t[4]=p=>n.formData.startTimeStr=p),disabled:!n.formData.enableStartDate,required:n.formData.enableStartDate,id:"startTime"},null,8,["modelValue","disabled","required"])])]),m("div",Ux,[m("div",Bx,[x(u,{text:" ",className:"d-none d-md-block"}),x(g,{modelValue:n.formData.enableStartDate,"onUpdate:modelValue":t[5]||(t[5]=p=>n.formData.enableStartDate=p),id:"enableStartDate",label:"Habilitar"},null,8,["modelValue"])])])]),m("div",qx,[m("div",Hx,[m("div",Wx,[x(u,{text:"Período de validade da matrícula"}),x(f,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[6]||(t[6]=p=>n.formData.validityPeriod=p),options:n.validityPeriodOptions,onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),m("div",jx,[m("div",Gx,[m("div",zx,[x(u,{text:"Matrícula termina"}),x(c,{type:"date",modelValue:n.formData.endDateStr,"onUpdate:modelValue":t[7]||(t[7]=p=>n.formData.endDateStr=p),disabled:!n.formData.enableEndDate,required:n.formData.enableEndDate,id:"endDate"},null,8,["modelValue","disabled","required"])])]),m("div",Kx,[m("div",Xx,[x(u,{text:" ",className:"d-none d-md-block"}),x(c,{type:"time",modelValue:n.formData.endTimeStr,"onUpdate:modelValue":t[8]||(t[8]=p=>n.formData.endTimeStr=p),disabled:!n.formData.enableEndDate,required:n.formData.enableEndDate,id:"endTime"},null,8,["modelValue","disabled","required"])])]),m("div",Yx,[m("div",Jx,[x(u,{text:" ",className:"d-none d-md-block"}),x(g,{modelValue:n.formData.enableEndDate,"onUpdate:modelValue":t[9]||(t[9]=p=>n.formData.enableEndDate=p),id:"enableEndDate",label:"Habilitar"},null,8,["modelValue"])])])]),m("div",Qx,[s.enrollment.creatorName?(S(),N("small",Zx,ne(`Criado por ${s.enrollment.creatorName} em ${s.enrollment.createdDate}`),1)):te("",!0),s.enrollment.modifierName?(S(),N("small",eS,ne(`Atualizado por ${s.enrollment.modifierName} em ${s.enrollment.modifiedDate}`),1)):te("",!0)])]),m("div",tS,[x(h,{variant:"primary",label:n.saving?"Salvando...":"Salvar","is-loading":n.saving,disabled:n.saving,onClick:a.saveChanges},null,8,["label","is-loading","disabled","onClick"]),x(h,{variant:"secondary",label:"Cancelar",disabled:n.saving,onClick:t[10]||(t[10]=p=>e.$emit("close"))},null,8,["disabled"])])])],2)])])}const rS=Le(wx,[["render",sS]]),F3="",U3="",nS={name:"BulkEditEnrollmentModal",components:{Pagination:Cn,CustomTable:wn,CustomSelect:nr},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[f,g]=this.formData.startTimeStr.split(":").map(Number),h=new Date(a,u-1,c,f,g,0,0);t=Math.floor(h.getTime()/1e3);const p=h.getTimezoneOffset()*60;t+=p}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[f,g]=this.formData.endTimeStr.split(":").map(Number),h=new Date(a,u-1,c,f,g,0,0);s=Math.floor(h.getTime()/1e3);const p=h.getTimezoneOffset()*60;s+=p}const i=this.users.filter(a=>a.offerUserEnrolId).map(a=>a.offerUserEnrolId);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await Rw({offerUserEnrolIds:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(f=>f.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},oS={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},iS={class:"modal-content"},aS={class:"modal-header"},lS={class:"modal-body"},uS={class:"enrollment-form"},cS={class:"table-container"},dS={class:"form-row"},fS={class:"form-field"},hS={class:"select-wrapper"},pS={class:"form-row"},mS={class:"form-field date-time-field"},gS={class:"date-field"},_S=["disabled"],vS={class:"time-field"},yS=["disabled"],bS={class:"enable-checkbox"},ES={class:"form-row"},CS={class:"form-field date-time-field"},wS={class:"date-field"},OS=["disabled"],xS={class:"time-field"},SS=["disabled"],IS={class:"enable-checkbox"},DS={class:"modal-footer"},NS={class:"footer-buttons"},TS=["disabled"];function AS(e,t,s,i,n,a){const u=L("CustomTable"),c=L("Pagination"),f=L("CustomSelect");return s.show?(S(),N("div",{key:0,class:"modal-overlay",onClick:t[17]||(t[17]=g=>e.$emit("close"))},[m("div",oS,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[16]||(t[16]=es(()=>{},["stop"]))},[m("div",iS,[m("div",aS,[t[19]||(t[19]=m("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=g=>e.$emit("close"))},t[18]||(t[18]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",lS,[m("div",uS,[m("div",null,[m("div",cS,[x(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?Jt((S(),ct(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=g=>n.currentPage=g),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=g=>n.perPage=g),total:s.users.length},null,8,["current-page","per-page","total"])),[[mu,s.users.length>n.perPage]]):te("",!0),t[20]||(t[20]=m("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),m("div",dS,[t[21]||(t[21]=m("div",{class:"form-label"},"Alterar o status",-1)),m("div",fS,[m("div",hS,[x(f,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=g=>n.formData.status=g),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),m("div",pS,[t[23]||(t[23]=m("div",{class:"form-label"},"Alterar data de início",-1)),m("div",mS,[m("div",gS,[Jt(m("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=g=>n.formData.startDateStr=g),class:"form-control",onChange:t[5]||(t[5]=(...g)=>a.handleStartDateChange&&a.handleStartDateChange(...g)),disabled:!n.formData.enableStartDate},null,40,_S),[[yr,n.formData.startDateStr]])]),m("div",vS,[Jt(m("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=g=>n.formData.startTimeStr=g),class:"form-control",onChange:t[7]||(t[7]=(...g)=>a.handleStartTimeChange&&a.handleStartTimeChange(...g)),disabled:!n.formData.enableStartDate},null,40,yS),[[yr,n.formData.startTimeStr]])]),m("div",bS,[Jt(m("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=g=>n.formData.enableStartDate=g),class:"custom-checkbox"},null,512),[[Rh,n.formData.enableStartDate]]),t[22]||(t[22]=m("label",{for:"enable-start-date"},"Habilitar",-1))])])]),m("div",ES,[t[25]||(t[25]=m("div",{class:"form-label"},"Alterar data de fim",-1)),m("div",CS,[m("div",wS,[Jt(m("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=g=>n.formData.endDateStr=g),class:"form-control",onChange:t[10]||(t[10]=(...g)=>a.handleEndDateChange&&a.handleEndDateChange(...g)),disabled:!n.formData.enableEndDate},null,40,OS),[[yr,n.formData.endDateStr]])]),m("div",xS,[Jt(m("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=g=>n.formData.endTimeStr=g),class:"form-control",onChange:t[12]||(t[12]=(...g)=>a.handleEndTimeChange&&a.handleEndTimeChange(...g)),disabled:!n.formData.enableEndDate},null,40,SS),[[yr,n.formData.endTimeStr]])]),m("div",IS,[Jt(m("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=g=>n.formData.enableEndDate=g),class:"custom-checkbox"},null,512),[[Rh,n.formData.enableEndDate]]),t[24]||(t[24]=m("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),m("div",DS,[t[26]||(t[26]=m("div",{class:"footer-spacer"},null,-1)),m("div",NS,[m("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...g)=>a.saveChanges&&a.saveChanges(...g)),disabled:n.isSubmitting},ne(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,TS),m("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=g=>e.$emit("close"))}," Cancelar ")])])])],2)])])):te("",!0)}const RS=Le(nS,[["render",AS],["__scopeId","data-v-404ad0d9"]]),B3="",PS={name:"Alert",props:{type:{type:String,default:"info"},text:{type:String,required:!0},icon:{type:String,required:!1}},computed:{textMessage(){return this.text.replace("-","‑")}}},MS=["innerHTML"];function kS(e,t,s,i,n,a){return S(),N("div",{class:me(["alert flex-column flex-md-row align-items-center",`alert-${s.type}`])},[s.icon?(S(),N("i",{key:0,class:me(s.icon)},null,2)):te("",!0),m("span",{innerHTML:a.textMessage},null,8,MS)],2)}const pa=Le(PS,[["render",kS],["__scopeId","data-v-f42be2df"]]),q3="",VS={name:"BulkDeleteEnrollmentModal",components:{Alert:pa,Pagination:Cn,CustomSelect:nr,CustomTable:wn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},LS={class:"modal-header"},$S={class:"modal-body"},FS={class:"enrollment-form"},US={class:"table-container"},BS={class:"modal-footer"},qS={class:"footer-buttons"},HS=["disabled"];function WS(e,t,s,i,n,a){const u=L("CustomTable"),c=L("Pagination"),f=L("Alert");return s.show?(S(),N("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=g=>e.$emit("close"))},[m("div",{class:"modal-container",onClick:t[5]||(t[5]=es(()=>{},["stop"]))},[m("div",LS,[t[8]||(t[8]=m("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=g=>e.$emit("close"))},t[7]||(t[7]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",$S,[m("div",FS,[m("div",US,[x(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?Jt((S(),ct(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=g=>n.currentPage=g),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=g=>n.perPage=g),total:s.users.length},null,8,["current-page","per-page","total"])),[[mu,s.users.length>n.perPage]]):te("",!0)]),x(f,{type:"primary",text:"Tem certeza de que deseja cancelar essas inscrições? Esta ação é irreversível.<br>Os usuários serão completamente removidos da turma e do curso correspondente, incluindo todo o histórico de progresso e registros de atividade."})]),m("div",BS,[m("div",qS,[m("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=g=>e.$emit("confirm")),disabled:n.isSubmitting},ne(n.isSubmitting?"Desinscrevendo...":"Desinscrever usuários"),9,HS),m("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=g=>e.$emit("close"))}," Cancelar ")])])])])):te("",!0)}const jS=Le(VS,[["render",WS],["__scopeId","data-v-7899e1fc"]]),H3="",GS={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{navigateToBack(){this.$emit("click")}}};function zS(e,t,s,i,n,a){return S(),N("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.navigateToBack&&a.navigateToBack(...u))},[t[1]||(t[1]=m("i",{class:"fas fa-angle-left"},null,-1)),Ze(" "+ne(s.label),1)])}const ti=Le(GS,[["render",zS],["__scopeId","data-v-774dfbf5"]]),W3="",KS={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},XS=["src"];function YS(e,t,s,i,n,a){return S(),N("div",{class:"user-avatar",style:As(a.avatarStyle)},[a.hasImage?(S(),N("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,XS)):(S(),N("div",{key:1,class:"avatar-initials",style:As({backgroundColor:a.backgroundColor})},ne(a.initials),5))],4)}const JS=Le(KS,[["render",YS],["__scopeId","data-v-eed19d8a"]]),j3="",QS={name:"ListEnrollments",mixins:[rr],components:{GearIcon:Lw,CircleInfoIcon:Uw,PageTabs:wO,CustomLabel:wr,CustomTable:wn,CustomSelect:nr,CustomInput:On,CustomCheckbox:Yn,CustomButton:hs,FilterSection:kp,FilterRow:ca,FilterGroup:da,FilterActions:Vp,FilterTag:ei,FilterTags:ha,Pagination:Cn,PageHeader:Xn,ConfirmationModal:Jn,Autocomplete:Or,AddEnrolmentModal:sx,EnrollmentDetailsModal:Cx,Toast:sr,EditRolesModal:yO,EditEnrollmentModal:rS,BulkEditEnrollmentModal:RS,BulkDeleteEnrollmentModal:jS,BackButton:ti,UserAvatar:JS,LFLoading:Zo},props:{offerClassId:{type:[Number,String],required:!0}},data(){return{filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],roleOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO",value:"timeStartFormatted",sortable:!0},{text:"DATA FIM",value:"timeEndFormatted",sortable:!0},{text:"PRAZO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progressFormatted",sortable:!1},{text:"SITUAÇÃO",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,selectedEnrollment:null,showEditRolesModal:!1,showEnrollmentModal:!1,showAddEnrolmentModal:!1,showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showBulkDeleteEnrollmentModal:!1,offerClass:{},selectedUsers:[],selectedBulkAction:""}},async created(){if(!this.offerClassId)throw new Error("ID da turma não foi definido.");await this.getOfferClass(),await this.getRegisteredUsers(),await this.getRoles()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)},currentEnrolmentUserIds(){return this.enrolments.map(e=>e.userId)},totalEnrolmentsInfo(){return this.totalEnrolments===0?"Nenhum participante encontrado":this.totalEnrolments===1?"1 participante encontrado":`${this.totalEnrolments} participantes encontrados`}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.getRegisteredUsers())},currentPage(e,t){e!==t&&this.getRegisteredUsers()}},methods:{async getOfferClass(){try{this.loading=!0;const e=await Mp(this.offerClassId);this.offerClass=e}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getRegisteredUsers(){try{this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerClassId,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},{enrolments:s,total:i}=await Dw(t);this.totalEnrolments=i,this.enrolments=s.map(n=>{const a=this.formatRoleOptions(n.roles);return{...n,id:n.userid,userId:n.userid,offerUserEnrolId:n.offeruserenrolid,fullName:n.fullname,roles:a,rolesFormatted:this.formatRoles(a),enrolName:n.enrol_name,timeCreatedFormatted:this.formatDateTime(n.timecreated),timeStartFormatted:this.formatDate(n.timestart),timeEndFormatted:this.formatDate(n.timeend),deadline:n.enrolperiod,progressFormatted:this.formatProgress(n.progress),situationName:n.situation_name,statusName:n.status===0?"Ativo":"Suspenso",creatorName:n.creatorname,modifierName:n.modifiername,createdDate:n.createddate,modifiedDate:n.modifieddate}})}catch(e){this.error=e.message}finally{this.loading=!1}},async getRoles(){try{this.loading=!0;const e=await ku(this.offerClass.offercourseid);this.roleOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},showEditRoles(e){this.selectedEnrollment=e,this.showEditRolesModal=!0},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){if(!Array.isArray(e))return"-";if(e.length>2)return e.slice(0,2).map(t=>t.label).join(", ")+" + "+(e.length-2);if(Array.isArray(e)&&e.length>0)return e.map(t=>t.label).join(", ")},formatRoleOptions(e){return e.map(t=>({value:t.id,label:t.name}))},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await Vu({offerclassid:this.offerClassId,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});this.nameOptions=t.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await Vu({offerclassid:this.offerClassId,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});this.cpfOptions=t.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await Vu({offerclassid:this.offerClassId,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});this.emailOptions=t.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.getRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.getRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.getRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.getRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.getRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.getRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",timeStartFormatted:"startdate",timeEndFormatted:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.offerClass&&((e=this.offerClass)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showAddEnrolmentModal=!0},closeEnrolmentModal(){this.showAddEnrolmentModal=!1},async navigateToBack(){this.$router.push({name:"offer.edit",params:{id:this.offerClass.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.offerClass.courseid}`;window.location.href=t},showEnrollmentDetails(e){this.selectedEnrollment=e,this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedEnrollment=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedEnrollment=null},async handleEditEnrollmentSuccess(e){this.showEditEnrollmentModal=!1,this.selectedEnrollment=null,this.showSuccessMessage("Matrícula editada com sucesso."),await this.getRegisteredUsers()},handleRoleUpdateSuccess(e){const t=this.enrolments.findIndex(s=>s.userId===e.userId);t!==-1?(this.enrolments[t].roles=e.newRoles,this.enrolments[t].rolesFormatted=this.formatRoles(e.newRoles)):this.getRegisteredUsers(),this.showEditRolesModal=!1,this.showSuccessMessage("Papéis atualizados com sucesso.")},editUser(e){this.selectedEnrollment=e,this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&e.push(n.offerUserEnrolId)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await Pw(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.getRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.getRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.offerClass.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.timeStartFormatted||"","Data de Término":i.timeEndFormatted||"",Prazo:i.deadline||"",Progresso:i.progressFormatted||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(f=>f.replace(/([A-Z])/g," $1").replace(/^./,g=>g.toUpperCase()).trim()),n=t+[i.join(","),...e.map(f=>s.map(g=>{const h=f[g]||"";return`"${String(h).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(f=>{const g=c[f]||"";return`"${String(g).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let W=0;W<t.length;W++){const ie=t[W].replace(/([A-Z])/g," $1").replace(/^./,q=>q.toUpperCase()).trim();s.push(ie)}let i="";for(let W=0;W<s.length;W++)i+="<th>"+s[W]+"</th>";let n="";for(let W=0;W<e.length;W++){let ie="<tr>";for(let q=0;q<t.length;q++)ie+="<td>"+(e[W][t[q]]||"")+"</td>";ie+="</tr>",n+=ie}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",f="<table><thead><tr>",g="</tr></thead><tbody>",h="</tbody></table>",p='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",v="</body></html>",C=a+u+c+f+i+g+n+h+p+v,P=new Blob([C],{type:"text/html;charset=utf-8;"}),A=URL.createObjectURL(P),oe=document.createElement("a");oe.setAttribute("href",A),oe.setAttribute("download","usuarios_matriculados.html"),oe.style.visibility="hidden",document.body.appendChild(oe),oe.click(),document.body.removeChild(oe),URL.revokeObjectURL(A),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(f=>{const g=s.map(h=>{const p=f[h]||"";return'"'+String(p).replace(/"/g,'""')+'"'});i.push(g.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.getRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)}}},ZS={id:"enrollments-view"},eI={class:"filters-section mb-3"},tI={class:"row"},sI={class:"col-xl-8"},rI={class:"row"},nI={class:"col-md-4"},oI={class:"filter-input-container position-relative"},iI={key:0,class:"dropdown-menu show position-absolute w-100"},aI=["onClick"],lI={class:"col-md-4 mt-3 mt-md-0"},uI={class:"filter-input-container position-relative"},cI={key:0,class:"dropdown-menu show position-absolute w-100"},dI=["onClick"],fI={class:"col-md-4 mt-3 mt-md-0"},hI={class:"filter-input-container position-relative"},pI={key:0,class:"dropdown-menu show position-absolute w-100"},mI=["onClick"],gI={class:"col-xl-4"},_I={class:"d-flex justify-content-between justify-content-md-end mb-5 mb-xl-0",style:{gap:"12px"}},vI={key:0,class:"my-4"},yI={key:1,class:"alert alert-danger"},bI={class:"my-3"},EI={class:"empty-state"},CI={class:"no-results"},wI={class:"checkbox-container"},OI=["checked","indeterminate"],xI={class:"checkbox-container"},SI=["checked","onChange"],II=["href","title"],DI={class:"user-name-link"},NI=["onClick"],TI={class:"progress-container"},AI={class:"progress-text"},RI={class:"action-buttons"},PI=["onClick"],MI=["onClick"],kI=["onClick"],VI={class:"selected-users-actions"},LI={class:"bulk-actions-container"};function $I(e,t,s,i,n,a){var De,$e,ue;const u=L("BackButton"),c=L("PageHeader"),f=L("PageTabs"),g=L("CustomLabel"),h=L("CustomButton"),p=L("FilterTag"),v=L("FilterTags"),C=L("UserAvatar"),P=L("CircleInfoIcon"),A=L("GearIcon"),oe=L("CustomTable"),W=L("Pagination"),ie=L("EnrollmentDetailsModal"),q=L("AddEnrolmentModal"),ye=L("EditRolesModal"),se=L("EditEnrollmentModal"),be=L("BulkEditEnrollmentModal"),xe=L("BulkDeleteEnrollmentModal"),ke=L("LFLoading"),de=L("Toast");return S(),N("div",ZS,[x(c,{title:"Usuários inscritos"},{actions:Re(()=>[x(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1}),x(f),m("div",eI,[m("div",tI,[m("div",sI,[m("div",rI,[m("div",nI,[m("div",oI,[x(g,{text:"Filtrar por nome"}),Jt(m("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[0]||(t[0]=R=>n.nameSearchInput=R),onInput:t[1]||(t[1]=(...R)=>a.handleNameInput&&a.handleNameInput(...R)),onFocus:t[2]||(t[2]=R=>n.showNameDropdown=n.nameOptions.length>0),onBlur:t[3]||(t[3]=R=>a.clearOptions("name"))},null,544),[[yr,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(S(),N("div",iI,[(S(!0),N(je,null,At(n.nameOptions,R=>(S(),N("button",{key:R.id,type:"button",class:"dropdown-item",onClick:G=>a.selectNameOption(R)},ne(R.label),9,aI))),128))])):te("",!0)])]),m("div",lI,[m("div",uI,[x(g,{text:"Filtrar por CPF"}),Jt(m("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[4]||(t[4]=R=>n.cpfSearchInput=R),onInput:t[5]||(t[5]=(...R)=>a.handleCpfInput&&a.handleCpfInput(...R)),onFocus:t[6]||(t[6]=R=>n.showCpfDropdown=n.cpfOptions.length>0),onBlur:t[7]||(t[7]=R=>a.clearOptions("cpf"))},null,544),[[yr,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(S(),N("div",cI,[(S(!0),N(je,null,At(n.cpfOptions,R=>(S(),N("button",{key:R.id,type:"button",class:"dropdown-item",onClick:G=>a.selectCpfOption(R)},ne(R.label),9,dI))),128))])):te("",!0)])]),m("div",fI,[m("div",hI,[x(g,{text:"Filtrar por e-mail"}),Jt(m("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[8]||(t[8]=R=>n.emailSearchInput=R),onInput:t[9]||(t[9]=(...R)=>a.handleEmailInput&&a.handleEmailInput(...R)),onFocus:t[10]||(t[10]=R=>n.showEmailDropdown=n.emailOptions.length>0),onBlur:t[11]||(t[11]=R=>a.clearOptions("email"))},null,544),[[yr,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(S(),N("div",pI,[(S(!0),N(je,null,At(n.emailOptions,R=>(S(),N("button",{key:R.id,type:"button",class:"dropdown-item",onClick:G=>a.selectEmailOption(R)},ne(R.label),9,mI))),128))])):te("",!0)])])])]),m("div",gI,[x(g,{text:" "}),m("div",_I,[!n.offerClass||((De=n.offerClass)==null?void 0:De.operational_cycle)!==2?(S(),ct(h,{key:0,variant:"primary",label:"Inscrever usuários",onClick:a.addNewUser},null,8,["onClick"])):te("",!0),!n.offerClass||(($e=n.offerClass)==null?void 0:$e.operational_cycle)!==2?(S(),ct(h,{key:1,variant:"secondary",label:"Inscrição por turmas",onClick:a.addNewUser},null,8,["onClick"])):te("",!0)])])])]),x(v,null,{default:Re(()=>[(S(!0),N(je,null,At(n.filteredUsers,R=>(S(),ct(p,{key:R.id,onRemove:G=>a.removeFilter(R.id||R.value)},{default:Re(()=>[Ze(ne(R.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.filteredUsers.length>0?(S(),N("div",vI,[m("button",{type:"button",class:"btn btn-secondary",onClick:t[12]||(t[12]=(...R)=>a.clearFilteredUsers&&a.clearFilteredUsers(...R))}," Limpar ")])):te("",!0),n.error?(S(),N("div",yI,[t[21]||(t[21]=m("i",{class:"fas fa-exclamation-circle"},null,-1)),Ze(" "+ne(n.error),1)])):te("",!0),m("div",bI,ne(a.totalEnrolmentsInfo),1),x(oe,{headers:n.tableHeaders,items:n.enrolments,tableClass:"table-hover","sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"empty-state":Re(()=>[m("div",EI,[m("span",CI,ne(n.loading?"Carregando registros...":"Nenhum registro encontrado..."),1)])]),"header-select":Re(()=>[m("div",wI,[m("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[13]||(t[13]=(...R)=>a.toggleSelectAll&&a.toggleSelectAll(...R)),class:"custom-checkbox"},null,40,OI)])]),"item-select":Re(({item:R})=>[m("div",xI,[m("input",{type:"checkbox",checked:a.isSelected(R.id),onChange:G=>a.toggleSelectUser(R.id),class:"custom-checkbox"},null,40,SI)])]),"item-fullName":Re(({item:R})=>[m("a",{class:"user-name-container",href:`/user/view.php?id=${R.id}`,title:"Ver perfil de "+R.fullName},[x(C,{"full-name":R.fullName,size:36},null,8,["full-name"]),m("span",DI,ne(R.fullName),1)],8,II)]),"item-email":Re(({item:R})=>[Ze(ne(R.email),1)]),"item-cpf":Re(({item:R})=>[Ze(ne(R.cpf),1)]),"item-roles":Re(({item:R})=>[m("span",{onClick:G=>a.showEditRoles(R)},[Ze(ne(R.rolesFormatted)+" ",1),t[22]||(t[22]=m("i",{class:"fas fa-pencil-alt edit-icon ml-1","aria-hidden":"true"},null,-1))],8,NI)]),"item-groups":Re(({item:R})=>[Ze(ne(R.groups),1)]),"item-timeStartFormatted":Re(({item:R})=>[Ze(ne(R.timeStartFormatted),1)]),"item-timeEndFormatted":Re(({item:R})=>[Ze(ne(R.timeEndFormatted),1)]),"item-deadline":Re(({item:R})=>[Ze(ne(R.deadline),1)]),"item-progressFormatted":Re(({item:R})=>[m("div",TI,[m("div",{class:"progress-bar",style:As({width:R.progressFormatted})},null,4),m("span",AI,ne(R.progressFormatted),1)])]),"item-situation":Re(({item:R})=>[Ze(ne(R.situationName),1)]),"item-grade":Re(({item:R})=>[Ze(ne(R.grade),1)]),"item-status":Re(({item:R})=>[m("span",{class:me(["badge",R.status===0?"badge-success":"badge-danger"])},ne(R.statusName),3)]),"item-actions":Re(({item:R})=>[m("div",RI,[m("button",{class:"btn-action btn-information",onClick:G=>a.showEnrollmentDetails(R),title:"Informações da matrícula"},[x(P,{class:""})],8,PI),m("button",{class:"btn-action btn-settings",onClick:G=>a.editUser(R),title:"Editar matrícula"},[x(A,{class:""})],8,MI),m("button",{class:"btn-action btn-delete",onClick:G=>a.editUser(R),title:"Excluir matrícula"},t[23]||(t[23]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,kI)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),x(W,{"current-page":n.currentPage,"onUpdate:currentPage":t[14]||(t[14]=R=>n.currentPage=R),"per-page":n.perPage,"onUpdate:perPage":t[15]||(t[15]=R=>n.perPage=R),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),m("div",VI,[m("div",LI,[t[25]||(t[25]=m("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),Jt(m("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[16]||(t[16]=R=>n.selectedBulkAction=R),onChange:t[17]||(t[17]=(...R)=>a.handleBulkAction&&a.handleBulkAction(...R))},t[24]||(t[24]=[iu('<option value="" data-v-b7d6f9e6>Escolher...</option><optgroup label="Comunicação" data-v-b7d6f9e6><option value="message" data-v-b7d6f9e6>Enviar uma mensagem</option><option value="note" data-v-b7d6f9e6>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-b7d6f9e6><option value="download_csv" data-v-b7d6f9e6> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-b7d6f9e6>Microsoft excel (.xlsx)</option><option value="download_html" data-v-b7d6f9e6>Tabela HTML</option><option value="download_json" data-v-b7d6f9e6> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-b7d6f9e6>OpenDocument (.ods)</option><option value="download_pdf" data-v-b7d6f9e6> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-b7d6f9e6><option value="edit_enrolment" data-v-b7d6f9e6> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-b7d6f9e6> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Mh,n.selectedBulkAction]])])]),x(ie,{show:n.showEnrollmentModal,user:n.selectedEnrollment,"course-name":((ue=n.offerClass)==null?void 0:ue.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),n.showAddEnrolmentModal?(S(),ct(q,{key:2,offerClass:n.offerClass,currentUserIds:a.currentEnrolmentUserIds,onClose:a.closeEnrolmentModal,onSuccess:a.getRegisteredUsers},null,8,["offerClass","currentUserIds","onClose","onSuccess"])):te("",!0),n.showEditRolesModal?(S(),ct(ye,{key:3,userId:n.selectedEnrollment.id,offerUserEnrolId:n.selectedEnrollment.offerUserEnrolId,currentUserRoles:n.selectedEnrollment.roles,courseRoles:n.roleOptions,onClose:t[18]||(t[18]=R=>n.showEditRolesModal=!1),onSuccess:a.handleRoleUpdateSuccess},null,8,["userId","offerUserEnrolId","currentUserRoles","courseRoles","onSuccess"])):te("",!0),n.showEditEnrollmentModal?(S(),ct(se,{key:4,enrollment:n.selectedEnrollment,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess},null,8,["enrollment","onClose","onSuccess"])):te("",!0),x(be,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(R=>n.enrolments.find(G=>G.id===R)).filter(Boolean),onClose:t[19]||(t[19]=R=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","onSuccess","onError"]),x(xe,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(R=>n.enrolments.find(G=>G.id===R)).filter(Boolean),onClose:t[20]||(t[20]=R=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","onConfirm","onError"]),x(ke,{"is-loading":n.loading},null,8,["is-loading"]),x(de,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const FI=Le(QS,[["render",$I],["__scopeId","data-v-b7d6f9e6"]]),UI={name:"WaitingList",props:{modelValue:{type:String,default:""}},methods:{}};function BI(e,t,s,i,n,a){return S(),N("div")}const qI=Le(UI,[["render",BI]]),HI={name:"Revoked",props:{modelValue:{type:String,default:""}},methods:{}};function WI(e,t,s,i,n,a){return S(),N("div")}const jI=Le(HI,[["render",WI]]),G3="",GI={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},zI={class:"text-editor-container"},KI={class:"editor-toolbar"},XI={class:"toolbar-group"},YI=["disabled"],JI=["disabled"],QI=["disabled"],ZI=["disabled"],eD={class:"toolbar-group"},tD=["disabled"],sD=["disabled"],rD=["contenteditable"],nD=["rows","placeholder","disabled"];function oD(e,t,s,i,n,a){return S(),N("div",zI,[s.label?(S(),N("label",{key:0,class:me(["filter-label",{disabled:s.disabled}])},ne(s.label),3)):te("",!0),m("div",{class:me(["editor-container",{disabled:s.disabled}])},[m("div",KI,[m("div",XI,[m("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[m("i",{class:"fas fa-bold"},null,-1)]),8,YI),m("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[m("i",{class:"fas fa-italic"},null,-1)]),8,JI),m("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[m("i",{class:"fas fa-underline"},null,-1)]),8,QI),m("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[m("i",{class:"fas fa-strikethrough"},null,-1)]),8,ZI)]),t[16]||(t[16]=m("div",{class:"toolbar-divider"},null,-1)),m("div",eD,[m("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[m("i",{class:"fas fa-list-ul"},null,-1)]),8,tD),m("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[m("i",{class:"fas fa-list-ol"},null,-1)]),8,sD)])]),n.showHtmlSource?Jt((S(),N("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,nD)),[[yr,n.htmlContent]]):(S(),N("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,rD))],2)])}const Lu=Le(GI,[["render",oD],["__scopeId","data-v-6578517d"]]),z3="",iD={name:"OfferForm",mixins:[rr],components:{Toast:sr,HelpIcon:fa,TextEditor:Lu,CustomInput:On,CustomLabel:wr,CustomSelect:nr,Autocomplete:Or,CustomCheckbox:Yn,ConfirmationModal:Jn},props:{offer:{type:Object,required:!0},isEditing:{type:Boolean,required:!0},isReadonly:{type:Boolean,default:!1}},emits:["update:offer","validate"],data(){return{localOffer:{...this.offer},showOfferStatusModal:!1,typeOptions:[],typeEnabled:!1,audienceOptions:[],formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}}}},async created(){this.typeEnabled&&await this.getTypes(),await this.getAudiences()},watch:{offer:{handler(e){var t;if(JSON.stringify(e)!==JSON.stringify(this.localOffer)){const s=(t=e==null?void 0:e.audiences)==null?void 0:t.map(i=>({value:i,label:""}));this.localOffer={...e,audiences:s}}},deep:!0,immediate:!0},localOffer:{handler(e){var s;const t=(s=e==null?void 0:e.audiences)==null?void 0:s.map(i=>i.value);e={...e,audiences:t},JSON.stringify(e)!==JSON.stringify(this.offer)&&this.$emit("update:offer",{...e,audiences:t})},deep:!0}},methods:{async getTypes(){try{const e=await getTypeOptions(),{enabled:t,types:s,default:i}=e;this.typeEnabled=!!t,t&&Array.isArray(s)&&(this.typeOptions=s.map(n=>({value:n,label:n.charAt(0).toUpperCase()+n.slice(1)})))}catch(e){this.showErrorMessage(e.message||"Erro ao carregar opções de tipos.")}},async getAudiences(){this.loading=!0;try{const e=await aC("");this.audienceOptions=e.map(t=>({value:t.id,label:t.name.toUpperCase()}))}catch(e){console.log(e),this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},handleStatusChange(e){if(console.log(e),!e){this.showOfferStatusModal=!0;return}this.localOffer.status=!0,this.validateForm()},confirmInactivateStatus(){this.showOfferStatusModal=!1,this.localOffer.status=!1,this.validateForm()},validateForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){var t;switch(e){case"name":this.formErrors.name.hasError=!this.localOffer.name;break;case"audiences":this.formErrors.audiences.hasError=!((t=this.localOffer)!=null&&t.audiences)||this.localOffer.audiences.length===0;break}return!this.formErrors[e].hasError}}},aD={class:"row"},lD={class:"col-md-3"},uD={class:"form-group"},cD={key:0,class:"col-md-4"},dD={class:"form-group"},fD={key:1,class:"col-md-3"},hD={class:"form-group"},pD={class:"row"},mD={class:"col-md-12"},gD={class:"form-group"},_D={class:"row"},vD={class:"col-md-12"};function yD(e,t,s,i,n,a){const u=L("CustomLabel"),c=L("CustomInput"),f=L("CustomSelect"),g=L("CustomCheckbox"),h=L("Autocomplete"),p=L("TextEditor"),v=L("ConfirmationModal"),C=L("Toast");return S(),N("div",null,[m("div",aD,[m("div",lD,[m("div",uD,[x(u,{required:"",text:"Nome da oferta"}),x(c,{modelValue:n.localOffer.name,"onUpdate:modelValue":t[0]||(t[0]=P=>n.localOffer.name=P),placeholder:"Oferta 0001",required:"",disabled:s.isReadonly,"has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=P=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])])]),n.typeEnabled?(S(),N("div",cD,[m("div",dD,[x(u,{text:"Tipo da oferta"}),x(f,{modelValue:n.localOffer.type,"onUpdate:modelValue":t[2]||(t[2]=P=>n.localOffer.type=P),options:n.typeOptions,disabled:s.isReadonly},null,8,["modelValue","options","disabled"])])])):te("",!0),s.isEditing?(S(),N("div",fD,[m("div",hD,[x(u,{text:" ",className:"d-none d-md-block"}),x(g,{modelValue:n.localOffer.status,"onUpdate:modelValue":t[3]||(t[3]=P=>n.localOffer.status=P),id:"status",label:"Ativar oferta",confirmBeforeChange:!0,disabled:s.isReadonly,onRequestChange:a.handleStatusChange},null,8,["modelValue","disabled","onRequestChange"])])])):te("",!0)]),m("div",pD,[m("div",mD,[m("div",gD,[x(u,{required:"",text:"Publico-alvo",help:`Para atribuir o público-alvo é necessário que o mesmo seja previamente criado no 'Gerenciador de Público-Alvo'.<br><br>
                  Após a exclusão do ‘atributo’ de um público-alvo, os usuários que já estiverem inscritos em um curso permanecerão matriculados, 
                  mantendo acesso ao conteúdo normalmente. <br><br>
                  A exclusão impactará apenas a exibição do curso para novos usuários dentro desse público-alvo.`}),x(h,{class:"autocomplete-audiences",modelValue:n.localOffer.audiences,"onUpdate:modelValue":[t[4]||(t[4]=P=>n.localOffer.audiences=P),t[5]||(t[5]=P=>a.validateForm())],items:n.audienceOptions,placeholder:"Pesquisar público-alvo...",required:!0,"show-all-option":!0,disabled:s.isReadonly,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message},null,8,["modelValue","items","disabled","has-error","error-message"])])])]),m("div",_D,[m("div",vD,[x(u,{text:"Descrição da oferta"}),x(p,{modelValue:n.localOffer.description,"onUpdate:modelValue":[t[6]||(t[6]=P=>n.localOffer.description=P),t[7]||(t[7]=P=>a.validateForm())],placeholder:"Digite a descrição da oferta aqui...",rows:5,disabled:s.isReadonly},null,8,["modelValue","disabled"])])]),x(v,{show:n.showOfferStatusModal,size:"md",title:"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma: ","list-title":"Comportamento para os cursos, turmas e matrículas:","list-items":["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."],"confirm-button-text":"Inativar oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[8]||(t[8]=P=>n.showOfferStatusModal=!1),onConfirm:a.confirmInactivateStatus},null,8,["show","onConfirm"]),x(C,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const Up=Le(iD,[["render",yD],["__scopeId","data-v-499e7423"]]),bD={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none"};function ED(e,t){return S(),N("svg",bD,t[0]||(t[0]=[m("path",{fill:"#fff",d:"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6"},null,-1),m("path",{fill:"#fff","fill-rule":"evenodd",d:"M5.216 14A2.24 2.24 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.3 6.3 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1z","clip-rule":"evenodd"},null,-1),m("path",{fill:"#fff",d:"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5"},null,-1)]))}const CD={render:ED},K3="",wD={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},OD={class:"table-responsive"},xD={class:"table"},SD={key:0,class:"expand-column"},ID=["onClick","data-value"],DD={key:0,class:"sort-icon"},ND={key:0},TD={key:0,class:"expand-column"},AD=["onClick","title"],RD=["colspan"],PD={class:"expanded-content"},MD={key:1},kD=["colspan"];function VD(e,t,s,i,n,a){return S(),N("div",OD,[m("table",xD,[m("thead",null,[m("tr",null,[s.expandable?(S(),N("th",SD)):te("",!0),(S(!0),N(je,null,At(s.headers,u=>(S(),N("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:me({sortable:u.sortable}),"data-value":u.value},[Ze(ne(u.text)+" ",1),u.sortable?(S(),N("span",DD,[m("i",{class:me(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):te("",!0)],10,ID))),128))])]),s.items.length>0?(S(),N("tbody",ND,[(S(!0),N(je,null,At(s.items,(u,c)=>(S(),N(je,{key:u.id},[m("tr",{class:me({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(S(),N("td",TD,[m("button",{class:"btn-expand",onClick:f=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[m("div",{class:me(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[m("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[m("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),m("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,AD)])):te("",!0),(S(!0),N(je,null,At(s.headers,f=>(S(),N("td",{key:`${u.id}-${f.value}`},[Rt(e.$slots,"item-"+f.value,{item:u},()=>[Ze(ne(u[f.value]),1)],!0)]))),128))],2),s.expandable?(S(),N("tr",{key:0,class:me(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[m("td",{colspan:s.headers.length+1},[m("div",PD,[Rt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,RD)],2)):te("",!0)],64))),128))])):(S(),N("tbody",MD,[m("tr",null,[m("td",{colspan:s.headers.length+(s.expandable?1:0)},[Rt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=m("div",{class:"empty-state"},[m("span",null,"Não existem registros")],-1))],!0)],8,kD)])]))])])}const LD=Le(wD,[["render",VD],["__scopeId","data-v-05038124"]]),X3="",$D={name:"AddOfferCourseModal",components:{CustomInput:On,CustomButton:hs,CustomTable:wn,Pagination:Cn,Autocomplete:Or,FilterTag:ei},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loading:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentOfferCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],currentOfferCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e,t){e?(this.getCurrentOfferCourses(),this.getCategories()):(this.selectedCategory=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){if(this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,!e){this.getCurrentOfferCourses();return}this.getCoursesForCategory(e.value)},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{async getCurrentOfferCourses(){try{this.loadingCurrentOfferCourses=!0;const e=await Pp(this.offerId);this.currentOfferCourses=e.courses.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id}))}catch{}finally{this.loadingCurrentOfferCourses=!1}},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Mu("");this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch{}finally{this.loadingCategories=!1}},async addOfferCourses(){try{if(this.loading=!0,this.selectedCoursesPreview.length===0){this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await dC(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch{}finally{this.loading=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(e)try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const n=await uC(this.offerId,e,i,t,this.coursesPerPage);let a=null,u=[];if(u=n.courses,a={page:n.page||1,total_pages:n.total_pages||1},a){if(this.coursesPage=a.page||1,this.coursesTotalPages=a.total_pages||1,this.hasMoreCourses=(a.page||1)<(a.total_pages||1),u&&u.length>0){const f=u.filter(g=>!this.currentOfferCourses.some(h=>h.id===g.id)&&!this.selectedCoursesPreview.some(h=>h.id===g.id)).map(g=>({value:g.id,label:g.fullname}));s?this.courseOptions=[...this.courseOptions,...f]:this.courseOptions=f}}else console.warn("Formato de resposta inesperado")}catch(n){console.error("Erro ao carregar cursos da categoria:",n),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?this.getCoursesForCategory(this.selectedCategory.value,this.currentPage,!1):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCourse=null,this.selectedCoursesPreview=[]}}},FD={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},UD={class:"modal-content"},BD={class:"modal-header"},qD={class:"modal-body"},HD={class:"row mb-4"},WD={class:"col-md-6"},jD={class:"col-md-6"},GD={class:"table-container"},zD={key:0,class:"empty-preview-message"},KD={class:"action-buttons"},XD=["onClick"],YD={class:"modal-footer"};function JD(e,t,s,i,n,a){const u=L("Autocomplete"),c=L("CustomTable"),f=L("Pagination"),g=L("CustomButton");return s.modelValue?(S(),N("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...h)=>a.closeModal&&a.closeModal(...h))},[m("div",FD,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[5]||(t[5]=es(()=>{},["stop"]))},[m("div",UD,[m("div",BD,[t[8]||(t[8]=m("h3",{class:"modal-title"},"Adicionar curso",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=(...h)=>a.closeModal&&a.closeModal(...h))},t[7]||(t[7]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",qD,[t[11]||(t[11]=m("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),m("div",HD,[m("div",WD,[x(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedCategory=h),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...",loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])]),m("div",jD,[x(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedCourse=h),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...",disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),m("div",GD,[n.selectedCoursesPreview.length===0?(S(),N("div",zD,t[9]||(t[9]=[m("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(S(),ct(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Re(({item:h})=>[m("div",KD,[m("button",{class:"btn-action btn-delete",onClick:p=>a.removeCourse(h),title:"Remover da lista"},t[10]||(t[10]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,XD)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(S(),ct(f,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=h=>n.currentPage=h),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=h=>n.perPage=h),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):te("",!0)]),m("div",YD,[x(g,{variant:"primary",label:"Confirmar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.addOfferCourses},null,8,["is-loading","disabled","onClick"]),x(g,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])],2)])])):te("",!0)}const QD=Le($D,[["render",JD],["__scopeId","data-v-b7b14bd4"]]),Y3="",ZD={name:"DuplicateOfferClassModal",mixins:[rr],components:{Toast:sr,Alert:pa,Pagination:Cn,CustomTable:wn,Autocomplete:Or,CustomButton:hs},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},offerClass:{type:Object,default:null},parentOfferCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success"],data(){return{selectedCategory:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loading:!1,loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{alertInfo(){return this.offerClass.enrol==="offer_dealership"?`O método de inscrição para essa turma é <b>${this.offerClass.enrolName}</b>.<br>
                Ao prosseguir, você está ciente de que novas inscrições só poderão ser realizadas pelo Gestor ou manualmente pelo Administrador.`:`O método de inscrição para essa turma é <b>${this.offerClass.enrolName}</b>.`},courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return this.getSortedCourses().slice(e,t)}},watch:{offerClass(){this.resetForm(),this.getCategories()},parentOfferCourse(){this.resetForm(),this.getCategories()},selectedCategory(e){this.resetCourseSelection(),e!=null&&e.value&&this.getCoursesForCategory(e.value)}},async created(){this.initializeComponent()},methods:{async initializeComponent(){this.resetForm(),await this.getCategories()},resetForm(){this.resetCategorySelection(),this.resetCourseSelection(),this.resetPagination(),this.resetDuplicationState(),this.existingCourses=[]},resetCategorySelection(){this.selectedCategory=null,this.categoryOptions=[],this.loadingCategories=!1},resetCourseSelection(){this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.resetCoursePagination()},resetCoursePagination(){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},resetPagination(){this.currentPage=1},resetDuplicationState(){this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0},getSortedCourses(){return[...this.selectedCoursesPreview].sort((e,t)=>{const s=e[this.sortBy],i=t[this.sortBy];return s<i?this.sortDesc?1:-1:s>i?this.sortDesc?-1:1:0})},async getCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Mu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.handleError("Erro ao carregar categorias:",e)}finally{this.loadingCategories=!1}},async getCoursesForCategory(e,t=1,s=!1,i=""){if(!(!e||!this.offerClass))try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const u=(await bC(this.offerClass.id)).filter(c=>{const f=!i||c.name&&c.name.toLowerCase().includes(i.toLowerCase())||c.fullname&&c.fullname.toLowerCase().includes(i.toLowerCase()),g=!this.selectedCoursesPreview.some(v=>parseInt(v.value)===parseInt(c.id)),h=parseInt(c.categoryid)===parseInt(e),p=parseInt(c.id)!==parseInt(this.parentOfferCourse.courseId);return h&&p&&f&&g}).map(c=>({value:c.id,label:c.name||c.fullname||c.coursename||`Curso ${c.id}`,categoryid:c.categoryid,category_name:c.category_name})).filter(c=>c!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...u]:this.targetCourseOptions=u,this.hasMoreCourses=u.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(n){this.handleError("Erro ao carregar cursos da categoria:",n),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.getCoursesForCategory(this.selectedCategory.value,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.resetCoursePagination(),await this.getCoursesForCategory(this.selectedCategory.value,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value),this.selectedCourse=null)},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handleError(e,t){this.$emit("error","Erro ao carregar dados. Por favor, tente novamente.")},async handleConfirm(){if(!(!this.offerClass||this.selectedCoursesPreview.length===0))try{this.loading=!0,this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const e=this.offerClass.name,t=parseInt(this.offerClass.id),s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value);try{const a=await yC(t,n);s.push({offerClassName:e,targetCourseName:i.label,offerClassId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){this.showErrorMessage(a)}}if(s.length===0)throw new Error("Nenhuma turma foi duplicada com sucesso.");this.$emit("success",{offerClassName:e,totalSelected:this.totalToDuplicate,totalDuplicates:s.length,duplicates:s}),this.resetForm(),this.$emit("close")}catch(e){this.showErrorMessage(e)}finally{this.duplicatingCourses=!1,this.loading=!1}}}},eN={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},tN={class:"modal-content"},sN={class:"modal-header"},rN={class:"modal-title"},nN={class:"modal-body"},oN={class:"row"},iN={class:"col-md-6"},aN={class:"form-group"},lN={class:"col-md-6"},uN={class:"form-group"},cN={key:0,class:"empty-preview-message"},dN={class:"action-buttons"},fN=["onClick"],hN={class:"mt-5"},pN={class:"modal-footer"};function mN(e,t,s,i,n,a){var v;const u=L("Autocomplete"),c=L("CustomTable"),f=L("Pagination"),g=L("Alert"),h=L("CustomButton"),p=L("Toast");return S(),N("div",{class:"modal-overlay",onClick:t[7]||(t[7]=C=>e.$emit("close"))},[m("div",eN,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[6]||(t[6]=es(()=>{},["stop"]))},[m("div",tN,[m("div",sN,[m("h3",rN,'Duplicar Turma "'+ne((v=s.offerClass)==null?void 0:v.name)+'"',1),m("button",{class:"modal-close",onClick:t[0]||(t[0]=C=>e.$emit("close"))},t[8]||(t[8]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",nN,[t[11]||(t[11]=m("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),m("div",oN,[m("div",iN,[m("div",aN,[x(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=C=>n.selectedCategory=C),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...",loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","loading","no-results-text"])])]),m("div",lN,[m("div",uN,[x(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=C=>n.selectedCourse=C),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...",disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])])]),n.selectedCoursesPreview.length===0?(S(),N("div",cN,t[9]||(t[9]=[m("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(S(),ct(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Re(({item:C})=>[m("div",dN,[m("button",{class:"btn-action btn-delete",onClick:P=>a.removeCourse(C),title:"Remover da lista"},t[10]||(t[10]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,fN)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])),n.selectedCoursesPreview.length>0?(S(),ct(f,{key:2,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=C=>n.currentPage=C),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=C=>n.perPage=C),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):te("",!0),m("div",hN,[x(g,{type:"primary",text:a.alertInfo},null,8,["text"])])]),m("div",pN,[x(h,{variant:"primary",label:"Duplicar","is-loading":n.loading,disabled:n.selectedCoursesPreview.length===0,onClick:a.handleConfirm},null,8,["is-loading","disabled","onClick"]),x(h,{variant:"secondary",label:"Cancelar",onClick:t[5]||(t[5]=C=>e.$emit("close"))})])])],2)]),x(p,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const gN=Le(ZD,[["render",mN],["__scopeId","data-v-8fd731f4"]]),J3="",_N={name:"AddOfferClassModal",components:{Alert:pa,CustomLabel:wr,CustomButton:hs,CustomSelect:nr},props:{size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},offerCourseId:{type:[Number,String],required:!0},offerId:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},created(){this.getEnrolmentMethods()},methods:{async getEnrolmentMethods(){this.loading=!0;const e=await EC(!0);this.enrolmentMethods=e.map(t=>({value:t.enrol,label:t.name})),this.enrolmentMethods.sort((t,s)=>t.label.localeCompare(s.label)),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offerCourseId:this.offerCourseId,offerId:this.offerId})}}},vN={class:"modal fade d-block show",tabindex:"-1","aria-modal":"true",role:"dialog"},yN={class:"modal-content"},bN={class:"modal-header"},EN={class:"modal-body"},CN={class:"row mb-3"},wN={class:"col-md-12"},ON={class:"form-group"},xN={class:"modal-footer"};function SN(e,t,s,i,n,a){const u=L("Alert"),c=L("CustomLabel"),f=L("CustomSelect"),g=L("CustomButton");return S(),N("div",{class:"modal-overlay",onClick:t[4]||(t[4]=h=>e.$emit("close"))},[m("div",vN,[m("div",{class:me(["modal-dialog modal-dialog-centered modal-dialog-scrollable",[`modal-${s.size}`]]),onClick:t[3]||(t[3]=es(()=>{},["stop"]))},[m("div",yN,[m("div",bN,[t[6]||(t[6]=m("h3",{class:"modal-title"},"Adicionar turma",-1)),m("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[5]||(t[5]=[m("i",{class:"fas fa-times"},null,-1)]))]),m("div",EN,[x(u,{type:"primary",icon:"fas fa-exclamation-triangle",text:"Esta configuração não poderá ser alterada posteriormente."}),m("div",CN,[m("div",wN,[m("div",ON,[x(c,{required:"",text:"Tipo de Inscrição",help:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.<br><br>\r
                    Auto inscrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no botão de auto inscrever (recomentado para cursos livres). <br><br>\r
                    Inscrição pelo Gestor Concessionária em ofertas: Nesta opção, o Gestor Concessionária realizará a inscrição manual dos usuários nas turmas correspondentes aos cursos disponíveis na oferta.<br><br>`}),x(f,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método de inscrição..."},...n.enrolmentMethods],"is-loading":n.loading,required:""},null,8,["modelValue","options","is-loading"])])])])]),m("div",xN,[x(g,{variant:"primary",label:"Continuar",disabled:!n.selectedEnrolType,onClick:a.handleConfirm},null,8,["disabled","onClick"]),x(g,{variant:"secondary",label:"Cancelar",onClick:t[2]||(t[2]=h=>e.$emit("close"))})])])],2)])])}const IN=Le(_N,[["render",SN],["__scopeId","data-v-554bcc1c"]]),Q3="",Z3="",DN={name:"TableList",mixins:[rr],components:{ViewIcon:Fp,UsersIcon:CD,CircleCheckIcon:Lp,CircleXMarkIcon:$p,CustomLabel:wr,OfferForm:Up,CustomTable:wn,CustomSelect:nr,CustomInput:On,CustomButton:hs,Pagination:Cn,CollapsibleTable:LD,PageHeader:Xn,BackButton:ti,Autocomplete:Or,TextEditor:Lu,CustomCheckbox:Yn,FilterRow:ca,FilterGroup:da,FilterTag:ei,FilterTags:ha,AddOfferCourseModal:QD,ConfirmationModal:Jn,Toast:sr,DuplicateOfferClassModal:gN,AddOfferClassModal:IN,LFLoading:Zo},props:{offerId:{type:Number,required:!0},isReadonly:{type:Boolean,default:!1}},data(){return{showAddCourseModalVisible:!1,showCourseStatusModal:!1,showDeleteOfferCourseModal:!1,offerCourseToDelete:null,showDeleteOfferClassModal:!1,offerClassToDelete:null,showOfferClassStatusModal:!1,showDuplicateOfferClassModal:!1,showEnrolTypeModal:!1,selectedOfferClass:null,offerClassToDuplicate:null,parentOfferCourse:null,selectedOfferCourseForClass:null,categoryOptions:[],courseOptions:[],selectedOfferCourse:null,loading:!1,inputFilters:{course:null,category:null,onlyActive:!1},offerCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,filterCoursesPage:1,filterCoursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,filterCourseNoResultsText:"Nenhum curso encontrado",offerCourseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"courseClassCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offerClassTableHeaders:[{text:"NOME DA TURMA",value:"name",sortable:!1},{text:"TIPO DE INSCRIÇÃO",value:"enrolName",sortable:!1},{text:"VAGAS",value:"vacancies",sortable:!1},{text:"INSCRITOS",value:"totalEnrolled",sortable:!1},{text:"DATA INÍCIO",value:"startDate",sortable:!1},{text:"DATA FIM",value:"endDate",sortable:!1},{text:"STATUS",value:"status",sortable:!1},{text:"AÇÕES",value:"actions",sortable:!1}]}},watch:{async"inputFilters.course"(e,t){this.currentPage=1,e!==null&&(this.inputFilters.category=null),await this.getCourses()},async"inputFilters.category"(e,t){e===""&&(this.inputFilters.category=null),e!==null&&(this.inputFilters.course=null),this.currentPage=1,await this.getCourses(),await this.getCourseOptions()},async"inputFilters.onlyActive"(e,t){this.currentPage=1,this.inputFilters.course=null,await this.getCourses(),await this.getCourseOptions()},currentPage(){this.getCourses()},perPage(){this.currentPage=1,this.getCourses()}},async created(){await this.getCourses(),await this.getCategoryOptions(),await this.getCourseOptions()},methods:{async getCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.inputFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.inputFilters.course&&(e.courseIds=[this.inputFilters.course.value]),this.inputFilters.category&&(e.categorySearch=this.inputFilters.category.label);const t=await Pp(this.offerId,e),{page:s,total_pages:i,total_items:n,courses:a}=t;this.currentPage=s;const u=[];for(const c of a)try{const g=(await pC(c.id)).map(h=>({...h,offerCourseId:c.id,enrol:h.enrol||"-",enrolName:h.enrol_name||"-",vacancies:h.max_users?h.max_users:"Ilimitado",totalEnrolled:h.enrolled_users||0,startDate:this.formatDate(h.startdate),endDate:this.formatDate(h.enddate),status:!!parseInt(h.status),statusName:parseInt(h.status)?"Ativa":"Inativo",canActivate:h.can_activate,canDelete:h.can_delete}));u.push({id:c.id,courseId:c.courseid,name:c.fullname,category:c.category_name||"-",courseClassCount:g.length,status:!!parseInt(c.status),statusName:parseInt(c.status)?"Ativo":"Inativo",canDelete:c.can_delete,canActivate:c.can_activate,offerClasses:g})}catch(f){console.log(f)}this.offerCourses=u,this.totalItems=n}catch(e){this.showErrorMessage(e),this.offerCourses=[],this.totalItems=0}finally{this.loading=!1}},async getCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Mu("",this.offerId);this.categoryOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getCourseOptions(e="",t=!0){var s,i;if(this.offerId){this.loading=!0;try{t?(this.filterCoursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const a=(await cC(this.offerId,(s=this.inputFilters.category)==null?void 0:s.value,e,(i=this.inputFilters.course)!=null&&i.value?[this.inputFilters.course.value]:[],this.inputFilters.onlyActive)).map(u=>({value:u.id||u.courseid,label:u.fullname}));t?this.courseOptions=a:this.courseOptions=[...this.courseOptions,...a],this.hasMoreCourses=!1,this.courseOptions.length===0&&(this.filterCourseNoResultsText="Nenhum curso disponível nesta categoria")}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},getOperationalCycleClassName(e){switch(e){case 0:return"badge-secondary";case 1:return"badge-primary";case 2:return"badge-success";default:return""}},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},clearFilters(){this.inputFilters={course:null,category:null,onlyActive:!1},this.getCourses(),this.getCourseOptions()},async removeFilter(e){this.inputFilters[e]=null},async loadMoreCourses(){var e;this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.filterCoursesPage+=1,(e=this.inputFilters.category)!=null&&e.value&&await this.getCourseOptions("",!1))},handleOnlyActiveChange(){this.currentPage=1,this.getCourses()},async handleAddCourseConfirm(e){try{this.loading=!0,await this.getCourses(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.getCourses()},handlePageChange(e){this.currentPage=e,this.getCourses()},addOfferClass(e){this.selectedOfferCourseForClass=e,this.showEnrolTypeModal=!0},handleAddClassConfirm(e){this.showEnrolTypeModal=!1,this.$router.push({name:"offer.class.create",query:{enrolMethod:e.enrolType,offerCourseId:e.offerCourseId}})},navigateToShowOfferClass(e){this.$router.push({name:"offer.class.show",params:{offerClassId:e.id}})},navigateToEditOfferClass(e){this.$router.push({name:"offer.class.edit",params:{offerClassId:e.id}})},requestToggleOfferClassStatus(e){this.selectedOfferClass={...e},this.showOfferClassStatusModal=!0},async toggleOfferClassStatus(){if(this.selectedOfferClass){this.loading=!0,this.showOfferClassStatusModal=!1;try{const{id:e,name:t,status:s}=this.selectedOfferClass,i=!s;await CC(e,i);const n=this.offerCourses.find(a=>a.offerClasses.some(u=>u.id===e));if(n){const a=n.offerClasses.find(u=>u.id===e);a&&(a.status=i,a.statusName=i?"Ativo":"Inativo")}this.showSuccessMessage(i?`Turma "${t}" ativada com sucesso.`:`Turma "${t}" inativada com sucesso.`),this.selectedOfferClass=null}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}}},removeOfferClass(e){e.canDelete&&(this.offerClassToDelete=e,this.showDeleteOfferClassModal=!0)},navigateToEnrollments(e){this.$router.push({name:"offer.class.enrollments",params:{offerClassId:parseInt(e.id)}})},async handleDuplicateSuccess(e){await this.getCourses(),e.totalDuplicates&&this.showSuccessMessage(`Turma "${e.offerClassName}" duplicada com sucesso para ${e.totalDuplicates} curso(s).`)},duplicateOfferClass(e){this.offerClassToDuplicate=e,this.parentOfferCourse=this.offerCourses.find(t=>t.id=e.offerCourseId),this.showDuplicateOfferClassModal=!0},async deleteOfferClass(){if(this.offerClassToDelete){this.loading=!0,this.showDeleteOfferClassModal=!1;try{const{id:e,name:t,offerCourseId:s}=this.offerClassToDelete;await gC(e);const i=this.offerCourses.find(n=>n.id===s);if(i&&Array.isArray(i.offerClasses)){const n=i.offerClasses.findIndex(a=>a.id===e);n!==-1&&(i.offerClasses.splice(n,1),i.courseClassCount=i.offerClasses.length)}this.showSuccessMessage(`Turma ${t} excluída com sucesso.`),this.offerClassToDelete=null}catch(e){console.log(e),this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}}},requestToggleOfferCourseStatus(e){e.canActivate&&(this.selectedOfferCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status?e.canActivate?"Inativar":"Não é possível inativar este curso":e.canActivate?"Ativar":"Não é possível ativar este curso"},async toggleOfferCourseStatus(){if(this.selectedOfferCourse)try{this.loading=!0;const e=!this.selectedOfferCourse.status,t=this.selectedOfferCourse.name,s=this.selectedOfferCourse.id;await iC(this.offerId,s,e);const i=this.offerCourses.findIndex(n=>n.id===this.selectedOfferCourse.id);if(i!==-1){const n=this.offerCourses[i];n.status=e,n.statusName=e?"Ativo":"Inativo"}this.showCourseStatusModal=!1,this.selectedOfferCourse=null,await this.getCourses(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch{}finally{this.loading=!1}},requestDeleteOfferCourse(e){e.canDelete&&(this.offerCourseToDelete=e,this.showDeleteOfferCourseModal=!0)},async deleteOfferCourse(){if(this.offerCourseToDelete)try{this.loading=!0,this.showDeleteOfferCourseModal=!1;const e=this.offerCourseToDelete.name,t=this.offerCourseToDelete.id;await oC(this.offerId,t),this.offerCourses=this.offerCourses.filter(i=>i.id!==this.offerCourseToDelete.id),this.offerCourseToDelete=null,await this.getCourses();const s=`Curso "${e}" excluído com sucesso.`;this.showSuccessMessage(s)}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}}}},NN={class:"row mb-5"},TN={class:"col-md-8"},AN={class:"row"},RN={class:"col-md-4"},PN={class:"col-md-4 mt-3 mt-md-0"},MN={class:"col-md-4 mt-3 mt-md-0"},kN={key:0,class:"col-md-4"},VN={class:"d-flex justify-content-end"},LN={class:"empty-state"},$N={class:"no-results"},FN=["title"],UN={key:0},BN={key:1},qN={class:"action-buttons"},HN=["disabled","onClick"],WN=["onClick","disabled","title"],jN=["onClick","disabled","title"],GN={class:"action-buttons"},zN=["onClick"],KN=["onClick"],XN=["disabled","onClick"],YN=["disabled","onClick"],JN=["title","disabled","onClick"],QN=["onClick","disabled","title"];function ZN(e,t,s,i,n,a){var de,De,$e,ue,R,G,Y,pe,j,J,ge;const u=L("Autocomplete"),c=L("CustomLabel"),f=L("CustomCheckbox"),g=L("FilterTag"),h=L("FilterTags"),p=L("CustomButton"),v=L("CircleCheckIcon"),C=L("CircleXMarkIcon"),P=L("ViewIcon"),A=L("UsersIcon"),oe=L("CustomTable"),W=L("CollapsibleTable"),ie=L("Pagination"),q=L("AddOfferCourseModal"),ye=L("ConfirmationModal"),se=L("DuplicateOfferClassModal"),be=L("AddOfferClassModal"),xe=L("LFLoading"),ke=L("Toast");return S(),N("div",null,[m("div",NN,[m("div",TN,[m("div",AN,[m("div",RN,[x(u,{modelValue:n.inputFilters.category,"onUpdate:modelValue":t[0]||(t[0]=ee=>n.inputFilters.category=ee),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada"},null,8,["modelValue","items","no-results-text"])]),m("div",PN,[x(u,{modelValue:n.inputFilters.course,"onUpdate:modelValue":t[1]||(t[1]=ee=>n.inputFilters.course=ee),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.filterCourseNoResultsText,onLoadMore:a.loadMoreCourses,onSearch:t[2]||(t[2]=ee=>a.getCourseOptions(ee)),ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onLoadMore"])]),m("div",MN,[x(c,{text:" ",className:"d-none d-md-block"}),x(f,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[3]||(t[3]=ee=>n.inputFilters.onlyActive=ee),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),n.inputFilters.course?(S(),ct(h,{key:0,class:"col-md-8 mt-3"},{default:Re(()=>[x(g,{onRemove:t[4]||(t[4]=ee=>a.removeFilter("course"))},{default:Re(()=>[Ze(" Curso: "+ne(n.inputFilters.course.label),1)]),_:1})]),_:1})):te("",!0)])]),s.isReadonly?te("",!0):(S(),N("div",kN,[x(c,{text:" "}),m("div",VN,[x(p,{variant:"primary",icon:"fa-solid fa-plus",label:"Adicionar Curso",onClick:t[5]||(t[5]=ee=>n.showAddCourseModalVisible=!0)})])]))]),x(W,{headers:n.offerCourseTableHeaders,items:n.offerCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Re(()=>[m("div",LN,[m("span",$N,ne(n.loading?"Carregando registros...":"Nenhum registro encontrado"),1)])]),"item-name":Re(({item:ee})=>[m("span",{title:ee.name},ne(ee.name.length>50?ee.name.slice(0,50)+"...":ee.name),9,FN)]),"item-status":Re(({item:ee})=>[ee.status?(S(),N("span",UN,[x(v),t[15]||(t[15]=Ze(" Ativo "))])):(S(),N("span",BN,[x(C),t[16]||(t[16]=Ze(" Inativo "))]))]),"item-actions":Re(({item:ee})=>[m("div",qN,[m("button",{class:"btn-action btn-add",disabled:s.isReadonly,onClick:_e=>a.addOfferClass(ee),title:"Adicionar turma"},t[17]||(t[17]=[m("i",{class:"fa-solid fa-plus"},null,-1)]),8,HN),m("button",{class:me(["btn-action",ee.status?"btn-deactivate":"btn-activate"]),onClick:_e=>a.requestToggleOfferCourseStatus(ee),disabled:!ee.status&&!ee.canActivate||!ee.canActivate||s.isReadonly,title:a.getStatusButtonTitle(ee)},[m("i",{class:me(ee.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,WN),m("button",{class:"btn-action btn-delete",onClick:_e=>a.requestDeleteOfferCourse(ee),disabled:!ee.canDelete||s.isReadonly,title:ee.canDelete?"Excluir":"Não é possível excluir este curso"},t[18]||(t[18]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,jN)])]),"expanded-content":Re(({item:ee})=>[x(oe,{headers:n.offerClassTableHeaders,items:ee.offerClasses,tableClass:"table-hover mb-0",theadClass:"thead-dark",tbodyClass:"tbody-light"},{"item-name":Re(({item:_e})=>[Ze(ne(_e.name.length>20?_e.name.slice(0,20)+"...":_e.name),1)]),"item-status":Re(({item:_e})=>[m("span",{class:me(["operational-cycle badge",a.getOperationalCycleClassName(_e.operational_cycle)])},ne(_e.operational_cycle_name),3)]),"item-actions":Re(({item:_e})=>[m("div",GN,[m("button",{class:"btn-action btn-edit",onClick:Se=>a.navigateToShowOfferClass(_e),title:"Visualizar"},[x(P)],8,zN),m("button",{class:"btn-action btn-users",onClick:Se=>a.navigateToEnrollments(_e),title:"Usuários Matriculados"},[x(A)],8,KN),m("button",{class:"btn-action btn-edit",disabled:s.isReadonly,onClick:Se=>a.navigateToEditOfferClass(_e),title:"Editar"},t[19]||(t[19]=[m("i",{class:"fas fa-pencil-alt"},null,-1)]),8,XN),m("button",{class:"btn-action btn-duplicate",disabled:s.isReadonly,onClick:Se=>a.duplicateOfferClass(_e),title:"Duplicar Turma"},t[20]||(t[20]=[m("i",{class:"fas fa-copy"},null,-1)]),8,YN),m("button",{class:me(["btn-action",_e.status?"btn-deactivate":"btn-activate"]),title:_e.status?"Inativar":"Ativar",disabled:s.isReadonly,onClick:Se=>a.requestToggleOfferClassStatus(_e)},[m("i",{class:me(_e.status?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,JN),m("button",{class:"btn-action btn-delete",onClick:Se=>a.removeOfferClass(_e),disabled:!_e.canDelete||s.isReadonly,title:_e.canDelete?"Excluir":"Não é possível excluir esta turma"},t[21]||(t[21]=[m("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,QN)])]),_:2},1032,["headers","items"])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),x(ie,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[6]||(t[6]=ee=>n.currentPage=ee),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[7]||(t[7]=ee=>n.perPage=ee),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"]),s.offerId?(S(),ct(q,{key:0,modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[8]||(t[8]=ee=>n.showAddCourseModalVisible=ee),offerId:s.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offerId","onConfirm"])):te("",!0),x(ye,{size:"md",show:n.showCourseStatusModal,title:(de=n.selectedOfferCourse)!=null&&de.showOfferClassStatusModal?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:(De=n.selectedOfferCourse)!=null&&De.status?"":"Tem certeza que deseja ativar este curso?","list-items":($e=n.selectedOfferCourse)!=null&&$e.status?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":(ue=n.selectedOfferCourse)!=null&&ue.status?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:(R=n.selectedOfferCourse)!=null&&R.status?"warning":"question",onClose:t[9]||(t[9]=ee=>n.showCourseStatusModal=!1),onConfirm:a.toggleOfferCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),x(ye,{size:"md",show:n.showDeleteOfferCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[10]||(t[10]=ee=>n.showDeleteOfferCourseModal=!1),onConfirm:a.deleteOfferCourse},null,8,["show","onConfirm"]),x(ye,{size:"md",show:n.showDeleteOfferClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[11]||(t[11]=ee=>n.showDeleteOfferClassModal=!1),onConfirm:a.deleteOfferClass},null,8,["show","onConfirm"]),x(ye,{show:n.showOfferClassStatusModal,size:"md",title:(G=n.selectedOfferClass)!=null&&G.status?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:(Y=n.selectedOfferClass)!=null&&Y.status?"":"Tem certeza que deseja ativar esta turma?","list-items":(pe=n.selectedOfferClass)!=null&&pe.status?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":(j=n.selectedOfferClass)!=null&&j.status?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:(J=n.selectedOfferClass)!=null&&J.status?"warning":"question",onClose:t[12]||(t[12]=ee=>n.showOfferClassStatusModal=!1),onConfirm:a.toggleOfferClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),n.showDuplicateOfferClassModal?(S(),ct(se,{key:1,size:"lg",offerClass:n.offerClassToDuplicate,parentOfferCourse:n.parentOfferCourse,offerId:s.offerId,onClose:t[13]||(t[13]=ee=>n.showDuplicateOfferClassModal=!1),onSuccess:a.handleDuplicateSuccess},null,8,["offerClass","parentOfferCourse","offerId","onSuccess"])):te("",!0),n.showEnrolTypeModal?(S(),ct(be,{key:2,offerCourseId:(ge=n.selectedOfferCourseForClass)==null?void 0:ge.id,offerId:s.offerId,onClose:t[14]||(t[14]=ee=>n.showEnrolTypeModal=!1),onConfirm:a.handleAddClassConfirm},null,8,["offerCourseId","offerId","onConfirm"])):te("",!0),x(xe,{"is-loading":n.loading},null,8,["is-loading"]),x(ke,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])])}const eT=Le(DN,[["render",ZN],["__scopeId","data-v-7424f2fa"]]),eU="",tT={name:"CreateEdit",mixins:[rr],components:{Toast:sr,Alert:pa,LFLoading:Zo,OfferForm:Up,PageHeader:Xn,BackButton:ti,CustomButton:hs,ConfirmationModal:Jn,OfferCourseTableList:eT},props:{isReadonly:{type:Boolean,default:!1}},data(){return{saving:!1,loading:!1,offer:{id:null,name:"",type:"",description:"",status:!1,audiences:[],creatorname:"",createddate:"",modifieddate:"",modifiername:""},isEditing:!1,isValidForm:!1,showRequestSaveModal:!1}},async created(){const e=parseInt(this.$route.params.id);e&&(await this.getOffer(e),this.isEditing=!0)},methods:{async getOffer(e){try{this.loading=!0;const t=await sC(e);this.offer={...t,status:!!parseInt(t.status)}}catch(t){this.showErrorMessage(t)}finally{this.loading=!1}},async createOffer(){if(this.showRequestSaveModal=!1,!this.isValidForm){this.loading=!1;return}this.saving=!0;try{const e=await Rp(this.offer);if(e.id){const t=e.id;this.showSuccessMessage("Oferta criada com sucesso!"),this.isEditing=!0,this.$router.push({name:"offer.edit",params:{id:t}})}}catch(e){this.showErrorMessage(e)}finally{this.saving=!1}},async updateOffer(){if(this.showRequestSaveModal=!1,!this.isValidForm){this.loading=!1;return}this.saving=!0;try{await Rp(this.offer),this.isValidForm=!1,this.showSuccessMessage("Oferta atualizada com sucesso!")}catch(e){this.showErrorMessage(e)}finally{this.saving=!1}},navigateToBack(){this.$router.push({name:"offer.index"})}}},sT={class:"section-container mt-3"},rT={key:0,class:"section-title"},nT={key:1,class:"message-container"},oT={class:"d-flex flex-column mt-3"},iT={key:0,class:"text-muted"},aT={key:1,class:"text-muted"},lT={class:"actions-container"};function uT(e,t,s,i,n,a){const u=L("BackButton"),c=L("PageHeader"),f=L("Alert"),g=L("OfferForm"),h=L("OfferCourseTableList"),p=L("CustomButton"),v=L("ConfirmationModal"),C=L("LFLoading"),P=L("Toast");return S(),N("div",{id:"create-edit-component",class:me({"edit-offer":n.isEditing&&!s.isReadonly,"view-offer":s.isReadonly,"create-offer":!n.isEditing})},[x(c,{title:n.isEditing?s.isReadonly?"Visualizar oferta":`Editar oferta: ${n.offer.name}`:"Adicionar oferta"},{actions:Re(()=>[x(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1},8,["title"]),x(f,{type:"primary",icon:"fas fa-exclamation-triangle",text:`Para que uma instância de oferta seja ativada e disponibilize os cursos\r
      para os públicos-alvo configurados, é necessário garantir que pelo menos\r
      um curso, um grupo de público-alvo, e uma turma estejam configurados à\r
      instância de oferta.`}),m("div",sT,[t[5]||(t[5]=m("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),x(g,{offer:n.offer,"onUpdate:offer":t[0]||(t[0]=A=>n.offer=A),isEditing:n.isEditing,isReadonly:s.isReadonly,onValidate:t[1]||(t[1]=A=>n.isValidForm=A)},null,8,["offer","isEditing","isReadonly"])]),m("div",{class:me(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(S(),N("h2",rT,"CURSOS")):te("",!0),n.isEditing?te("",!0):(S(),N("div",nT,t[6]||(t[6]=[m("div",{class:"lock-message"},[m("i",{class:"fas fa-lock lock-icon"}),m("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))),n.isEditing?(S(),ct(h,{key:2,offerId:n.offer.id,isReadonly:s.isReadonly},null,8,["offerId","isReadonly"])):te("",!0)],2),m("div",oT,[n.offer.creatorname?(S(),N("small",iT," Criado por "+ne(n.offer.creatorname)+" em "+ne(n.offer.createddate),1)):te("",!0),n.offer.modifiername?(S(),N("small",aT," Atualizado por "+ne(n.offer.modifiername)+" em "+ne(n.offer.modifieddate),1)):te("",!0)]),m("div",lT,[x(p,{variant:"primary",label:n.saving?"Salvando...":"Salvar",isLoading:n.saving,onClick:t[2]||(t[2]=A=>n.showRequestSaveModal=!0),disabled:!n.isValidForm||s.isReadonly},null,8,["label","isLoading","disabled"]),x(p,{variant:"secondary",label:"Cancelar",onClick:a.navigateToBack},null,8,["onClick"])]),t[7]||(t[7]=m("div",{class:"required-fields-message"},[m("hr"),m("div",{class:"form-info"},[Ze(" Este formulário contém campos obrigatórios marcados com "),m("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),x(v,{show:n.showRequestSaveModal,size:"md",title:"Você tem certeza que deseja salvar as alterações feitas?","confirm-button-text":"Salvar","cancel-button-text":"Cancelar",onClose:t[3]||(t[3]=A=>n.showRequestSaveModal=!1),onConfirm:t[4]||(t[4]=A=>n.isEditing?a.updateOffer():a.createOffer())},null,8,["show"]),x(C,{"is-loading":n.loading},null,8,["is-loading"]),x(P,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],2)}const $u=Le(tT,[["render",uT],["__scopeId","data-v-580d7cee"]]);var Fu={exports:{}};const cT="2.0.0",Bp=256,dT=Number.MAX_SAFE_INTEGER||9007199254740991,fT=16,hT=Bp-6;var ma={MAX_LENGTH:Bp,MAX_SAFE_COMPONENT_LENGTH:fT,MAX_SAFE_BUILD_LENGTH:hT,MAX_SAFE_INTEGER:dT,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:cT,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2},ga=typeof process=="object"&&{}&&{}.NODE_DEBUG&&/\bsemver\b/i.test({}.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:s,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:n}=ma,a=ga;t=e.exports={};const u=t.re=[],c=t.safeRe=[],f=t.src=[],g=t.safeSrc=[],h=t.t={};let p=0;const v="[a-zA-Z0-9-]",C=[["\\s",1],["\\d",n],[v,i]],P=oe=>{for(const[W,ie]of C)oe=oe.split(`${W}*`).join(`${W}{0,${ie}}`).split(`${W}+`).join(`${W}{1,${ie}}`);return oe},A=(oe,W,ie)=>{const q=P(W),ye=p++;a(oe,ye,W),h[oe]=ye,f[ye]=W,g[ye]=q,u[ye]=new RegExp(W,ie?"g":void 0),c[ye]=new RegExp(q,ie?"g":void 0)};A("NUMERICIDENTIFIER","0|[1-9]\\d*"),A("NUMERICIDENTIFIERLOOSE","\\d+"),A("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${v}*`),A("MAINVERSION",`(${f[h.NUMERICIDENTIFIER]})\\.(${f[h.NUMERICIDENTIFIER]})\\.(${f[h.NUMERICIDENTIFIER]})`),A("MAINVERSIONLOOSE",`(${f[h.NUMERICIDENTIFIERLOOSE]})\\.(${f[h.NUMERICIDENTIFIERLOOSE]})\\.(${f[h.NUMERICIDENTIFIERLOOSE]})`),A("PRERELEASEIDENTIFIER",`(?:${f[h.NONNUMERICIDENTIFIER]}|${f[h.NUMERICIDENTIFIER]})`),A("PRERELEASEIDENTIFIERLOOSE",`(?:${f[h.NONNUMERICIDENTIFIER]}|${f[h.NUMERICIDENTIFIERLOOSE]})`),A("PRERELEASE",`(?:-(${f[h.PRERELEASEIDENTIFIER]}(?:\\.${f[h.PRERELEASEIDENTIFIER]})*))`),A("PRERELEASELOOSE",`(?:-?(${f[h.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${f[h.PRERELEASEIDENTIFIERLOOSE]})*))`),A("BUILDIDENTIFIER",`${v}+`),A("BUILD",`(?:\\+(${f[h.BUILDIDENTIFIER]}(?:\\.${f[h.BUILDIDENTIFIER]})*))`),A("FULLPLAIN",`v?${f[h.MAINVERSION]}${f[h.PRERELEASE]}?${f[h.BUILD]}?`),A("FULL",`^${f[h.FULLPLAIN]}$`),A("LOOSEPLAIN",`[v=\\s]*${f[h.MAINVERSIONLOOSE]}${f[h.PRERELEASELOOSE]}?${f[h.BUILD]}?`),A("LOOSE",`^${f[h.LOOSEPLAIN]}$`),A("GTLT","((?:<|>)?=?)"),A("XRANGEIDENTIFIERLOOSE",`${f[h.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),A("XRANGEIDENTIFIER",`${f[h.NUMERICIDENTIFIER]}|x|X|\\*`),A("XRANGEPLAIN",`[v=\\s]*(${f[h.XRANGEIDENTIFIER]})(?:\\.(${f[h.XRANGEIDENTIFIER]})(?:\\.(${f[h.XRANGEIDENTIFIER]})(?:${f[h.PRERELEASE]})?${f[h.BUILD]}?)?)?`),A("XRANGEPLAINLOOSE",`[v=\\s]*(${f[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${f[h.XRANGEIDENTIFIERLOOSE]})(?:\\.(${f[h.XRANGEIDENTIFIERLOOSE]})(?:${f[h.PRERELEASELOOSE]})?${f[h.BUILD]}?)?)?`),A("XRANGE",`^${f[h.GTLT]}\\s*${f[h.XRANGEPLAIN]}$`),A("XRANGELOOSE",`^${f[h.GTLT]}\\s*${f[h.XRANGEPLAINLOOSE]}$`),A("COERCEPLAIN",`(^|[^\\d])(\\d{1,${s}})(?:\\.(\\d{1,${s}}))?(?:\\.(\\d{1,${s}}))?`),A("COERCE",`${f[h.COERCEPLAIN]}(?:$|[^\\d])`),A("COERCEFULL",f[h.COERCEPLAIN]+`(?:${f[h.PRERELEASE]})?(?:${f[h.BUILD]})?(?:$|[^\\d])`),A("COERCERTL",f[h.COERCE],!0),A("COERCERTLFULL",f[h.COERCEFULL],!0),A("LONETILDE","(?:~>?)"),A("TILDETRIM",`(\\s*)${f[h.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",A("TILDE",`^${f[h.LONETILDE]}${f[h.XRANGEPLAIN]}$`),A("TILDELOOSE",`^${f[h.LONETILDE]}${f[h.XRANGEPLAINLOOSE]}$`),A("LONECARET","(?:\\^)"),A("CARETTRIM",`(\\s*)${f[h.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",A("CARET",`^${f[h.LONECARET]}${f[h.XRANGEPLAIN]}$`),A("CARETLOOSE",`^${f[h.LONECARET]}${f[h.XRANGEPLAINLOOSE]}$`),A("COMPARATORLOOSE",`^${f[h.GTLT]}\\s*(${f[h.LOOSEPLAIN]})$|^$`),A("COMPARATOR",`^${f[h.GTLT]}\\s*(${f[h.FULLPLAIN]})$|^$`),A("COMPARATORTRIM",`(\\s*)${f[h.GTLT]}\\s*(${f[h.LOOSEPLAIN]}|${f[h.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",A("HYPHENRANGE",`^\\s*(${f[h.XRANGEPLAIN]})\\s+-\\s+(${f[h.XRANGEPLAIN]})\\s*$`),A("HYPHENRANGELOOSE",`^\\s*(${f[h.XRANGEPLAINLOOSE]})\\s+-\\s+(${f[h.XRANGEPLAINLOOSE]})\\s*$`),A("STAR","(<|>)?=?\\s*\\*"),A("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),A("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(Fu,Fu.exports);var si=Fu.exports;const pT=Object.freeze({loose:!0}),mT=Object.freeze({});var Uu=e=>e?typeof e!="object"?pT:e:mT;const qp=/^[0-9]+$/,Hp=(e,t)=>{const s=qp.test(e),i=qp.test(t);return s&&i&&(e=+e,t=+t),e===t?0:s&&!i?-1:i&&!s?1:e<t?-1:1};var Wp={compareIdentifiers:Hp,rcompareIdentifiers:(e,t)=>Hp(t,e)};const _a=ga,{MAX_LENGTH:jp,MAX_SAFE_INTEGER:va}=ma,{safeRe:ya,t:ba}=si,gT=Uu,{compareIdentifiers:Qn}=Wp;var ts=class lr{constructor(t,s){if(s=gT(s),t instanceof lr){if(t.loose===!!s.loose&&t.includePrerelease===!!s.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>jp)throw new TypeError(`version is longer than ${jp} characters`);_a("SemVer",t,s),this.options=s,this.loose=!!s.loose,this.includePrerelease=!!s.includePrerelease;const i=t.trim().match(s.loose?ya[ba.LOOSE]:ya[ba.FULL]);if(!i)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>va||this.major<0)throw new TypeError("Invalid major version");if(this.minor>va||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>va||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(n=>{if(/^[0-9]+$/.test(n)){const a=+n;if(a>=0&&a<va)return a}return n}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(_a("SemVer.compare",this.version,this.options,t),!(t instanceof lr)){if(typeof t=="string"&&t===this.version)return 0;t=new lr(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof lr||(t=new lr(t,this.options)),Qn(this.major,t.major)||Qn(this.minor,t.minor)||Qn(this.patch,t.patch)}comparePre(t){if(t instanceof lr||(t=new lr(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let s=0;do{const i=this.prerelease[s],n=t.prerelease[s];if(_a("prerelease compare",s,i,n),i===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(i===void 0)return-1;if(i===n)continue;return Qn(i,n)}while(++s)}compareBuild(t){t instanceof lr||(t=new lr(t,this.options));let s=0;do{const i=this.build[s],n=t.build[s];if(_a("build compare",s,i,n),i===void 0&&n===void 0)return 0;if(n===void 0)return 1;if(i===void 0)return-1;if(i===n)continue;return Qn(i,n)}while(++s)}inc(t,s,i){if(t.startsWith("pre")){if(!s&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(s){const n=`-${s}`.match(this.options.loose?ya[ba.PRERELEASELOOSE]:ya[ba.PRERELEASE]);if(!n||n[1]!==s)throw new Error(`invalid identifier: ${s}`)}}switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",s,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",s,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",s,i),this.inc("pre",s,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",s,i),this.inc("pre",s,i);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const n=Number(i)?1:0;if(this.prerelease.length===0)this.prerelease=[n];else{let a=this.prerelease.length;for(;--a>=0;)typeof this.prerelease[a]=="number"&&(this.prerelease[a]++,a=-2);if(a===-1){if(s===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(n)}}if(s){let a=[s,n];i===!1&&(a=[s]),Qn(this.prerelease[0],s)===0?isNaN(this.prerelease[1])&&(this.prerelease=a):this.prerelease=a}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};const Gp=ts;var Zn=(e,t,s=!1)=>{if(e instanceof Gp)return e;try{return new Gp(e,t)}catch(i){if(!s)return null;throw i}};const _T=Zn;var vT=(e,t)=>{const s=_T(e,t);return s?s.version:null};const yT=Zn;var bT=(e,t)=>{const s=yT(e.trim().replace(/^[=v]+/,""),t);return s?s.version:null};const zp=ts;var ET=(e,t,s,i,n)=>{typeof s=="string"&&(n=i,i=s,s=void 0);try{return new zp(e instanceof zp?e.version:e,s).inc(t,i,n).version}catch{return null}};const Kp=Zn;var CT=(e,t)=>{const s=Kp(e,null,!0),i=Kp(t,null,!0),n=s.compare(i);if(n===0)return null;const a=n>0,u=a?s:i,c=a?i:s,f=!!u.prerelease.length;if(!!c.prerelease.length&&!f){if(!c.patch&&!c.minor)return"major";if(c.compareMain(u)===0)return c.minor&&!c.patch?"minor":"patch"}const h=f?"pre":"";return s.major!==i.major?h+"major":s.minor!==i.minor?h+"minor":s.patch!==i.patch?h+"patch":"prerelease"};const wT=ts;var OT=(e,t)=>new wT(e,t).major;const xT=ts;var ST=(e,t)=>new xT(e,t).minor;const IT=ts;var DT=(e,t)=>new IT(e,t).patch;const NT=Zn;var TT=(e,t)=>{const s=NT(e,t);return s&&s.prerelease.length?s.prerelease:null};const Xp=ts;var Us=(e,t,s)=>new Xp(e,s).compare(new Xp(t,s));const AT=Us;var RT=(e,t,s)=>AT(t,e,s);const PT=Us;var MT=(e,t)=>PT(e,t,!0);const Yp=ts;var Bu=(e,t,s)=>{const i=new Yp(e,s),n=new Yp(t,s);return i.compare(n)||i.compareBuild(n)};const kT=Bu;var VT=(e,t)=>e.sort((s,i)=>kT(s,i,t));const LT=Bu;var $T=(e,t)=>e.sort((s,i)=>LT(i,s,t));const FT=Us;var Ea=(e,t,s)=>FT(e,t,s)>0;const UT=Us;var qu=(e,t,s)=>UT(e,t,s)<0;const BT=Us;var Jp=(e,t,s)=>BT(e,t,s)===0;const qT=Us;var Qp=(e,t,s)=>qT(e,t,s)!==0;const HT=Us;var Hu=(e,t,s)=>HT(e,t,s)>=0;const WT=Us;var Wu=(e,t,s)=>WT(e,t,s)<=0;const jT=Jp,GT=Qp,zT=Ea,KT=Hu,XT=qu,YT=Wu;var Zp=(e,t,s,i)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof s=="object"&&(s=s.version),e===s;case"!==":return typeof e=="object"&&(e=e.version),typeof s=="object"&&(s=s.version),e!==s;case"":case"=":case"==":return jT(e,s,i);case"!=":return GT(e,s,i);case">":return zT(e,s,i);case">=":return KT(e,s,i);case"<":return XT(e,s,i);case"<=":return YT(e,s,i);default:throw new TypeError(`Invalid operator: ${t}`)}};const JT=ts,QT=Zn,{safeRe:Ca,t:wa}=si;var ZT=(e,t)=>{if(e instanceof JT)return e;if(typeof e=="number"&&(e=String(e)),typeof e!="string")return null;t=t||{};let s=null;if(!t.rtl)s=e.match(t.includePrerelease?Ca[wa.COERCEFULL]:Ca[wa.COERCE]);else{const f=t.includePrerelease?Ca[wa.COERCERTLFULL]:Ca[wa.COERCERTL];let g;for(;(g=f.exec(e))&&(!s||s.index+s[0].length!==e.length);)(!s||g.index+g[0].length!==s.index+s[0].length)&&(s=g),f.lastIndex=g.index+g[1].length+g[2].length;f.lastIndex=-1}if(s===null)return null;const i=s[2],n=s[3]||"0",a=s[4]||"0",u=t.includePrerelease&&s[5]?`-${s[5]}`:"",c=t.includePrerelease&&s[6]?`+${s[6]}`:"";return QT(`${i}.${n}.${a}${u}${c}`,t)};class eA{constructor(){this.max=1e3,this.map=new Map}get(t){const s=this.map.get(t);if(s!==void 0)return this.map.delete(t),this.map.set(t,s),s}delete(t){return this.map.delete(t)}set(t,s){if(!this.delete(t)&&s!==void 0){if(this.map.size>=this.max){const n=this.map.keys().next().value;this.delete(n)}this.map.set(t,s)}return this}}var tA=eA,ju,em;function Bs(){if(em)return ju;em=1;const e=/\s+/g;class t{constructor(Y,pe){if(pe=n(pe),Y instanceof t)return Y.loose===!!pe.loose&&Y.includePrerelease===!!pe.includePrerelease?Y:new t(Y.raw,pe);if(Y instanceof a)return this.raw=Y.value,this.set=[[Y]],this.formatted=void 0,this;if(this.options=pe,this.loose=!!pe.loose,this.includePrerelease=!!pe.includePrerelease,this.raw=Y.trim().replace(e," "),this.set=this.raw.split("||").map(j=>this.parseRange(j.trim())).filter(j=>j.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const j=this.set[0];if(this.set=this.set.filter(J=>!A(J[0])),this.set.length===0)this.set=[j];else if(this.set.length>1){for(const J of this.set)if(J.length===1&&oe(J[0])){this.set=[J];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let Y=0;Y<this.set.length;Y++){Y>0&&(this.formatted+="||");const pe=this.set[Y];for(let j=0;j<pe.length;j++)j>0&&(this.formatted+=" "),this.formatted+=pe[j].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(Y){const j=((this.options.includePrerelease&&C)|(this.options.loose&&P))+":"+Y,J=i.get(j);if(J)return J;const ge=this.options.loose,ee=ge?f[g.HYPHENRANGELOOSE]:f[g.HYPHENRANGE];Y=Y.replace(ee,ue(this.options.includePrerelease)),u("hyphen replace",Y),Y=Y.replace(f[g.COMPARATORTRIM],h),u("comparator trim",Y),Y=Y.replace(f[g.TILDETRIM],p),u("tilde trim",Y),Y=Y.replace(f[g.CARETTRIM],v),u("caret trim",Y);let _e=Y.split(" ").map(ot=>ie(ot,this.options)).join(" ").split(/\s+/).map(ot=>$e(ot,this.options));ge&&(_e=_e.filter(ot=>(u("loose invalid filter",ot,this.options),!!ot.match(f[g.COMPARATORLOOSE])))),u("range list",_e);const Se=new Map,Ye=_e.map(ot=>new a(ot,this.options));for(const ot of Ye){if(A(ot))return[ot];Se.set(ot.value,ot)}Se.size>1&&Se.has("")&&Se.delete("");const yt=[...Se.values()];return i.set(j,yt),yt}intersects(Y,pe){if(!(Y instanceof t))throw new TypeError("a Range is required");return this.set.some(j=>W(j,pe)&&Y.set.some(J=>W(J,pe)&&j.every(ge=>J.every(ee=>ge.intersects(ee,pe)))))}test(Y){if(!Y)return!1;if(typeof Y=="string")try{Y=new c(Y,this.options)}catch{return!1}for(let pe=0;pe<this.set.length;pe++)if(R(this.set[pe],Y,this.options))return!0;return!1}}ju=t;const s=tA,i=new s,n=Uu,a=Oa(),u=ga,c=ts,{safeRe:f,t:g,comparatorTrimReplace:h,tildeTrimReplace:p,caretTrimReplace:v}=si,{FLAG_INCLUDE_PRERELEASE:C,FLAG_LOOSE:P}=ma,A=G=>G.value==="<0.0.0-0",oe=G=>G.value==="",W=(G,Y)=>{let pe=!0;const j=G.slice();let J=j.pop();for(;pe&&j.length;)pe=j.every(ge=>J.intersects(ge,Y)),J=j.pop();return pe},ie=(G,Y)=>(u("comp",G,Y),G=be(G,Y),u("caret",G),G=ye(G,Y),u("tildes",G),G=ke(G,Y),u("xrange",G),G=De(G,Y),u("stars",G),G),q=G=>!G||G.toLowerCase()==="x"||G==="*",ye=(G,Y)=>G.trim().split(/\s+/).map(pe=>se(pe,Y)).join(" "),se=(G,Y)=>{const pe=Y.loose?f[g.TILDELOOSE]:f[g.TILDE];return G.replace(pe,(j,J,ge,ee,_e)=>{u("tilde",G,j,J,ge,ee,_e);let Se;return q(J)?Se="":q(ge)?Se=`>=${J}.0.0 <${+J+1}.0.0-0`:q(ee)?Se=`>=${J}.${ge}.0 <${J}.${+ge+1}.0-0`:_e?(u("replaceTilde pr",_e),Se=`>=${J}.${ge}.${ee}-${_e} <${J}.${+ge+1}.0-0`):Se=`>=${J}.${ge}.${ee} <${J}.${+ge+1}.0-0`,u("tilde return",Se),Se})},be=(G,Y)=>G.trim().split(/\s+/).map(pe=>xe(pe,Y)).join(" "),xe=(G,Y)=>{u("caret",G,Y);const pe=Y.loose?f[g.CARETLOOSE]:f[g.CARET],j=Y.includePrerelease?"-0":"";return G.replace(pe,(J,ge,ee,_e,Se)=>{u("caret",G,J,ge,ee,_e,Se);let Ye;return q(ge)?Ye="":q(ee)?Ye=`>=${ge}.0.0${j} <${+ge+1}.0.0-0`:q(_e)?ge==="0"?Ye=`>=${ge}.${ee}.0${j} <${ge}.${+ee+1}.0-0`:Ye=`>=${ge}.${ee}.0${j} <${+ge+1}.0.0-0`:Se?(u("replaceCaret pr",Se),ge==="0"?ee==="0"?Ye=`>=${ge}.${ee}.${_e}-${Se} <${ge}.${ee}.${+_e+1}-0`:Ye=`>=${ge}.${ee}.${_e}-${Se} <${ge}.${+ee+1}.0-0`:Ye=`>=${ge}.${ee}.${_e}-${Se} <${+ge+1}.0.0-0`):(u("no pr"),ge==="0"?ee==="0"?Ye=`>=${ge}.${ee}.${_e}${j} <${ge}.${ee}.${+_e+1}-0`:Ye=`>=${ge}.${ee}.${_e}${j} <${ge}.${+ee+1}.0-0`:Ye=`>=${ge}.${ee}.${_e} <${+ge+1}.0.0-0`),u("caret return",Ye),Ye})},ke=(G,Y)=>(u("replaceXRanges",G,Y),G.split(/\s+/).map(pe=>de(pe,Y)).join(" ")),de=(G,Y)=>{G=G.trim();const pe=Y.loose?f[g.XRANGELOOSE]:f[g.XRANGE];return G.replace(pe,(j,J,ge,ee,_e,Se)=>{u("xRange",G,j,J,ge,ee,_e,Se);const Ye=q(ge),yt=Ye||q(ee),ot=yt||q(_e),Mt=ot;return J==="="&&Mt&&(J=""),Se=Y.includePrerelease?"-0":"",Ye?J===">"||J==="<"?j="<0.0.0-0":j="*":J&&Mt?(yt&&(ee=0),_e=0,J===">"?(J=">=",yt?(ge=+ge+1,ee=0,_e=0):(ee=+ee+1,_e=0)):J==="<="&&(J="<",yt?ge=+ge+1:ee=+ee+1),J==="<"&&(Se="-0"),j=`${J+ge}.${ee}.${_e}${Se}`):yt?j=`>=${ge}.0.0${Se} <${+ge+1}.0.0-0`:ot&&(j=`>=${ge}.${ee}.0${Se} <${ge}.${+ee+1}.0-0`),u("xRange return",j),j})},De=(G,Y)=>(u("replaceStars",G,Y),G.trim().replace(f[g.STAR],"")),$e=(G,Y)=>(u("replaceGTE0",G,Y),G.trim().replace(f[Y.includePrerelease?g.GTE0PRE:g.GTE0],"")),ue=G=>(Y,pe,j,J,ge,ee,_e,Se,Ye,yt,ot,Mt)=>(q(j)?pe="":q(J)?pe=`>=${j}.0.0${G?"-0":""}`:q(ge)?pe=`>=${j}.${J}.0${G?"-0":""}`:ee?pe=`>=${pe}`:pe=`>=${pe}${G?"-0":""}`,q(Ye)?Se="":q(yt)?Se=`<${+Ye+1}.0.0-0`:q(ot)?Se=`<${Ye}.${+yt+1}.0-0`:Mt?Se=`<=${Ye}.${yt}.${ot}-${Mt}`:G?Se=`<${Ye}.${yt}.${+ot+1}-0`:Se=`<=${Se}`,`${pe} ${Se}`.trim()),R=(G,Y,pe)=>{for(let j=0;j<G.length;j++)if(!G[j].test(Y))return!1;if(Y.prerelease.length&&!pe.includePrerelease){for(let j=0;j<G.length;j++)if(u(G[j].semver),G[j].semver!==a.ANY&&G[j].semver.prerelease.length>0){const J=G[j].semver;if(J.major===Y.major&&J.minor===Y.minor&&J.patch===Y.patch)return!0}return!1}return!0};return ju}var Gu,tm;function Oa(){if(tm)return Gu;tm=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(h,p){if(p=s(p),h instanceof t){if(h.loose===!!p.loose)return h;h=h.value}h=h.trim().split(/\s+/).join(" "),u("comparator",h,p),this.options=p,this.loose=!!p.loose,this.parse(h),this.semver===e?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(h){const p=this.options.loose?i[n.COMPARATORLOOSE]:i[n.COMPARATOR],v=h.match(p);if(!v)throw new TypeError(`Invalid comparator: ${h}`);this.operator=v[1]!==void 0?v[1]:"",this.operator==="="&&(this.operator=""),v[2]?this.semver=new c(v[2],this.options.loose):this.semver=e}toString(){return this.value}test(h){if(u("Comparator.test",h,this.options.loose),this.semver===e||h===e)return!0;if(typeof h=="string")try{h=new c(h,this.options)}catch{return!1}return a(h,this.operator,this.semver,this.options)}intersects(h,p){if(!(h instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new f(h.value,p).test(this.value):h.operator===""?h.value===""?!0:new f(this.value,p).test(h.semver):(p=s(p),p.includePrerelease&&(this.value==="<0.0.0-0"||h.value==="<0.0.0-0")||!p.includePrerelease&&(this.value.startsWith("<0.0.0")||h.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&h.operator.startsWith(">")||this.operator.startsWith("<")&&h.operator.startsWith("<")||this.semver.version===h.semver.version&&this.operator.includes("=")&&h.operator.includes("=")||a(this.semver,"<",h.semver,p)&&this.operator.startsWith(">")&&h.operator.startsWith("<")||a(this.semver,">",h.semver,p)&&this.operator.startsWith("<")&&h.operator.startsWith(">")))}}Gu=t;const s=Uu,{safeRe:i,t:n}=si,a=Zp,u=ga,c=ts,f=Bs();return Gu}const sA=Bs();var xa=(e,t,s)=>{try{t=new sA(t,s)}catch{return!1}return t.test(e)};const rA=Bs();var nA=(e,t)=>new rA(e,t).set.map(s=>s.map(i=>i.value).join(" ").trim().split(" "));const oA=ts,iA=Bs();var aA=(e,t,s)=>{let i=null,n=null,a=null;try{a=new iA(t,s)}catch{return null}return e.forEach(u=>{a.test(u)&&(!i||n.compare(u)===-1)&&(i=u,n=new oA(i,s))}),i};const lA=ts,uA=Bs();var cA=(e,t,s)=>{let i=null,n=null,a=null;try{a=new uA(t,s)}catch{return null}return e.forEach(u=>{a.test(u)&&(!i||n.compare(u)===1)&&(i=u,n=new lA(i,s))}),i};const zu=ts,dA=Bs(),sm=Ea;var fA=(e,t)=>{e=new dA(e,t);let s=new zu("0.0.0");if(e.test(s)||(s=new zu("0.0.0-0"),e.test(s)))return s;s=null;for(let i=0;i<e.set.length;++i){const n=e.set[i];let a=null;n.forEach(u=>{const c=new zu(u.semver.version);switch(u.operator){case">":c.prerelease.length===0?c.patch++:c.prerelease.push(0),c.raw=c.format();case"":case">=":(!a||sm(c,a))&&(a=c);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${u.operator}`)}}),a&&(!s||sm(s,a))&&(s=a)}return s&&e.test(s)?s:null};const hA=Bs();var pA=(e,t)=>{try{return new hA(e,t).range||"*"}catch{return null}};const mA=ts,rm=Oa(),{ANY:gA}=rm,_A=Bs(),vA=xa,nm=Ea,om=qu,yA=Wu,bA=Hu;var Ku=(e,t,s,i)=>{e=new mA(e,i),t=new _A(t,i);let n,a,u,c,f;switch(s){case">":n=nm,a=yA,u=om,c=">",f=">=";break;case"<":n=om,a=bA,u=nm,c="<",f="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(vA(e,t,i))return!1;for(let g=0;g<t.set.length;++g){const h=t.set[g];let p=null,v=null;if(h.forEach(C=>{C.semver===gA&&(C=new rm(">=0.0.0")),p=p||C,v=v||C,n(C.semver,p.semver,i)?p=C:u(C.semver,v.semver,i)&&(v=C)}),p.operator===c||p.operator===f||(!v.operator||v.operator===c)&&a(e,v.semver))return!1;if(v.operator===f&&u(e,v.semver))return!1}return!0};const EA=Ku;var CA=(e,t,s)=>EA(e,t,">",s);const wA=Ku;var OA=(e,t,s)=>wA(e,t,"<",s);const im=Bs();var xA=(e,t,s)=>(e=new im(e,s),t=new im(t,s),e.intersects(t,s));const SA=xa,IA=Us;var DA=(e,t,s)=>{const i=[];let n=null,a=null;const u=e.sort((h,p)=>IA(h,p,s));for(const h of u)SA(h,t,s)?(a=h,n||(n=h)):(a&&i.push([n,a]),a=null,n=null);n&&i.push([n,null]);const c=[];for(const[h,p]of i)h===p?c.push(h):!p&&h===u[0]?c.push("*"):p?h===u[0]?c.push(`<=${p}`):c.push(`${h} - ${p}`):c.push(`>=${h}`);const f=c.join(" || "),g=typeof t.raw=="string"?t.raw:String(t);return f.length<g.length?f:t};const am=Bs(),Xu=Oa(),{ANY:Yu}=Xu,ri=xa,Ju=Us,NA=(e,t,s={})=>{if(e===t)return!0;e=new am(e,s),t=new am(t,s);let i=!1;e:for(const n of e.set){for(const a of t.set){const u=AA(n,a,s);if(i=i||u!==null,u)continue e}if(i)return!1}return!0},TA=[new Xu(">=0.0.0-0")],lm=[new Xu(">=0.0.0")],AA=(e,t,s)=>{if(e===t)return!0;if(e.length===1&&e[0].semver===Yu){if(t.length===1&&t[0].semver===Yu)return!0;s.includePrerelease?e=TA:e=lm}if(t.length===1&&t[0].semver===Yu){if(s.includePrerelease)return!0;t=lm}const i=new Set;let n,a;for(const C of e)C.operator===">"||C.operator===">="?n=um(n,C,s):C.operator==="<"||C.operator==="<="?a=cm(a,C,s):i.add(C.semver);if(i.size>1)return null;let u;if(n&&a){if(u=Ju(n.semver,a.semver,s),u>0)return null;if(u===0&&(n.operator!==">="||a.operator!=="<="))return null}for(const C of i){if(n&&!ri(C,String(n),s)||a&&!ri(C,String(a),s))return null;for(const P of t)if(!ri(C,String(P),s))return!1;return!0}let c,f,g,h,p=a&&!s.includePrerelease&&a.semver.prerelease.length?a.semver:!1,v=n&&!s.includePrerelease&&n.semver.prerelease.length?n.semver:!1;p&&p.prerelease.length===1&&a.operator==="<"&&p.prerelease[0]===0&&(p=!1);for(const C of t){if(h=h||C.operator===">"||C.operator===">=",g=g||C.operator==="<"||C.operator==="<=",n){if(v&&C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===v.major&&C.semver.minor===v.minor&&C.semver.patch===v.patch&&(v=!1),C.operator===">"||C.operator===">="){if(c=um(n,C,s),c===C&&c!==n)return!1}else if(n.operator===">="&&!ri(n.semver,String(C),s))return!1}if(a){if(p&&C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===p.major&&C.semver.minor===p.minor&&C.semver.patch===p.patch&&(p=!1),C.operator==="<"||C.operator==="<="){if(f=cm(a,C,s),f===C&&f!==a)return!1}else if(a.operator==="<="&&!ri(a.semver,String(C),s))return!1}if(!C.operator&&(a||n)&&u!==0)return!1}return!(n&&g&&!a&&u!==0||a&&h&&!n&&u!==0||v||p)},um=(e,t,s)=>{if(!e)return t;const i=Ju(e.semver,t.semver,s);return i>0?e:i<0||t.operator===">"&&e.operator===">="?t:e},cm=(e,t,s)=>{if(!e)return t;const i=Ju(e.semver,t.semver,s);return i<0?e:i>0||t.operator==="<"&&e.operator==="<="?t:e};var RA=NA;const Qu=si,dm=ma,PA=ts,fm=Wp,MA=Zn,kA=vT,VA=bT,LA=ET,$A=CT,FA=OT,UA=ST,BA=DT,qA=TT,HA=Us,WA=RT,jA=MT,GA=Bu,zA=VT,KA=$T,XA=Ea,YA=qu,JA=Jp,QA=Qp,ZA=Hu,eR=Wu,tR=Zp,sR=ZT,rR=Oa(),nR=Bs(),oR=xa,iR=nA,aR=aA,lR=cA,uR=fA,cR=pA,dR=Ku,fR=CA,hR=OA,pR=xA,mR=DA,gR=RA;Qu.re,Qu.src,Qu.t,dm.SEMVER_SPEC_VERSION,dm.RELEASE_TYPES,fm.compareIdentifiers,fm.rcompareIdentifiers;const FU="",_R={name:"Form",mixins:[rr],components:{CustomLabel:wr,CustomInput:On,CustomSelect:nr,CustomButton:hs,PageHeader:Xn,BackButton:ti,Autocomplete:Or,TextEditor:Lu,CustomCheckbox:Yn,FilterRow:ca,FilterGroup:da,Toast:sr,HelpIcon:fa,FilterTag:ei,FilterTags:ha},props:{offerCourse:{type:Object,required:!0},offerClass:{type:Object,required:!0},isEditing:{type:Boolean,required:!0},isReadonly:{type:Boolean,default:!1}},emits:["update:offerClass","validate"],data(){return{loading:!1,loadingTeachers:!1,localOfferClass:{},divisionOptions:[],sectorOptions:[],groupOptions:[],dealershipOptions:[],roleOptions:[],teacherOptions:[],modalityOptions:[],situationOptions:[],formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Perfil padrão é obrigatório"},modality:{hasError:!1,message:"Modalidade é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},reenrolmentsituations:{hasError:!1,message:"É necessário selecionar um status quando a rematrícula estiver habilitada"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"},maxusersdealership:{hasError:!1,message:"Número máximo de inscrições por concessionária deve ser maior ou igual a zero"}}}},async created(){await this.getInitialData(),this.debouncedSearchTeachers=tr.debounce(e=>{this.fetchPotentialTeachers(e)},300)},computed:{maxEnrolPeriod(){if(this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.localOfferClass.startdate,this.localOfferClass.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.localOfferClass.startdate&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&this.localOfferClass.startdate===this.localOfferClass.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{offerClass:{handler(e){tr.isEqual(e,this.localOfferClass)||(this.localOfferClass={...e})},deep:!0,immediate:!0},localOfferClass:{handler(e){tr.isEqual(e,this.offerClass)||this.$emit("update:offerClass",e)},deep:!0},"localOfferClass.optional_fields.enablehirearchyrestriction":function(e,t){tr.isEqual(e,t)||e||(this.localOfferClass.optional_fields={...this.localOfferClass.optional_fields,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]})},"localOfferClass.optional_fields.hirearchyrestrictiondivisions":function(e,t){if(tr.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictionsectors=[],this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getSectors(s)},"localOfferClass.optional_fields.hirearchyrestrictionsectors":function(e,t){if(tr.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiongroups=[],this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getGroups(s)},"localOfferClass.optional_fields.hirearchyrestrictiongroups":function(e,t){if(tr.isEqual(e,t))return;if(!e.length){this.localOfferClass.optional_fields.hirearchyrestrictiondealerships=[];return}const s=e.map(i=>i.value);this.getDealerships(s)},"localOfferClass.optional_fields.enableenrolperiod":function(e){!e&&this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"localOfferClass.optional_fields.enableenddate":function(e){console.log("enableenddate",e),e||(this.localOfferClass.startdate=null,this.localOfferClass.optional_fields.enddate=null)},"localOfferClass.startdate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enddate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.localOfferClass.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enableenddate":function(e){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"localOfferClass.optional_fields.enablepreenrolment":function(e){e||(this.localOfferClass.optional_fields.preenrolmentstartdate=null,this.localOfferClass.optional_fields.preenrolmentenddate=null)},"localOfferClass.optional_fields.preenrolmentstartdate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.preenrolmentenddate":function(){this.localOfferClass.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"localOfferClass.optional_fields.enablereenrol":function(){this.isValidField("reenrolmentsituations")}},methods:{async getInitialData(){try{this.loading=!0,await this.setEnrolmentMethod(),await this.getRoles(),await this.getModalities(),await this.getSituations(),await this.getHierarchyRestrictionData()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async getRoles(){const e=await ku(this.offerCourse.id);if(this.roleOptions=e.map(t=>({value:t.id,label:t.name})).sort((t,s)=>t.label.localeCompare(s.label)),!this.localOfferClass.optional_fields.roleid){const t=this.roleOptions.find(s=>s.value===5);this.localOfferClass.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async getModalities(){this.modalityOptions=[{value:"presencial",label:"Presencial"},{value:"web",label:"WEB"},{value:"virtual",label:"Virtual"},{value:"blended",label:"Blended"}],this.localOfferClass.optional_fields.modality||(this.localOfferClass.optional_fields.modality=this.modalityOptions[0].value)},async getSituations(){const e=await vC();this.situationOptions=e.map(t=>({value:t.id,label:t.name}))},async setEnrolmentMethod(){try{if(this.isEditing)return;const e=this.$route.query.enrolMethod;if(!e)throw new Error("Método de inscrição não informado");this.localOfferClass.enrol=e}catch(e){this.showErrorMessage(e)}},async getHierarchyRestrictionData(){await this.getDivisions(),this.localOfferClass.optional_fields.enablehirearchyrestriction&&(this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length&&await this.getSectors(),this.localOfferClass.optional_fields.hirearchyrestrictionsectors.length&&await this.getGroups(),this.localOfferClass.optional_fields.hirearchyrestrictiongroups.length&&await this.getDealerships())},async getDivisions(){try{const e=await wC();this.divisionOptions=e.map(t=>({value:t.id,label:t.name}))}catch(e){this.showErrorMessage(e)}},async getSectors(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiondivisions.map(s=>s.value));const t=await OC(e);this.sectorOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async getGroups(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictionsectors.map(s=>s.value));const t=await xC(e);this.groupOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async getDealerships(e){try{e||(e=this.localOfferClass.optional_fields.hirearchyrestrictiongroups.map(s=>s.value));const t=await SC(e);this.dealershipOptions=t.map(s=>({value:s.id,label:s.name}))}catch(t){this.showErrorMessage(t)}},async fetchPotentialTeachers(e){try{if(e.length<3)return;this.loadingTeachers=!0;let t=this.localOfferClass.teachers.map(i=>i.value)??[];const s=await _C(this.offerCourse.id,this.classId,e,t);this.teacherOptions=s.map(i=>({value:i.id,label:i.fullname}))}catch(t){this.showErrorMessage(t)}finally{this.loadingTeachers=!1}},validateForm(){let e=!0;return Object.keys(this.formErrors).forEach(t=>{this.isValidField(t)||(e=!1)}),this.$emit("validate",e),e},isValidField(e){switch(e){case"classname":this.formErrors.classname.hasError=!this.localOfferClass.classname;break;case"startdate":const t=this.localOfferClass.startdate,s=t&&this.localOfferClass.optional_fields.enableenddate&&this.localOfferClass.optional_fields.enddate&&new Date(this.localOfferClass.startdate)>new Date(this.localOfferClass.optional_fields.enddate);t?s?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.localOfferClass.optional_fields.roleid;break;case"enddate":const i=this.localOfferClass.optional_fields.enableenddate,n=this.localOfferClass.optional_fields.enddate,a=i&&n&&this.localOfferClass.startdate&&new Date(this.localOfferClass.optional_fields.enddate)<new Date(this.localOfferClass.startdate);i&&!n?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):a?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.localOfferClass.optional_fields.enablepreenrolment&&!this.localOfferClass.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const u=this.localOfferClass.optional_fields.enableenrolperiod,c=this.localOfferClass.optional_fields.enrolperiod!==null&&this.localOfferClass.optional_fields.enrolperiod!==void 0&&this.localOfferClass.optional_fields.enrolperiod!=="",f=this.maxEnrolPeriod!==null&&c&&parseInt(this.localOfferClass.optional_fields.enrolperiod)>this.maxEnrolPeriod;u&&!c?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):u&&f?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"enableextension":this.validateExtensionPeriod(),this.validateExtensionDaysAvailable(),this.validateExtensionMaxRequests();break;case"extensionperiod":this.validateExtensionPeriod();break;case"extensiondaysavailable":this.validateExtensionDaysAvailable();break;case"extensionmaxrequests":this.validateExtensionMaxRequests();break;case"reenrolmentsituations":this.validateReenrolmentSituations();break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers(),this.validateMaxUsersByDealership();break;case"maxusersdealership":this.validateMaxUsersByDealership();break}return!this.formErrors[e].hasError},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.localOfferClass.optional_fields.enableenrolperiod&&(this.localOfferClass.optional_fields.enableenrolperiod=!1,this.localOfferClass.optional_fields.enrolperiod=null,this.localOfferClass.optional_fields.enableextension&&(this.localOfferClass.optional_fields.enableextension=!1,this.localOfferClass.optional_fields.extensionperiod=null,this.localOfferClass.optional_fields.extensiondaysavailable=null,this.localOfferClass.optional_fields.extensionmaxrequests=null),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.localOfferClass.optional_fields.enablepreenrolment){const t=this.localOfferClass.startdate,s=this.localOfferClass.optional_fields.enableenddate?this.localOfferClass.optional_fields.enddate:null,i=this.localOfferClass.optional_fields.preenrolmentstartdate,n=this.localOfferClass.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=parseInt(this.localOfferClass.optional_fields.minusers),t=parseInt(this.localOfferClass.optional_fields.maxusers);return this.formErrors.minusers.hasError=!1,e===0?!0:t>0&&e>t?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor ou igual que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=parseInt(this.localOfferClass.optional_fields.minusers),t=parseInt(this.localOfferClass.optional_fields.maxusers);return this.formErrors.maxusers.hasError=!1,t===0?!0:e>0&&t<e?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior ou igual que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},validateMaxUsersByDealership(){const e=parseInt(this.localOfferClass.optional_fields.maxusers),t=parseInt(this.localOfferClass.optional_fields.maxusersdealership);if(this.formErrors.maxusersdealership.hasError=!1,e===0||t===0)return!0;if(t>e)return this.formErrors.maxusersdealership.message="Número máximo de inscrições por concessionária deve ser menor ou igual ao máximo de inscrições",this.formErrors.maxusersdealership.hasError=!0,!1},validateExtensionPeriod(){this.formErrors.extensionperiod.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionperiod},validateExtensionDaysAvailable(){this.formErrors.extensiondaysavailable.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensiondaysavailable},validateExtensionMaxRequests(){this.formErrors.extensionmaxrequests.hasError=this.localOfferClass.optional_fields.enableextension&&this.localOfferClass.optional_fields.enableenrolperiod&&!this.localOfferClass.optional_fields.extensionmaxrequests},validateReenrolmentSituations(){this.formErrors.reenrolmentsituations.hasError=this.localOfferClass.optional_fields.enablereenrol&&!this.localOfferClass.optional_fields.reenrolmentsituations.length},navigateToBack(){this.offerCourse.offerid?this.router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})}}},vR={class:"row"},yR={class:"col-md-3"},bR={class:"form-group"},ER={class:"col-md-3"},CR={class:"form-group"},wR={class:"col-md-3"},OR={class:"col-md-3 ml-md-n3 mt-n3 mt-md-0"},xR={class:"form-group"},SR={class:"row"},IR={class:"col-12"},DR={class:"form-group"},NR={class:"row"},TR={class:"col-md-3"},AR={class:"col-md-3"},RR={class:"col-md-3 ml-md-n3 mt-n3 mt-md-0"},PR={class:"form-group"},MR={key:0,class:"row"},kR={key:0,class:"col-md-3"},VR={class:"form-group"},LR={key:1,class:"col-md-3"},$R={class:"form-group"},FR={class:"col-md-4"},UR={class:"form-group"},BR={class:"row mb-n3"},qR={class:"col-md-12"},HR={class:"row"},WR={class:"col-md-4 col-lg-3"},jR={class:"form-group"},GR={class:"col-md-4 col-lg-3"},zR={class:"form-group"},KR={class:"col-md-4 col-lg-3"},XR={class:"form-group"},YR={class:"col-md-4 col-lg-3"},JR={class:"form-group"},QR={class:"row"},ZR={class:"col-md-3"},eP={class:"form-group"},tP={class:"col-md-3"},sP={class:"form-group"},rP={key:0,class:"col-md-3"},nP={key:1,class:"col-md-3 ml-md-n3 mt-n3 mt-md-0"},oP={class:"form-group"},iP={key:1,class:"row mb-n3"},aP={class:"col-md-12"},lP={key:2,class:"row"},uP={class:"col-md-4"},cP={class:"col-md-4"},dP={class:"col-md-4"},fP={key:3,class:"row mb-n3"},hP={class:"col-md-4"},pP={class:"form-group"},mP={key:4,class:"row"},gP={class:"col-md-4"},_P={class:"row"},vP={class:"col-md-4"},yP={class:"form-group mb-3"};function bP(e,t,s,i,n,a){const u=L("CustomLabel"),c=L("CustomInput"),f=L("CustomCheckbox"),g=L("TextEditor"),h=L("Autocomplete"),p=L("CustomSelect");return S(),N("div",null,[m("div",vR,[m("div",yR,[m("div",bR,[x(u,{required:"",text:"Nome da turma",help:"Insira um nome para a turma. Exemplo: Turma ADM 2025."}),x(c,{modelValue:n.localOfferClass.classname,"onUpdate:modelValue":t[0]||(t[0]=v=>n.localOfferClass.classname=v),placeholder:"Digite o nome da turma",required:"",id:"classname",disabled:s.isReadonly,"has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])])]),m("div",ER,[m("div",CR,[x(u,{required:"",text:"Data de início",help:"Insira uma data de início para a turma. Exemplo: 07/04/2025."}),x(c,{modelValue:n.localOfferClass.startdate,"onUpdate:modelValue":t[2]||(t[2]=v=>n.localOfferClass.startdate=v),type:"date",required:"",class:"date-input",id:"startdate",disabled:s.isReadonly,"has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])])]),m("div",wR,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenddate}])},[x(u,{text:"Data de término",help:"Insira uma data de término para a turma, caso haja. Exemplo: 07/12/2025."}),x(c,{modelValue:n.localOfferClass.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=v=>n.localOfferClass.optional_fields.enddate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enableenddate||s.isReadonly,required:"",id:"enddate",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)]),m("div",OR,[m("div",xR,[x(u,{text:" ",className:"d-none d-md-block"}),x(f,{modelValue:n.localOfferClass.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=v=>n.localOfferClass.optional_fields.enableenddate=v),id:"enableEndDate",label:"Habilitar data de termínio",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),m("div",SR,[m("div",IR,[m("div",DR,[x(u,{text:"Descrição da turma",help:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025"}),x(g,{modelValue:n.localOfferClass.optional_fields.description,"onUpdate:modelValue":[t[7]||(t[7]=v=>n.localOfferClass.optional_fields.description=v),t[8]||(t[8]=v=>a.validateForm())],placeholder:"Digite a descrição da turma aqui...",rows:4,disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),m("div",NR,[m("div",TR,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[x(u,{required:n.localOfferClass.optional_fields.enablepreenrolment,text:"Data de início da inscrição",help:"Data de início do período de inscrição."},null,8,["required"]),x(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[9]||(t[9]=v=>n.localOfferClass.optional_fields.preenrolmentstartdate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enablepreenrolment||s.isReadonly,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[10]||(t[10]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)]),m("div",AR,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enablepreenrolment}])},[x(u,{required:n.localOfferClass.optional_fields.enablepreenrolment,text:"Data de término da inscrição"},null,8,["required"]),x(c,{modelValue:n.localOfferClass.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[11]||(t[11]=v=>n.localOfferClass.optional_fields.preenrolmentenddate=v),type:"date",disabled:!n.localOfferClass.optional_fields.enablepreenrolment||s.isReadonly,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[12]||(t[12]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)]),m("div",RR,[m("div",PR,[x(u,{text:" ",className:"d-none d-md-block"}),x(f,{modelValue:n.localOfferClass.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[13]||(t[13]=v=>n.localOfferClass.optional_fields.enablepreenrolment=v),id:"enablePreEnrolment",label:"Habilitar a inscrição",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])]),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",MR,[n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",kR,[m("div",VR,[x(u,{text:"Número mínimo de inscrições",help:"Insira um número mínimo de inscrições (vagas) para a turma, se houver. Exemplo: 20."}),x(c,{modelValue:n.localOfferClass.optional_fields.minusers,"onUpdate:modelValue":t[14]||(t[14]=v=>n.localOfferClass.optional_fields.minusers=v),type:"number","has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[15]||(t[15]=v=>a.validateForm()),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])):te("",!0),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",LR,[m("div",$R,[x(u,{text:"Número máximo de inscrições",help:"Insira um número máximo de inscrições (vagas) para a turma. Exemplo: 100."}),x(c,{modelValue:n.localOfferClass.optional_fields.maxusers,"onUpdate:modelValue":t[16]||(t[16]=v=>n.localOfferClass.optional_fields.maxusers=v),type:"number","has-error":n.formErrors.maxusers.hasError,"error-message":n.formErrors.maxusers.message,onValidate:t[17]||(t[17]=v=>a.validateForm()),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])):te("",!0),m("div",FR,[m("div",UR,[x(u,{text:"Número máximo de inscrições por concessionária",help:"Insira um número máximo de inscrições por concessionária para a turma, se houver. Exemplo: 5."}),x(c,{modelValue:n.localOfferClass.optional_fields.maxusersdealership,"onUpdate:modelValue":t[18]||(t[18]=v=>n.localOfferClass.optional_fields.maxusersdealership=v),type:"number","has-error":n.formErrors.maxusersdealership.hasError,"error-message":n.formErrors.maxusersdealership.message,onValidate:t[19]||(t[19]=v=>a.validateForm()),min:0,disabled:s.isReadonly},null,8,["modelValue","has-error","error-message","disabled"])])])])):te("",!0),m("div",BR,[m("div",qR,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction,"dependent-field":!0}])},[x(f,{modelValue:n.localOfferClass.optional_fields.enablehirearchyrestriction,"onUpdate:modelValue":t[20]||(t[20]=v=>n.localOfferClass.optional_fields.enablehirearchyrestriction=v),id:"enablehirearchyrestriction",label:"Habilitar restrição por estruturas",help:"Ao habilitar esta opção, os campos 'Divisão', 'Setor', 'Grupo' e 'Concessionária' serão automaticamente ajustados conforme as seleções realizadas nos respectivos filtros.",disabled:s.isReadonly},null,8,["modelValue","disabled"])],2)])]),m("div",HR,[m("div",WR,[m("div",jR,[x(u,{text:"Divisão"}),x(h,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondivisions,"onUpdate:modelValue":[t[21]||(t[21]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiondivisions=v),t[22]||(t[22]=v=>a.validateForm())],items:n.divisionOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||s.isReadonly},null,8,["modelValue","items","disabled"])])]),m("div",GR,[m("div",zR,[x(u,{text:"Setor"}),x(h,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictionsectors,"onUpdate:modelValue":[t[23]||(t[23]=v=>n.localOfferClass.optional_fields.hirearchyrestrictionsectors=v),t[24]||(t[24]=v=>a.validateForm())],items:n.sectorOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiondivisions.length||s.isReadonly},null,8,["modelValue","items","disabled"])])]),m("div",KR,[m("div",XR,[x(u,{text:"Grupo"}),x(h,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiongroups,"onUpdate:modelValue":[t[25]||(t[25]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiongroups=v),t[26]||(t[26]=v=>a.validateForm())],items:n.groupOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictionsectors.length||s.isReadonly},null,8,["modelValue","items","disabled"])])]),m("div",YR,[m("div",JR,[x(u,{text:"Concessionária"}),x(h,{class:"autocomplete-audiences",modelValue:n.localOfferClass.optional_fields.hirearchyrestrictiondealerships,"onUpdate:modelValue":[t[27]||(t[27]=v=>n.localOfferClass.optional_fields.hirearchyrestrictiondealerships=v),t[28]||(t[28]=v=>a.validateForm())],items:n.dealershipOptions,placeholder:"Selecionar",required:!0,autoOpen:!1,"show-all-option":!0,disabled:!n.localOfferClass.optional_fields.enablehirearchyrestriction||!n.localOfferClass.optional_fields.hirearchyrestrictiongroups.length||s.isReadonly},null,8,["modelValue","items","disabled"])])])]),m("div",QR,[m("div",ZR,[m("div",eP,[x(u,{required:"",text:"Perfil atribuído por padrão",help:`Caso a turma seja criada para perfis de 'Aluno Nissan' e ‘Aluno Concessionária’, o perfil padrão selecionado será ‘Estudante’.<br><br>
                    Caso a turma seja criada para perfis de ‘Gestores’, o perfil padrão selecionado será ‘Gestor’ ou outro perfil pertinente.`}),x(p,{modelValue:n.localOfferClass.optional_fields.roleid,"onUpdate:modelValue":t[29]||(t[29]=v=>n.localOfferClass.optional_fields.roleid=v),options:n.roleOptions,required:"",disabled:s.isReadonly,"has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[30]||(t[30]=v=>a.validateForm())},null,8,["modelValue","options","disabled","has-error","error-message"])])]),m("div",tP,[m("div",sP,[x(u,{required:"",text:"Modalidade da turma",help:"Selecione a modalidade da turma."}),x(p,{modelValue:n.localOfferClass.optional_fields.modality,"onUpdate:modelValue":t[31]||(t[31]=v=>n.localOfferClass.optional_fields.modality=v),options:n.modalityOptions,required:"",disabled:s.isReadonly,"has-error":n.formErrors.modality.hasError,"error-message":n.formErrors.modality.message,onValidate:t[32]||(t[32]=v=>a.validateForm())},null,8,["modelValue","options","disabled","has-error","error-message"])])]),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",rP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly}])},[x(u,{required:n.localOfferClass.optional_fields.enableenrolperiod,id:"enrolperiod",text:"Duração da matrícula",help:"Insira um período em dias para a duração da matricula dos alunos, se houver, na turma. Exemplo: 15 <br><br>"+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["required","help"]),x(c,{modelValue:n.localOfferClass.optional_fields.enrolperiod,"onUpdate:modelValue":t[33]||(t[33]=v=>n.localOfferClass.optional_fields.enrolperiod=v),type:"number",id:"enrolperiod",disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[34]||(t[34]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message","max"])],2)])):te("",!0),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",nP,[m("div",oP,[x(u,{text:" ",className:"d-none d-md-block"}),x(f,{modelValue:n.localOfferClass.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[35]||(t[35]=v=>n.localOfferClass.optional_fields.enableenrolperiod=v),id:"enableEnrolPeriod",label:"Habilitar duração da matrícula",disabled:a.shouldDisableEnrolPeriod||s.isReadonly,title:a.shouldDisableEnrolPeriod?"Não é possível habilitar duração da matrícula para turmas de um dia (data início = data fim)":""},null,8,["modelValue","disabled","title"])])])):te("",!0)]),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",iP,[m("div",aP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[x(f,{modelValue:n.localOfferClass.optional_fields.enableextension,"onUpdate:modelValue":t[36]||(t[36]=v=>n.localOfferClass.optional_fields.enableextension=v),id:"enableextension",label:"Habilitar prorrogação de matrícula",disabled:!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,help:"A prorrogação estende a duração da matrícula do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou.<br><br> Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado.",title:n.localOfferClass.optional_fields.enableenrolperiod?"":"É necessário habilitar o duração da matrícula primeiro",onValidate:t[37]||(t[37]=v=>a.validateForm())},null,8,["modelValue","disabled","title"])],2)])])):te("",!0),n.localOfferClass.enrol!=="offer_audience"?(S(),N("div",lP,[m("div",uP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[x(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Dias adicionais na matrícula",help:"Insira um período em dias para prorrogar a matricula dos alunos da turma. Exemplo: 3."},null,8,["required"]),x(c,{modelValue:n.localOfferClass.optional_fields.extensionperiod,"onUpdate:modelValue":t[38]||(t[38]=v=>n.localOfferClass.optional_fields.extensionperiod=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[39]||(t[39]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)]),m("div",cP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[x(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Dias antes do fim para exibir o botão de prorrogação"},null,8,["required"]),x(c,{modelValue:n.localOfferClass.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[40]||(t[40]=v=>n.localOfferClass.optional_fields.extensiondaysavailable=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[41]||(t[41]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)]),m("div",dP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod,"dependent-field":!0}])},[x(u,{required:n.localOfferClass.optional_fields.enableextension,text:"Quantidade máxima de prorrogações permitidas"},null,8,["required"]),x(c,{modelValue:n.localOfferClass.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[42]||(t[42]=v=>n.localOfferClass.optional_fields.extensionmaxrequests=v),type:"number",disabled:!n.localOfferClass.optional_fields.enableextension||!n.localOfferClass.optional_fields.enableenrolperiod||s.isReadonly,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[43]||(t[43]=v=>a.validateForm())},null,8,["modelValue","disabled","has-error","error-message"])],2)])])):te("",!0),n.localOfferClass.enrol!=="offer_dealership"?(S(),N("div",fP,[m("div",hP,[m("div",pP,[x(f,{modelValue:n.localOfferClass.optional_fields.enablereenrol,"onUpdate:modelValue":t[44]||(t[44]=v=>n.localOfferClass.optional_fields.enablereenrol=v),id:"enableReenrol",label:"Habilitar rematrícula",text:"Habilitar rematrícula",help:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela.",disabled:s.isReadonly},null,8,["modelValue","disabled"])])])])):te("",!0),n.localOfferClass.enrol!=="offer_dealership"?(S(),N("div",mP,[m("div",gP,[m("div",{class:me(["form-group",{disabled:!n.localOfferClass.optional_fields.enablereenrol}])},[x(u,{text:"Status que permite rematrículas",required:n.localOfferClass.optional_fields.enablereenrol},null,8,["required"]),x(h,{modelValue:n.localOfferClass.optional_fields.reenrolmentsituations,"onUpdate:modelValue":[t[45]||(t[45]=v=>n.localOfferClass.optional_fields.reenrolmentsituations=v),t[46]||(t[46]=v=>a.validateForm())],items:n.situationOptions,placeholder:"Selecione o status...",required:!0,disabled:!n.localOfferClass.optional_fields.enablereenrol||s.isReadonly,"auto-open":!1,"has-error":n.formErrors.reenrolmentsituations.hasError,"error-message":n.formErrors.reenrolmentsituations.message},null,8,["modelValue","items","disabled","has-error","error-message"])],2)])])):te("",!0),m("div",_P,[m("div",vP,[m("div",yP,[x(u,{text:"Atribuir Instrutor",help:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."}),x(h,{class:"autocomplete-audiences",modelValue:n.localOfferClass.teachers,"onUpdate:modelValue":[t[47]||(t[47]=v=>n.localOfferClass.teachers=v),t[48]||(t[48]=v=>a.validateForm())],items:n.teacherOptions,"has-search-icon":!0,placeholder:"Pesquisar...",autoOpen:!1,"show-all-option":!1,loading:n.loadingTeachers,disabled:s.isReadonly,onSearch:t[49]||(t[49]=v=>e.debouncedSearchTeachers(v))},null,8,["modelValue","items","loading","disabled"])])])])])}const EP=Le(_R,[["render",bP],["__scopeId","data-v-48ce2996"]]),UU="",CP={name:"CreateEdit",mixins:[rr],components:{Form:EP,Toast:sr,LFLoading:Zo,PageHeader:Xn,BackButton:ti,CustomButton:hs,Autocomplete:Or,ConfirmationModal:Jn},props:{isReadonly:{type:Boolean,default:!1}},data(){return{saving:!1,loading:!1,originalClassName:"",offerClass:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,modality:null,maxusersdealership:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,enablehirearchyrestriction:!1,hirearchyrestrictiondivisions:[],hirearchyrestrictionsectors:[],hirearchyrestrictiongroups:[],hirearchyrestrictiondealerships:[]},creatorname:"",createddate:"",modifieddate:"",modifiername:""},offerCourse:null,isValidForm:!1,showRequestSaveModal:!1}},async created(){this.isEditing&&await this.getOfferClass(),this.offerCourseId&&(await this.getOfferCourse(),this.offerClass.offercourseid=parseInt(this.offerCourseId))},computed:{isEditing(){return!!this.offerClassId},offerClassId(){return parseInt(this.$route.params.offerClassId)},offerCourseId(){var e;return this.$route.query.offerCourseId||((e=this.offerClass)==null?void 0:e.offercourseid)}},methods:{async getOfferClass(){try{this.loading=!0;const e=await Mp(parseInt(this.offerClassId));this.offerClass=e,this.offerClass.teachers=this.offerClass.teachers.map(t=>({value:t.id,label:t.fullname})),e.optional_fields&&this.processOptionalFields(e.optional_fields),this.originalClassName=this.offerClass.classname}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async getOfferCourse(){try{this.loading=!0;const e=await hC(this.offerCourseId);this.offerCourse=e}catch(e){this.showErrorMessage(e)}finally{this.loading=!1}},async createClass(){try{if(this.showRequestSaveModal=!1,!this.isValidForm)return;this.saving=!0;const e=this.prepareClassData(!1);if(!e)return;let t=await fC(e);this.showSuccessMessage(t.message),this.originalClassName=this.offerClass.classname,this.$router.push({name:"offer.class.edit",params:{offerClassId:t.offerclassid}})}catch(e){this.showErrorMessage(e)}finally{this.saving=!1}},async updateClass(){try{if(this.showRequestSaveModal=!1,!this.isValidForm)return;this.saving=!0;const e=this.prepareClassData(!0);if(!e)return;e.offerclassid=this.offerClassId;let t=await mC(e);this.showSuccessMessage(t.message),this.isValidForm=!1,this.getOfferClass()}catch(e){console.log(e),this.showErrorMessage(e)}finally{this.saving=!1}},prepareClassData(){const e=tr.cloneDeep(this.offerClass);e.offercourseid=parseInt(this.offerCourseId),e.teachers=e.teachers.map(i=>i.value),e.optional_fields.enableenddate||(e.optional_fields.enddate=null),e.optional_fields.enablepreenrolment||(e.optional_fields.preenrolmentstartdate=null,e.optional_fields.preenrolmentenddate=null),(!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod)&&(e.optional_fields.extensionperiod=null,e.optional_fields.extensiondaysavailable=null,e.optional_fields.extensionmaxrequests=null,e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=null),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=e.optional_fields.reenrolmentsituations.map(i=>i.value):e.optional_fields.reenrolmentsituations=[],e.optional_fields.enablehirearchyrestriction?(e.optional_fields.hirearchyrestrictiondivisions=this.mapToValues(e.optional_fields.hirearchyrestrictiondivisions),e.optional_fields.hirearchyrestrictionsectors=this.mapToValues(e.optional_fields.hirearchyrestrictionsectors),e.optional_fields.hirearchyrestrictiongroups=this.mapToValues(e.optional_fields.hirearchyrestrictiongroups),e.optional_fields.hirearchyrestrictiondealerships=this.mapToValues(e.optional_fields.hirearchyrestrictiondealerships)):(e.optional_fields.hirearchyrestrictiondivisions=[],e.optional_fields.hirearchyrestrictionsectors=[],e.optional_fields.hirearchyrestrictiongroups=[],e.optional_fields.hirearchyrestrictiondealerships=[]),this.isEditing&&"enrol"in e&&delete e.enrol,delete e.createddate,delete e.creatorname,delete e.modifieddate,delete e.modifiername;const s=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(i=>!e[i]);return s.length>0?(this.showErrorMessage(`Campos obrigatórios ausentes: ${s.join(", ")}`),null):e},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processHirarchyRestrictionFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processHirarchyRestrictionFields(e){this.offerClass.optional_fields.hirearchyrestrictiondivisions=this.mapToOptions(e.hirearchyrestrictiondivisions),this.offerClass.optional_fields.hirearchyrestrictionsectors=this.mapToOptions(e.hirearchyrestrictionsectors),this.offerClass.optional_fields.hirearchyrestrictiongroups=this.mapToOptions(e.hirearchyrestrictiongroups),this.offerClass.optional_fields.hirearchyrestrictiondealerships=this.mapToOptions(e.hirearchyrestrictiondealerships)},processDateFields(e){e.enableenddate&&(this.offerClass.optional_fields.enableenddate=!0,this.offerClass.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.offerClass.optional_fields.enablepreenrolment=!0,this.offerClass.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.offerClass.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.offerClass.optional_fields.enableenrolperiod=!0,this.offerClass.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.offerClass.optional_fields.enrolperiod=null},processUserLimits(e){this.offerClass.optional_fields.minusers=e.minusers>0?e.minusers:null,this.offerClass.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.offerClass.optional_fields.roleid=e.roleid||null,this.offerClass.optional_fields.description=e.description||"",this.offerClass.optional_fields.modality=e.modality||null},processReenrolment(e){e.enablereenrol?(this.offerClass.optional_fields.enablereenrol=!0,this.offerClass.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.offerClass.optional_fields.reenrolmentsituations=e.reenrolmentsituations.map(t=>({label:"",value:t})))):this.offerClass.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.offerClass.optional_fields.enableextension=!0,this.processExtensionPeriods(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.offerClass.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.offerClass.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.offerClass.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},mapToOptions(e){return e.map(t=>({value:t,label:""}))},mapToValues(e){return e.map(t=>t.value)},resetExtensionFields(){this.offerClass.optional_fields.extensionperiod=null,this.offerClass.optional_fields.extensiondaysavailable=null,this.offerClass.optional_fields.extensionmaxrequests=null,this.extensionSituations=[]},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},navigateToBack(){this.offerCourse.offerid?this.$router.push({name:"offer.edit",params:{id:this.offerCourse.offerid}}):this.router.push({name:"offer.index"})}}},wP={class:"page-header-container"},OP={class:"d-flex flex-column mt-3"},xP={key:0,class:"text-muted"},SP={key:1,class:"text-muted"},IP={class:"actions-container"};function DP(e,t,s,i,n,a){const u=L("BackButton"),c=L("PageHeader"),f=L("Form"),g=L("CustomButton"),h=L("ConfirmationModal"),p=L("LFLoading"),v=L("Toast");return S(),N("div",{id:"create-edit-class",class:me({"edit-class":a.isEditing&&!s.isReadonly,"view-class":s.isReadonly,"create-class":!a.isEditing})},[m("div",wP,[x(c,{title:a.isEditing?s.isReadonly?"Visualizar turma":`Editar turma: ${n.originalClassName??""}`:"Adicionar turma"},{actions:Re(()=>[x(u,{onClick:a.navigateToBack},null,8,["onClick"])]),_:1},8,["title"])]),t[5]||(t[5]=m("div",{class:"section-container"},[m("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS")],-1)),n.offerCourse?(S(),ct(f,{key:0,ref:"form",offerClass:n.offerClass,"onUpdate:offerClass":t[0]||(t[0]=C=>n.offerClass=C),offerCourse:n.offerCourse,isEditing:a.isEditing,isReadonly:s.isReadonly,onValidate:t[1]||(t[1]=C=>n.isValidForm=C)},null,8,["offerClass","offerCourse","isEditing","isReadonly"])):te("",!0),m("div",OP,[n.offerClass.creatorname?(S(),N("small",xP," Criado por "+ne(n.offerClass.creatorname)+" em "+ne(n.offerClass.createddate),1)):te("",!0),n.offerClass.modifiername?(S(),N("small",SP," Atualizado por "+ne(n.offerClass.modifiername)+" em "+ne(n.offerClass.modifieddate),1)):te("",!0)]),m("div",IP,[x(g,{variant:"primary",label:n.saving?"Salvando...":"Salvar",isLoading:n.saving,disabled:!n.isValidForm||s.isReadonly,onClick:t[2]||(t[2]=C=>n.showRequestSaveModal=!0)},null,8,["label","isLoading","disabled"]),x(g,{variant:"secondary",label:"Cancelar",onClick:a.navigateToBack},null,8,["onClick"])]),t[6]||(t[6]=m("div",{class:"required-fields-message"},[m("hr"),m("div",{class:"form-info"},[Ze(" Este formulário contém campos obrigatórios marcados com "),m("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),x(h,{show:n.showRequestSaveModal,size:"md",title:"Você tem certeza que deseja salvar as alterações feitas?","confirm-button-text":"Salvar","cancel-button-text":"Cancelar",onClose:t[3]||(t[3]=C=>n.showRequestSaveModal=!1),onConfirm:t[4]||(t[4]=C=>a.isEditing?a.updateClass():a.createClass())},null,8,["show"]),x(p,{"is-loading":n.loading},null,8,["is-loading"]),x(v,{show:e.showToast,message:e.toastMessage,type:e.toastType,duration:3e3},null,8,["show","message","type"])],2)}const Zu=Le(CP,[["render",DP],["__scopeId","data-v-45dddaa9"]]),NP="/local/offermanager/",TP=(()=>{const e=window.location.host,t=window.location.pathname,s=G_.wwwroot.replace(/^https?\:\/\//i,"").replace(e,"").concat(NP);return t.includes("index.php")?s+"index.php":s})(),AP=[{path:"/",name:"offer.index",component:Iw,meta:{title:"Gerenciar Ofertas"}},{path:"/offers/create",name:"offer.create",component:$u,meta:{title:"Nova Oferta"}},{path:"/offers/:id/edit",name:"offer.edit",component:$u,props:!0,meta:{title:"Editar Oferta"}},{path:"/offers/:id",name:"offer.show",component:$u,props:{isReadonly:!0},meta:{title:"Visualizar Oferta"}},{path:"/offers/classes/create",name:"offer.class.create",component:Zu,props:!0,meta:{title:"Nova Turma"}},{path:"/offers/classes/:offerClassId/edit",name:"offer.class.edit",component:Zu,props:!0,meta:{title:"Editar Turma"}},{path:"/offers/classes/:offerClassId",name:"offer.class.show",component:Zu,props:{isReadonly:!0},meta:{title:"Visualizar Turma"}},{path:"/offers/classes/:offerClassId/enrollments",name:"offer.class.enrollments",component:FI,props:!0,meta:{title:"Usuários matriculados"}},{path:"/offers/classes/:offerClassId/waiting-list",name:"offer.class.waiting-list",component:qI,props:!0,meta:{title:"Lista de espera"}},{path:"/offers/classes/:offerClassId/revoked-enrollments",name:"offer.class.revoked-enrollments",component:jI,props:!0,meta:{title:"Inscrições revogadas"}},{path:"/:pathMatch(.*)*",redirect:"/"}],Sa=Z0({history:c0(TP),routes:AP,scrollBehavior(){return{top:0}}});Sa.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),Sa.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&Sa.push("/")});const BU="",RP=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{RP();const s=Qb(ME);return s.use(TE()),s.use(Sa),s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
