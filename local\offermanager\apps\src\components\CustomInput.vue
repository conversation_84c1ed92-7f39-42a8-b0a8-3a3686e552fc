<template>
  <div class="custom-input-container" :style="customWidth">
    <CustomLabel v-if="label" :text="label" />
    <div
      class="input-wrapper"
      :class="{ 'with-icon': hasSearchIcon || isDateType }"
    >
      <input
        :type="type"
        :placeholder="placeholder"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        :disabled="disabled"
        class="form-control"
        :class="{ 'is-invalid': hasError }"
        :min="isNumberType ? 0 : null"
        :max="max"
        :id="id"
      />
      <div v-if="hasSearchIcon" class="search-icon">
        <i class="fas fa-search"></i>
      </div>
      <div
        v-if="isDateType"
        class="calendar-icon"
        :class="{ disabled: disabled }"
      >
        <i class="fas fa-calendar-alt"></i>
      </div>
      <div
        v-if="hasError && errorMessage"
        class="form-control-feedback invalid-feedback d-block"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import CustomLabel from "@/components/CustomLabel.vue";

export default {
  name: "CustomInput",

  components: {
    CustomLabel,
  },

  props: {
    modelValue: {
      type: [String, Number],
      default: "",
    },
    id: {
      type: String,
      required: false,
      default: "custom-input-" + Math.random().toString(36).substring(2, 9),
    },
    label: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "Digite aqui...",
    },
    type: {
      type: String,
      default: "text",
    },
    hasSearchIcon: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hasError: {
      type: Boolean,
      default: false,
    },
    errorMessage: {
      type: String,
      default: "",
    },
    required: {
      type: Boolean,
      default: false,
    },
    max: {
      type: [String, Number],
      default: null,
    },
  },

  computed: {
    customWidth() {
      return this.width
        ? {
            width:
              typeof this.width === "number" ? `${this.width}px` : this.width,
          }
        : {};
    },
    isDateType() {
      return this.type === "date";
    },
    isNumberType() {
      return this.type === "number";
    },
  },

  methods: {
    handleInput(event) {
      let value = event.target.value;

      // Para campos numéricos, impedir valores negativos e validar valor máximo
      if (this.isNumberType) {
        // Remover qualquer sinal negativo do valor
        if (value.includes("-")) {
          value = value.replace(/-/g, "");
          event.target.value = value;
        }

        // Se o valor não estiver vazio, garantir que seja positivo e não exceda o máximo
        if (value !== "") {
          // Converter para número e verificar se é negativo
          const numValue = parseFloat(value);
          if (numValue < 0 || isNaN(numValue)) {
            // Se for negativo ou não for um número válido, definir como vazio
            value = "";
            event.target.value = value;
          }
          // Verificar se excede o valor máximo
          else if (this.max !== null && numValue > parseFloat(this.max)) {
            // Se exceder o valor máximo, definir como o valor máximo
            value = this.max.toString();
            event.target.value = value;
            // Emitir evento de validação para atualizar mensagens de erro
            this.$emit("validate");
          }
        }
      }

      this.$emit("update:modelValue", value);

      // Se o campo estava com erro e agora tem valor, emitir evento de validação
      if (this.hasError && value) {
        this.$emit("validate");
      }
    },

    handleBlur(event) {
      this.$emit("validate");
    },
  },

  emits: ["update:modelValue", "validate"],
};
</script>

<style lang="scss" scoped>
.custom-input-container {
  width: 100%;
  position: relative;

  @media (max-width: 768px) {
    width: 100% !important;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  flex: 1;

  .calendar-icon {
    position: absolute;
    right: 10px;
    top: 19px;
    transform: translateY(-50%);
    color: #fff;
    pointer-events: none;

    &.disabled {
      opacity: 0.65;
    }
  }

  .search-icon {
    position: absolute;
    right: 10px;
    top: 19px;
    transform: translateY(-50%);
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
  }

  .form-control {
    &.is-invalid ~ .search-icon,
    &[type="date"].is-invalid ~ .calendar-icon {
      right: 40px;
    }
  }

  input[type="date"] {
    color-scheme: dark;

    /* Esconde o ícone nativo do calendário */
    &::-webkit-calendar-picker-indicator {
      opacity: 0;
    }
  }

  .calendar-icon {
    color: #fff;
    z-index: 1;
  }

  /* Chrome, Safari, Edge, Opera */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
