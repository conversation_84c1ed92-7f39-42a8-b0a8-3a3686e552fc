<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent\trait;

use core_date;
use local_offermanager\persistent\offer_course_model;
use local_offermanager\event\offer_class_duplicated;
use local_offermanager\persistent\offer_class_model;
use moodle_exception;
use stdClass;
use Exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Trait class_duplication_trait
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
trait class_duplication_trait
{
    /**
     * Duplica a instância atual da turma para um novo curso dentro da mesma oferta.
     *
     * @param offer_course_model $target_offer_course A instância de offer_course_model do curso de destino.
     * @return offer_class_model A nova instância de offer_class_model criada.
     * @throws moodle_exception Se as regras de duplicação não forem atendidas ou ocorrer um erro.
     */
    public function duplicate(offer_course_model $target_offer_course): offer_class_model
    {
        global $DB, $USER;

        $original_offer_course = $this->get_offer_course();
        $original_offer = $original_offer_course->get_offer();
        $target_offer = $target_offer_course->get_offer();

        if ($original_offer->get('id') !== $target_offer->get('id')) {
            throw new moodle_exception('error:duplicate_different_offer', 'local_offermanager');
        }

        $original_course = $this->get_course();
        $target_course = $target_offer_course->get_course();

        // if ($original_course->id === $target_course->id) {
        //     throw new moodle_exception('error:duplicate_same_course', 'local_offermanager');
        // }

        $plugin = $this->get_plugin();

        $original_extensiondata = $this->get_extension_data();
        $original_hirearchyrestriction = $this->get_hirearchy_restriction_data();

        $transaction = $DB->start_delegated_transaction();

        $timezone = core_date::get_user_timezone($USER);
        $new_start_date = make_timestamp(date('Y'), date('m'), date('d'), 0, 0, 0, $timezone);

        try {
            $fields = [
                'offercourseid' => $target_offer_course->get('id'),
                'classname' => $this->get_mapped_field('classname') . " - Cópia",
                'startdate' => NULL,
                'enableenddate' => false,
                'enddate' => NULL,
                'enablepreenrolment' => false,
                'preenrolmentstartdate' => NULL,
                'preenrolmentenddate' => NULL,
                'description' => $this->get_mapped_field('description') ?? '',
                'enableenrolperiod' => $this->get_mapped_field('enableenrolperiod'),
                'enrolperiod' => $this->get_mapped_field('enrolperiod'),
                'minusers' => $this->get_mapped_field('minusers'),
                'maxusers' => $this->get_mapped_field('maxusers'),
                'roleid' => $this->get_mapped_field('roleid'),
                'enablereenrol' => $this->get_mapped_field('enablereenrol'),
                'reenrolmentsituations' => $this->get_mapped_field('reenrolmentsituations') ? explode(', ', $this->get_mapped_field('reenrolmentsituations')) : [],
                'enableextension' => $this->get_mapped_field('enableextension'),
                'extensionperiod' => $original_extensiondata->period,
                'extensiondaysavailable' => $original_extensiondata->days_available,
                'extensionmaxrequests' => $original_extensiondata->max_requests,
                'enablehirearchyrestriction' => $this->get_mapped_field('enablehirearchyrestriction'),
                'hirearchyrestrictiondivisions' => $original_hirearchyrestriction->divisions ?? [],
                'hirearchyrestrictionsectors' => $original_hirearchyrestriction->sectors ?? [],
                'hirearchyrestrictiongroups' => $original_hirearchyrestriction->groups ?? [],
                'hirearchyrestrictiondealerships' => $original_hirearchyrestriction->dealerships ?? [],
                'teachers' => [],
                'modality' => $this->get_mapped_field('modality') ?? '',
                'maxusersdealership' => $this->get_mapped_field('maxusersdealership')
            ];

            $new_enrolid = $plugin->add_instance($target_course, $fields, true);

            $new_offer_class = offer_class_model::get_by_enrolid($new_enrolid);

            $teacherids_string = $this->get_mapped_field('teachers');

            if ($teacherids_string) {
                $teacherids = explode(', ', $teacherids_string);

                $return = $new_offer_class->update_teachers($teacherids);

                if (!$return) {
                    throw new moodle_exception('error:update_teacher_error', 'local_offermanager');
                }
            }

            $event = offer_class_duplicated::create_from_instances($this, $new_offer_class);
            $event->trigger();

            $transaction->allow_commit();

            $new_offer_class->set('clone_id', $this->get('id'));
            $new_offer_class->save();

            $new_offer_class->fetch_enrol_instance();

            return $new_offer_class;
        } catch (Exception $e) {
            $transaction->rollback($e);
            throw $e;
        }
    }

    /**
     * Retorna os cursos potenciais para duplicação da turma.
     *
     * @return array Lista de curso (stdClass) com pelo menos id, fullname e informações da categoria
     * @throws moodle_exception
     */
    public function get_potential_duplication_courses(): array
    {
        $offercourse = $this->get_offer_course();
        $offer = $offercourse->get_offer();
        $allcourses = $offer->get_courses(true, [], 0, 0, 'fullname', 'ASC');

        $potential = [];
        foreach ($allcourses as $offercourseitem) {
            $course = $offercourseitem->get_course();
            $course_category = $offercourseitem->get_course_category();

            $obj = new stdClass;
            $obj->id = $offercourseitem->get('id');
            $obj->fullname = $course->fullname;
            $obj->categoryid = $course_category->id;
            $obj->category_name = $course_category->name;
            $potential[] = $obj;
        }

        return $potential;
    }
}
