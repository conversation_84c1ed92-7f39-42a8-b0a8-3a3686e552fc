<template>
  <div id="enrollments-view">
    <PageHeader title="Usuários inscritos">
      <template #actions>
        <BackButton @click="navigateToBack" />
      </template>
    </PageHeader>

    <PageTabs />

    <!-- Filtros -->
    <div class="filters-section mb-3">
      <div class="row">
        <div class="col-xl-8">
          <div class="row">
            <!-- Filtro por Nome -->
            <div class="col-md-4">
              <div class="filter-input-container position-relative">
                <CustomLabel text="Filtrar por nome" />
                <input
                  id="name-filter"
                  type="text"
                  class="form-control"
                  placeholder="Buscar..."
                  v-model="nameSearchInput"
                  @input="handleNameInput"
                  @focus="showNameDropdown = nameOptions.length > 0"
                  @blur="clearOptions('name')"
                />
                <!-- Dropdown de opções de nome -->
                <div
                  v-if="showNameDropdown && nameOptions.length > 0"
                  class="dropdown-menu show position-absolute w-100"
                >
                  <button
                    v-for="option in nameOptions"
                    :key="option.id"
                    type="button"
                    class="dropdown-item"
                    @click="selectNameOption(option)"
                  >
                    {{ option.label }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Filtro por CPF -->
            <div class="col-md-4 mt-3 mt-md-0">
              <div class="filter-input-container position-relative">
                <CustomLabel text="Filtrar por CPF" />
                <input
                  id="cpf-filter"
                  type="text"
                  class="form-control"
                  placeholder="Buscar..."
                  v-model="cpfSearchInput"
                  @input="handleCpfInput"
                  @focus="showCpfDropdown = cpfOptions.length > 0"
                  @blur="clearOptions('cpf')"
                />
                <!-- Dropdown de opções de CPF -->
                <div
                  v-if="showCpfDropdown && cpfOptions.length > 0"
                  class="dropdown-menu show position-absolute w-100"
                >
                  <button
                    v-for="option in cpfOptions"
                    :key="option.id"
                    type="button"
                    class="dropdown-item"
                    @click="selectCpfOption(option)"
                  >
                    {{ option.label }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Filtro por E-mail -->
            <div class="col-md-4 mt-3 mt-md-0">
              <div class="filter-input-container position-relative">
                <CustomLabel text="Filtrar por e-mail" />
                <input
                  id="email-filter"
                  type="text"
                  class="form-control"
                  placeholder="Buscar..."
                  v-model="emailSearchInput"
                  @input="handleEmailInput"
                  @focus="showEmailDropdown = emailOptions.length > 0"
                  @blur="clearOptions('email')"
                />
                <!-- Dropdown de opções de email -->
                <div
                  v-if="showEmailDropdown && emailOptions.length > 0"
                  class="dropdown-menu show position-absolute w-100"
                >
                  <button
                    v-for="option in emailOptions"
                    :key="option.id"
                    type="button"
                    class="dropdown-item"
                    @click="selectEmailOption(option)"
                  >
                    {{ option.label }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-4">
          <CustomLabel text="&nbsp" />
          <div
            class="d-flex justify-content-between justify-content-md-end mb-5 mb-xl-0"
            style="gap: 12px"
          >
            <CustomButton
              v-if="!offerClass || offerClass?.operational_cycle !== 2"
              variant="primary"
              label="Inscrever usuários"
              @click="addNewUser"
            />

            <CustomButton
              v-if="!offerClass || offerClass?.operational_cycle !== 2"
              variant="secondary"
              label="Inscrição por turmas"
              @click="addNewUser"
            />
          </div>
        </div>
      </div>
    </div>

    <FilterTags>
      <!-- Tags para usuários selecionados no filtro -->
      <FilterTag
        v-for="user in filteredUsers"
        :key="user.id"
        @remove="removeFilter(user.id || user.value)"
      >
        {{ user.label }}
      </FilterTag>
    </FilterTags>

    <!-- Botão Limpar Filtros -->
    <div v-if="filteredUsers.length > 0" class="my-4">
      <button
        type="button"
        class="btn btn-secondary"
        @click="clearFilteredUsers"
      >
        Limpar
      </button>
    </div>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <div class="my-3">{{ totalEnrolmentsInfo }}</div>

    <CustomTable
      :headers="tableHeaders"
      :items="enrolments"
      tableClass="table-hover"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @sort="handleTableSort"
    >
      <template #empty-state>
        <div class="empty-state">
          <span class="no-results">
            {{
              loading
                ? "Carregando registros..."
                : "Nenhum registro encontrado..."
            }}
          </span>
        </div>
      </template>
      <template #header-select>
        <div class="checkbox-container">
          <input
            type="checkbox"
            :checked="allSelected"
            :indeterminate="someSelected && !allSelected"
            @change="toggleSelectAll"
            class="custom-checkbox"
          />
        </div>
      </template>
      <template #item-select="{ item }">
        <div class="checkbox-container">
          <input
            type="checkbox"
            :checked="isSelected(item.id)"
            @change="toggleSelectUser(item.id)"
            class="custom-checkbox"
          />
          <CustomCheckbox
            v-model="selectedUsers.includes(item.id)"
            @update:modelValue="toggleSelectUser(item.id)"
          />
        </div>
      </template>
      <template #item-fullName="{ item }">
        <a
          class="user-name-container"
          :href="`/user/view.php?id=${item.id}`"
          :title="'Ver perfil de ' + item.fullName"
        >
          <UserAvatar :full-name="item.fullName" :size="36" />
          <span class="user-name-link">{{ item.fullName }}</span>
        </a>
      </template>
      <template #item-email="{ item }">
        {{ item.email }}
      </template>
      <template #item-cpf="{ item }">
        {{ item.cpf }}
      </template>
      <template #item-roles="{ item }">
        <span @click="showEditRoles(item)">
          {{ item.rolesFormatted }}
          <i class="fas fa-pencil-alt edit-icon ml-1" aria-hidden="true"></i>
        </span>
      </template>
      <template #item-groups="{ item }">
        {{ item.groups }}
      </template>
      <template #item-timeStartFormatted="{ item }">
        {{ item.timeStartFormatted }}
      </template>
      <template #item-timeEndFormatted="{ item }">
        {{ item.timeEndFormatted }}
      </template>
      <template #item-deadline="{ item }">
        {{ item.deadline }}
      </template>
      <template #item-progressFormatted="{ item }">
        <div class="progress-container">
          <div
            class="progress-bar"
            :style="{ width: item.progressFormatted }"
          ></div>
          <span class="progress-text">{{ item.progressFormatted }}</span>
        </div>
      </template>
      <template #item-situation="{ item }">
        {{ item.situationName }}
      </template>
      <template #item-grade="{ item }">
        {{ item.grade }}
      </template>
      <template #item-status="{ item }">
        <span
          class="badge"
          :class="item.status === 0 ? 'badge-success' : 'badge-danger'"
        >
          {{ item.statusName }}
        </span>
      </template>
      <template #item-actions="{ item }">
        <div class="action-buttons">
          <button
            class="btn-action btn-information"
            @click="showEnrollmentDetails(item)"
            title="Informações da matrícula"
          >
            <CircleInfoIcon class="" />
          </button>
          <button
            class="btn-action btn-settings"
            @click="editUser(item)"
            title="Editar matrícula"
          >
            <GearIcon class="" />
          </button>
          <button
            class="btn-action btn-delete"
            @click="editUser(item)"
            title="Excluir matrícula"
          >
            <i class="fa fa-trash fa-fw"></i>
          </button>
        </div>
      </template>
    </CustomTable>

    <!-- Paginação -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalEnrolments"
      :loading="loading"
    />

    <!-- Ações para usuários selecionados - sempre visível -->
    <div class="selected-users-actions">
      <div class="bulk-actions-container">
        <label for="bulk-actions">Com usuários selecionados...</label>
        <select
          id="bulk-actions"
          class="form-control bulk-select"
          v-model="selectedBulkAction"
          @change="handleBulkAction"
        >
          <option value="">Escolher...</option>
          <optgroup label="Comunicação">
            <option value="message">Enviar uma mensagem</option>
            <option value="note">Escrever uma nova anotação</option>
          </optgroup>
          <optgroup label="Baixar dados da tabela como:">
            <option value="download_csv">
              Valores separados por vírgula (.csv)
            </option>
            <option value="download_xlsx">Microsoft excel (.xlsx)</option>
            <option value="download_html">Tabela HTML</option>
            <option value="download_json">
              JavaScript Object Notation (.json)
            </option>
            <option value="download_ods">OpenDocument (.ods)</option>
            <option value="download_pdf">
              Formato de documento portável (.pdf)
            </option>
          </optgroup>
          <optgroup label="Inscrições">
            <option value="edit_enrolment">
              Editar matrículas de usuários selecionados
            </option>
            <option value="delete_enrolment">
              Excluir matrículas de usuários selecionados
            </option>
          </optgroup>
        </select>
      </div>
    </div>

    <!-- Modal de Detalhes da Matrícula -->
    <EnrollmentDetailsModal
      :show="showEnrollmentModal"
      :user="selectedEnrollment"
      :course-name="offerClass?.course_fullname || ''"
      @close="closeEnrollmentModal"
    />

    <!-- Modal de Matrícula de Usuários -->
    <AddEnrolmentModal
      v-if="showAddEnrolmentModal"
      :offerClass="offerClass"
      :currentUserIds="currentEnrolmentUserIds"
      @close="closeEnrolmentModal"
      @success="getRegisteredUsers"
    />

    <EditRolesModal
      v-if="showEditRolesModal"
      :userId="selectedEnrollment.id"
      :offerUserEnrolId="selectedEnrollment.offerUserEnrolId"
      :currentUserRoles="selectedEnrollment.roles"
      :courseRoles="roleOptions"
      @close="showEditRolesModal = false"
      @success="handleRoleUpdateSuccess"
    />

    <EditEnrollmentModal
      v-if="showEditEnrollmentModal"
      :enrollment="selectedEnrollment"
      @close="closeEditEnrollmentModal"
      @success="handleEditEnrollmentSuccess"
    />

    <!-- Modal de Edição de Matrícula -->
    <BulkEditEnrollmentModal
      :show="showBulkEditEnrollmentModal"
      :users="
        selectedUsers
          .map((id) => enrolments.find((offer) => offer.id === id))
          .filter(Boolean)
      "
      @close="this.showBulkEditEnrollmentModal = false"
      @success="handleBulkEditEnrollmentSuccess"
      @error="handleBulkEditEnrollmentError"
    />

    <BulkDeleteEnrollmentModal
      :show="showBulkDeleteEnrollmentModal"
      :users="
        selectedUsers
          .map((id) => enrolments.find((offer) => offer.id === id))
          .filter(Boolean)
      "
      @close="showBulkDeleteEnrollmentModal = false"
      @confirm="confirmeBulkDeleteEnrollment"
      @error="handleBulkDeleteEnrollmentError"
    />

    <LFLoading :is-loading="loading" />

    <!-- Toast para mensagens -->
    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import {
  fetchEnrolments,
  deleteEnrolmentBulk,
  searchEnrolledUsers,
} from "@/services/enrolment";

import { getClass, getCourseRoles } from "@/services/offer";

import Toast from "@/components/Toast.vue";
import ToastMessages from "@/mixins/toastMessages";

import GearIcon from "@/assets/icons/gear.svg";
import CircleInfoIcon from "@/assets/icons/circle-info.svg";

// Importação dos componentes
import EditRolesModal from "@/components/enrollment/EditRolesModal.vue";
import PageTabs from "@/components/enrollment/PageTabs.vue";
import CustomLabel from "@/components/CustomLabel.vue";
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import CustomButton from "@/components/CustomButton.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterActions from "@/components/FilterActions.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import AddEnrolmentModal from "@/components/enrollment/AddEnrolmentModal.vue";
import EnrollmentDetailsModal from "@/components/enrollment/EnrollmentDetailsModal.vue";
import EditEnrollmentModal from "@/components/enrollment/EditEnrollmentModal.vue";
import BulkEditEnrollmentModal from "@/components/enrollment/BulkEditEnrollmentModal.vue";
import BulkDeleteEnrollmentModal from "@/components/enrollment/BulkDeleteEnrollmentModal.vue";
import BackButton from "@/components/BackButton.vue";
import UserAvatar from "@/components/UserAvatar.vue";
import LFLoading from "@/components/LFLoading.vue";

export default {
  name: "ListEnrollments",

  mixins: [ToastMessages],

  components: {
    GearIcon,
    CircleInfoIcon,
    PageTabs,
    CustomLabel,
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    FilterTag,
    FilterTags,
    Pagination,
    PageHeader,
    ConfirmationModal,
    Autocomplete,
    AddEnrolmentModal,
    EnrollmentDetailsModal,
    Toast,
    EditRolesModal,
    EditEnrollmentModal,
    BulkEditEnrollmentModal,
    BulkDeleteEnrollmentModal,
    BackButton,
    UserAvatar,
    LFLoading,
  },

  props: {
    offerClassId: {
      type: [Number, String],
      required: true,
    },
  },

  data() {
    return {
      filteredUsers: [],
      nameOptions: [],
      cpfOptions: [],
      emailOptions: [],
      roleOptions: [],

      // Estados dos inputs de filtro
      nameSearchInput: "",
      cpfSearchInput: "",
      emailSearchInput: "",

      // Estados dos dropdowns
      showNameDropdown: false,
      showCpfDropdown: false,
      showEmailDropdown: false,

      // Timers para debounce
      nameDebounceTimer: null,
      cpfDebounceTimer: null,
      emailDebounceTimer: null,

      tableHeaders: [
        { text: "", value: "select", sortable: false, width: "50px" },
        {
          text: "NOME/SOBRENOME",
          value: "fullName",
          sortable: true,
          width: "220px",
        },
        { text: "E-MAIL", value: "email", sortable: true },
        { text: "CPF", value: "cpf", sortable: true },
        { text: "PAPÉIS", value: "roles", sortable: false },
        { text: "GRUPOS", value: "groups", sortable: false },
        {
          text: "DATA INÍCIO",
          value: "timeStartFormatted",
          sortable: true,
        },
        {
          text: "DATA FIM",
          value: "timeEndFormatted",
          sortable: true,
        },
        { text: "PRAZO", value: "deadline", sortable: true },
        { text: "PROGRESSO", value: "progressFormatted", sortable: false },
        { text: "SITUAÇÃO", value: "situation", sortable: true },
        { text: "NOTA", value: "grade", sortable: false },
        { text: "ESTADO", value: "status", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      enrolments: [],
      totalEnrolments: 0,
      loading: false,
      error: null,

      currentPage: 1,
      perPage: 10,

      sortBy: "fullName",
      sortDesc: false,

      selectedEnrollment: null,
      showEditRolesModal: false,
      showEnrollmentModal: false,
      showAddEnrolmentModal: false,
      showEditEnrollmentModal: false,
      showBulkEditEnrollmentModal: false,
      showBulkDeleteEnrollmentModal: false,

      offerClass: {},

      selectedUsers: [],
      selectedBulkAction: "",
    };
  },

  async created() {
    if (!this.offerClassId) {
      throw new Error("ID da turma não foi definido.");
    }

    await this.getOfferClass();
    await this.getRegisteredUsers();
    await this.getRoles();
  },

  beforeUnmount() {
    // Limpar timers
    if (this.nameDebounceTimer) clearTimeout(this.nameDebounceTimer);
    if (this.cpfDebounceTimer) clearTimeout(this.cpfDebounceTimer);
    if (this.emailDebounceTimer) clearTimeout(this.emailDebounceTimer);
  },

  computed: {
    allSelected() {
      return (
        this.enrolments.length > 0 &&
        this.selectedUsers.length === this.enrolments.length
      );
    },

    someSelected() {
      return this.selectedUsers.length > 0 && !this.allSelected;
    },

    excludedUserIds() {
      return this.filteredUsers.map((user) => user.id || user.value);
    },

    currentEnrolmentUserIds() {
      return this.enrolments.map((enrolment) => enrolment.userId);
    },

    /**
     * Returns a string representation of the total number of enrolments.
     */
    totalEnrolmentsInfo() {
      if (this.totalEnrolments === 0) return "Nenhum participante encontrado";
      if (this.totalEnrolments === 1) return "1 participante encontrado";

      return `${this.totalEnrolments} participantes encontrados`;
    },
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;
        this.selectedUsers = [];

        this.getRegisteredUsers();
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getRegisteredUsers();
      }
    },
  },

  methods: {
    /**
     * Gets offer class data
     *
     * @return {Promise<void>}
     */
    async getOfferClass() {
      try {
        this.loading = true;

        const response = await getClass(this.offerClassId);

        this.offerClass = response;
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Fetches enrolled users from backend
     *
     * @returns {Promise<void>}
     */
    async getRegisteredUsers() {
      try {
        this.loading = true;
        this.error = null;

        let userids = [];

        if (this.filteredUsers.length > 0) {
          userids = this.excludedUserIds;
        }

        const params = {
          offerclassid: this.offerClassId,
          userids: userids,
          page: this.currentPage,
          perpage: this.perPage,
          orderby: this.mapSortFieldToBackend(this.sortBy || "fullName"),
          direction: this.sortDesc ? "DESC" : "ASC",
        };

        const { enrolments, total } = await fetchEnrolments(params);

        this.totalEnrolments = total;

        this.enrolments = enrolments.map((enrolment) => {
          const roles = this.formatRoleOptions(enrolment.roles);

          return {
            ...enrolment,
            id: enrolment.userid,
            userId: enrolment.userid,
            offerUserEnrolId: enrolment.offeruserenrolid,
            fullName: enrolment.fullname,
            roles: roles,
            rolesFormatted: this.formatRoles(roles),
            enrolName: enrolment.enrol_name,
            timeCreatedFormatted: this.formatDateTime(enrolment.timecreated),
            timeStartFormatted: this.formatDate(enrolment.timestart),
            timeEndFormatted: this.formatDate(enrolment.timeend),
            deadline: enrolment.enrolperiod,
            progressFormatted: this.formatProgress(enrolment.progress),
            situationName: enrolment.situation_name,
            statusName: enrolment.status === 0 ? "Ativo" : "Suspenso",
            creatorName: enrolment.creatorname,
            modifierName: enrolment.modifiername,
            createdDate: enrolment.createddate,
            modifiedDate: enrolment.modifieddate,
          };
        });
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Fetches roles for the current offer class and updates the role options.
     *
     * @return {Promise<void>}
     */
    async getRoles() {
      try {
        this.loading = true;

        const response = await getCourseRoles(this.offerClass.offercourseid);

        this.roleOptions = response.map((role) => ({
          value: role.id,
          label: role.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    showEditRoles(user) {
      this.selectedEnrollment = user;
      this.showEditRolesModal = true;
    },

    // Métodos auxiliares para formatar os dados
    formatDate(timestamp) {
      if (!timestamp || timestamp === 0) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    formatDateTime(timestamp, options = {}) {
      if (!timestamp || timestamp === 0) return "-";

      if (Object.keys(options).length === 0) {
        options = {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        };
      }
      const date = new Date(timestamp * 1000);
      return date.toLocaleString("pt-BR", options);
    },

    formatProgress(progress) {
      if (progress === null || progress === undefined) return "-";
      return Math.round(progress) + "%";
    },

    /**
     * Formats the roles array into a string
     *
     * @param {array} roles
     * @returns {string}
     */
    formatRoles(roles) {
      if (!Array.isArray(roles)) return "-";

      if (roles.length > 2) {
        return (
          roles
            .slice(0, 2)
            .map((role) => role.label)
            .join(", ") +
          " + " +
          (roles.length - 2)
        );
      }

      if (Array.isArray(roles) && roles.length > 0) {
        return roles.map((role) => role.label).join(", ");
      }
    },

    /**
     * Converts an array of role objects into an array of options for selection components.
     *
     * @param {Array} roles - An array of role objects, each containing an id and a name.
     * @returns {Array} An array of option objects with value and label properties.
     */
    formatRoleOptions(roles) {
      return roles.map((role) => ({
        value: role.id,
        label: role.name,
      }));
    },

    // Métodos para carregar opções dos autocompletes
    async loadNameOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.nameOptions = [];
        this.showNameDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerClassId,
          fieldstring: "name",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        this.nameOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
        this.showNameDropdown = this.nameOptions.length > 0;
      } catch (error) {
        this.nameOptions = [];
        this.showNameDropdown = false;
      }
    },

    async loadCpfOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.cpfOptions = [];
        this.showCpfDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerClassId,
          fieldstring: "username",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        this.cpfOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
        this.showCpfDropdown = this.cpfOptions.length > 0;
      } catch (error) {
        this.cpfOptions = [];
        this.showCpfDropdown = false;
      }
    },

    async loadEmailOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.emailOptions = [];
        this.showEmailDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerClassId,
          fieldstring: "email",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        this.emailOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
        this.showEmailDropdown = this.emailOptions.length > 0;
      } catch (error) {
        this.emailOptions = [];
        this.showEmailDropdown = false;
      }
    },

    // Métodos para controlar os inputs de filtro
    handleNameInput() {
      if (this.nameDebounceTimer) {
        clearTimeout(this.nameDebounceTimer);
      }

      if (this.nameSearchInput.length >= 3) {
        this.nameDebounceTimer = setTimeout(() => {
          this.loadNameOptions(this.nameSearchInput);
        }, 500);
      } else {
        this.showNameDropdown = false;
      }
    },

    handleCpfInput() {
      if (this.cpfDebounceTimer) {
        clearTimeout(this.cpfDebounceTimer);
      }

      if (this.cpfSearchInput.length >= 3) {
        this.cpfDebounceTimer = setTimeout(() => {
          this.loadCpfOptions(this.cpfSearchInput);
        }, 500);
      } else {
        this.showCpfDropdown = false;
      }
    },

    handleEmailInput() {
      if (this.emailDebounceTimer) {
        clearTimeout(this.emailDebounceTimer);
      }

      if (this.emailSearchInput.length >= 3) {
        this.emailDebounceTimer = setTimeout(() => {
          this.loadEmailOptions(this.emailSearchInput);
        }, 500);
      } else {
        this.showEmailDropdown = false;
      }
    },

    // Métodos para selecionar opções dos dropdowns
    selectNameOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: "name",
      });
      this.nameSearchInput = "";
      this.showNameDropdown = false;
      this.clearOptions();
      this.getRegisteredUsers();
    },

    selectCpfOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: "cpf",
      });
      this.cpfSearchInput = "";
      this.showCpfDropdown = false;
      this.clearOptions();
      this.getRegisteredUsers();
    },

    selectEmailOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: "email",
      });
      this.emailSearchInput = "";
      this.showEmailDropdown = false;
      this.clearOptions();
      this.getRegisteredUsers();
    },

    clearOptions(field) {
      setTimeout(() => {
        switch (field) {
          case "name":
            this.nameOptions = [];
            break;
          case "cpf":
            this.cpfOptions = [];
            break;
          case "email":
            this.emailOptions = [];
            break;
          default:
            this.nameOptions = [];
            this.cpfOptions = [];
            this.emailOptions = [];
            break;
        }
      }, 500);
    },

    /**
     * Remove um usuário da lista de filtros baseado no índice ou ID do usuário
     * @param {number|string} indexOrUserId - Índice do usuário na lista filteredUsers ou ID do usuário
     */
    removeFilter(userid) {
      const userIndex = this.filteredUsers.findIndex(
        (user) => user.id === userid || user.value === userid
      );

      if (userIndex !== -1) {
        this.filteredUsers.splice(userIndex, 1);
      }
      this.getRegisteredUsers();
    },

    /**
     * Limpa todos os filtros aplicados e recarrega a lista de usuários
     */
    clearFilteredUsers() {
      this.filteredUsers = [];
      this.getRegisteredUsers();
    },

    // Métodos para manipular a seleção de usuários
    toggleSelectAll() {
      if (this.allSelected) {
        this.selectedUsers = [];
      } else {
        this.selectedUsers = this.enrolments.map((user) => user.id);
      }
    },

    toggleSelectUser(userId) {
      const index = this.selectedUsers.indexOf(userId);
      if (index === -1) {
        this.selectedUsers.push(userId);
      } else {
        this.selectedUsers.splice(index, 1);
      }
    },

    isSelected(userId) {
      return this.selectedUsers.includes(userId);
    },

    async handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;

      await this.getRegisteredUsers();
    },

    mapSortFieldToBackend(frontendField) {
      const fieldMapping = {
        fullName: "fullname",
        email: "email",
        cpf: "cpf",
        timeStartFormatted: "startdate",
        timeEndFormatted: "enddate",
        deadline: "enrolperiod",
        situation: "situation",
        status: "status",
      };

      return fieldMapping[frontendField] || "fullname";
    },

    addNewUser() {
      if (this.offerClass && this.offerClass?.operational_cycle === 2) {
        this.error =
          "Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";
        return;
      }

      this.showAddEnrolmentModal = true;
    },

    closeEnrolmentModal() {
      this.showAddEnrolmentModal = false;
    },

    async navigateToBack() {
      this.$router.push({
        name: "offer.edit",
        params: { id: this.offerClass.offerid },
      });
    },

    // Método para redirecionar para o perfil do usuário
    viewUserProfile(userId) {
      if (!userId) {
        return;
      }

      // Construir a URL para o perfil do usuário
      const url = `/user/view.php?id=${userId}&course=${this.offerClass.courseid}`;

      // Redirecionar para a URL
      window.location.href = url;
    },

    showEnrollmentDetails(enrollment) {
      this.selectedEnrollment = enrollment;

      this.showEnrollmentModal = true;
    },

    closeEnrollmentModal() {
      this.showEnrollmentModal = false;
      this.selectedEnrollment = null;
    },

    closeEditEnrollmentModal() {
      this.showEditEnrollmentModal = false;
      this.selectedEnrollment = null;
    },

    async handleEditEnrollmentSuccess(data) {
      this.showEditEnrollmentModal = false;
      this.selectedEnrollment = null;

      this.showSuccessMessage("Matrícula editada com sucesso.");

      await this.getRegisteredUsers();
    },

    /**
     * Update the status of a user's enrollment
     * @param {Object} data Dados da atualização
     */
    handleRoleUpdateSuccess(data) {
      const userIndex = this.enrolments.findIndex(
        (user) => user.userId === data.userId
      );

      if (userIndex !== -1) {
        this.enrolments[userIndex].roles = data.newRoles;
        this.enrolments[userIndex].rolesFormatted = this.formatRoles(
          data.newRoles
        );
      } else {
        this.getRegisteredUsers();
      }

      this.showEditRolesModal = false;
      this.showSuccessMessage("Papéis atualizados com sucesso.");
    },

    editUser(user) {
      this.selectedEnrollment = user;

      this.showEditEnrollmentModal = true;
    },

    async confirmeBulkDeleteEnrollment() {
      this.loading = true;

      const offerUserEnrolIds = [];

      for (const userId of this.selectedUsers) {
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user) {
          offerUserEnrolIds.push(user.offerUserEnrolId);
        }
      }

      if (offerUserEnrolIds.length === 0) {
        this.showErrorMessage(
          "Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."
        );
        this.loading = false;
        return;
      }

      const processingMessage = `Processando exclusão de ${offerUserEnrolIds.length} matrícula(s)...`;
      this.showSuccessMessage(processingMessage);

      const results = await deleteEnrolmentBulk(offerUserEnrolIds);

      if (results && results.length > 0) {
        const successCount = results.filter(
          (result) => result.operation_status
        ).length;
        const failCount = results.length - successCount;

        if (successCount > 0) {
          this.showSuccessMessage(
            `${successCount} matrícula(s) cancelada(s) com sucesso.${failCount > 0 ? ` ${failCount} matrícula(s) não puderam ser canceladas.` : ""}`
          );

          await this.getRegisteredUsers();

          this.selectedUsers = [];
        } else {
          this.showErrorMessage(
            "Não foi possível cancelar as matrículas. Por favor, tente novamente."
          );
        }
      } else {
        this.showSuccessMessage(
          `${offerUserEnrolIds.length} matrícula(s) cancelada(s) com sucesso.`
        );

        await this.getRegisteredUsers();

        // Limpar a seleção de usuários
        this.selectedUsers = [];
      }

      this.showBulkDeleteEnrollmentModal = false;
      this.loading = false;
    },

    handleBulkAction() {
      if (!this.selectedBulkAction) return;

      // Verificar se temos usuários selecionados
      if (this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para realizar esta ação."
        );
        this.selectedBulkAction = "";
        return;
      }

      switch (this.selectedBulkAction) {
        case "message":
          this.sendMessage();
          break;
        case "note":
          this.writeNote();
          break;
        case "download_csv":
          this.downloadData("csv");
          break;
        case "download_xlsx":
          this.downloadData("xlsx");
          break;
        case "download_html":
          this.downloadData("html");
          break;
        case "download_json":
          this.downloadData("json");
          break;
        case "download_ods":
          this.downloadData("ods");
          break;
        case "download_pdf":
          this.downloadData("pdf");
          break;
        case "edit_enrolment":
          this.editEnrolments();
          break;
        case "delete_enrolment":
          this.bulkDeleteEnrollment();
          break;
      }

      // Reset the select after action
      this.selectedBulkAction = "";
    },

    sendMessage() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para enviar mensagem."
        );
        return;
      }

      this.showSendMessageModal(this.selectedUsers);
    },

    /**
     * Mostra o modal de envio de mensagens usando a API do Moodle
     *
     * @param {Array} userIds - IDs dos usuários para enviar mensagem
     */
    showSendMessageModal(userIds) {
      if (typeof window.require !== "function") {
        this.showErrorMessage(
          "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_message/message_send_bulk do Moodle para mostrar o modal
      window.require(
        ["core_message/message_send_bulk"],
        (BulkSender) => {
          if (typeof BulkSender.showModal !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Mostrar o modal de mensagens
          BulkSender.showModal(userIds, () => {
            // Resetar o select após o envio da mensagem
            this.selectedBulkAction = "";
          });
        },
        (error) => {
          this.showErrorMessage(
            "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    writeNote() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para escrever anotação."
        );
        return;
      }

      this.showAddNoteModal(this.offerClass.courseid, this.selectedUsers);
    },

    /**
     * Mostra o modal de adição de anotações usando a API do Moodle
     *
     * @param {Number} courseId - ID do curso
     * @param {Array} userIds - IDs dos usuários para adicionar anotação
     */
    showAddNoteModal(courseId, userIds) {
      // Verificar se o módulo necessário está disponível
      if (typeof window.require !== "function") {
        this.showErrorMessage(
          "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_user/local/participants/bulkactions do Moodle para mostrar o modal
      window.require(
        ["core_user/local/participants/bulkactions"],
        (BulkActions) => {
          if (typeof BulkActions.showAddNote !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Obter os nomes dos estados de anotação
          const noteStateNames = {
            personal: "Pessoal",
            course: "Curso",
            site: "Site",
          };

          // Mostrar o modal de anotações
          BulkActions.showAddNote(courseId, userIds, noteStateNames, "")
            .then((modal) => {
              modal.getRoot().on("hidden.bs.modal", () => {
                this.selectedBulkAction = "";
              });

              return modal;
            })
            .catch((error) => {
              this.showErrorMessage(
                "Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde."
              );
            });
        },
        (error) => {
          this.showErrorMessage(
            "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    downloadData(format) {
      if (this.selectedUsers.length === 0) return;

      this.prepareLocalDownload(format);
    },

    prepareLocalDownload(format) {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage("Nenhum usuário selecionado para download.");
        return;
      }

      const data = [];

      for (const userId of this.selectedUsers) {
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user) {
          const userData = {
            ID: user.id || "",
            Nome: user.fullName || user.name || "",
            Email: user.email || "",
            CPF: user.cpf || "",
            Papéis: user.roles || "",
            Grupos: user.groups || "",
            "Data de Início": user.timeStartFormatted || "",
            "Data de Término": user.timeEndFormatted || "",
            Prazo: user.deadline || "",
            Progresso: user.progressFormatted || "",
            Situação: user.situationName || user.situation || "",
            Nota: user.grade || "",
            Estado: user.statusName || "",
          };

          data.push(userData);
        }
      }

      // Verificar se há dados para download
      if (data.length === 0) {
        this.showErrorMessage("Nenhum dado disponível para download.");
        return;
      }

      switch (format) {
        case "csv":
          this.downloadCSV(data);
          break;
        case "xlsx":
          this.downloadXLSX(data);
          break;
        case "html":
          this.downloadHTML(data);
          break;
        case "json":
          this.downloadJSON(data);
          break;
        case "ods":
          this.downloadODS(data);
          break;
        case "pdf":
          this.downloadPDF(data);
          break;
        default:
          this.showErrorMessage("Formato de download não suportado.");
          break;
      }
    },

    downloadCSV(data) {
      // Implementação para download de CSV
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Formatar os cabeçalhos para melhor legibilidade
      const formattedHeaders = headers.map((header) => {
        // Converter camelCase para Title Case com espaços
        return header
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
      });

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          formattedHeaders.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadXLSX(data) {
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente no Excel
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          headers.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Mostrar uma mensagem informativa
      this.showSuccessMessage(
        "Download concluído. O arquivo CSV pode ser aberto no Excel."
      );
    },

    downloadHTML(data) {
      if (data.length === 0) return;

      const headers = Object.keys(data[0]);

      const formattedHeaders = [];
      for (let i = 0; i < headers.length; i++) {
        const formatted = headers[i]
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
        formattedHeaders.push(formatted);
      }

      let tableHeaders = "";
      for (let i = 0; i < formattedHeaders.length; i++) {
        tableHeaders += "<th>" + formattedHeaders[i] + "</th>";
      }

      let tableRows = "";
      for (let i = 0; i < data.length; i++) {
        let rowHtml = "<tr>";
        for (let j = 0; j < headers.length; j++) {
          rowHtml += "<td>" + (data[i][headers[j]] || "") + "</td>";
        }
        rowHtml += "</tr>";
        tableRows += rowHtml;
      }

      const htmlStart =
        '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>';
      const styles =
        "<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>";
      const bodyStart = "</head><body><h1>Usuários Matriculados</h1>";
      const tableStart = "<table><thead><tr>";
      const tableMiddle = "</tr></thead><tbody>";
      const tableEnd = "</tbody></table>";
      const footer =
        '<div class="footer">Gerado em ' +
        new Date().toLocaleString() +
        "</div>";
      const htmlEnd = "</body></html>";

      const htmlContent =
        htmlStart +
        styles +
        bodyStart +
        tableStart +
        tableHeaders +
        tableMiddle +
        tableRows +
        tableEnd +
        footer +
        htmlEnd;

      const blob = new Blob([htmlContent], {
        type: "text/html;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.html");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Mostrar uma mensagem informativa
      this.showSuccessMessage(
        "Download concluído. O arquivo HTML foi salvo com sucesso."
      );
    },

    downloadJSON(data) {
      // Implementação para download de JSON
      if (data.length === 0) return;

      // Criar o conteúdo JSON formatado
      const jsonContent = JSON.stringify(data, null, 2);

      // Criar o blob e fazer o download
      const blob = new Blob([jsonContent], {
        type: "application/json;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.json");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadODS(data) {
      if (data.length === 0) return;

      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      let csvRows = [];

      csvRows.push(headers.join(","));

      data.forEach((row) => {
        const values = headers.map((header) => {
          const value = row[header] || "";
          return '"' + String(value).replace(/"/g, '""') + '"';
        });
        csvRows.push(values.join(","));
      });

      const csvContent = BOM + csvRows.join("\n");

      const blob = new Blob([csvContent], {
        type: "text/csv;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.showSuccessMessage(
        "Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS."
      );
    },

    downloadPDF(data) {
      if (data.length === 0) return;

      this.downloadHTML(data);

      this.showSuccessMessage(
        "Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."
      );
    },

    editEnrolments() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para editar matrícula."
        );
        return;
      }

      if (this.selectedUsers.length === 1) {
        const userId = this.selectedUsers[0];
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user) {
          this.editUser(user);
        } else {
          this.showErrorMessage(
            "Usuário não encontrado. Por favor, tente novamente."
          );
        }
      } else {
        this.showBulkEditEnrollmentModal = true;
      }
    },

    async handleBulkEditEnrollmentSuccess(data) {
      this.showSuccessMessage(
        data.message || "Matrículas editadas com sucesso."
      );

      await this.getRegisteredUsers();

      this.selectedUsers = [];
      this.showBulkEditEnrollmentModal = false;
    },

    handleBulkEditEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível editar as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },

    bulkDeleteEnrollment() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para excluir matrícula."
        );
        return;
      }

      this.showBulkDeleteEnrollmentModal = true;
    },

    handleBulkDeleteEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível excluir as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },
  },
};
</script>

<style src="@/assets/scss/RegisteredUsers.scss" lang="scss" scoped></style>
