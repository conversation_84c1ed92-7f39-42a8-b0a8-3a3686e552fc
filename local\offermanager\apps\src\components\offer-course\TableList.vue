<template>
  <div>
    <div class="row mb-5">
      <div class="col-md-8">
        <div class="row">
          <div class="col-md-4">
            <Autocomplete
              v-model="inputFilters.category"
              :items="categoryOptions"
              placeholder="Pesquisar..."
              label="Categoria"
              :has-search-icon="true"
              :auto-open="false"
              :show-filter-tags="false"
              :show-selected-in-input="true"
              :no-results-text="
                categoryOptions.length === 0
                  ? 'Nenhuma categoria disponível'
                  : 'Nenhuma categoria encontrada'
              "
            />
          </div>

          <div class="col-md-4 mt-3 mt-md-0">
            <Autocomplete
              v-model="inputFilters.course"
              :items="courseOptions"
              placeholder="Pesquisar..."
              label="Curso"
              :has-search-icon="true"
              :auto-open="true"
              :loading="loadingCourses || loadingMoreCourses"
              :no-results-text="filterCourseNoResultsText"
              @load-more="loadMoreCourses"
              @search="(search) => getCourseOptions(search)"
              ref="courseAutocomplete"
            />
          </div>

          <div class="col-md-4 mt-3 mt-md-0">
            <CustomLabel text="&nbsp" className="d-none d-md-block" />
            <CustomCheckbox
              v-model="inputFilters.onlyActive"
              id="onlyActive"
              label="Não exibir inativos"
              @change="handleOnlyActiveChange"
            />
          </div>

          <!-- Tags de filtro -->
          <FilterTags v-if="inputFilters.course" class="col-md-8 mt-3">
            <FilterTag @remove="removeFilter('course')">
              Curso: {{ inputFilters.course.label }}
            </FilterTag>
          </FilterTags>
        </div>
      </div>

      <div class="col-md-4" v-if="!isReadonly">
        <CustomLabel text="&nbsp" />
        <div class="d-flex justify-content-end">
          <CustomButton
            variant="primary"
            icon="fa-solid fa-plus"
            label="Adicionar Curso"
            @click="showAddCourseModalVisible = true"
          />
        </div>
      </div>
    </div>

    <!-- Tabela de Cursos -->
    <CollapsibleTable
      :headers="offerCourseTableHeaders"
      :items="offerCourses"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @sort="handleTableSort"
      :expandable="true"
    >
      <template #empty-state>
        <div class="empty-state">
          <span class="no-results">{{
            loading ? "Carregando registros..." : "Nenhum registro encontrado"
          }}</span>
        </div>
      </template>

      <template #item-name="{ item }">
        <span :title="item.name">
          {{
            item.name.length > 50 ? item.name.slice(0, 50) + "..." : item.name
          }}
        </span>
      </template>

      <template #item-status="{ item }">
        <span v-if="item.status">
          <CircleCheckIcon />
          Ativo
        </span>
        <span v-else>
          <CircleXMarkIcon />
          Inativo
        </span>
      </template>

      <template #item-actions="{ item }">
        <div class="action-buttons">
          <button
            class="btn-action btn-add"
            :disabled="isReadonly"
            @click="addOfferClass(item)"
            title="Adicionar turma"
          >
            <i class="fa-solid fa-plus"></i>
          </button>
          <button
            class="btn-action"
            :class="item.status ? 'btn-deactivate' : 'btn-activate'"
            @click="requestToggleOfferCourseStatus(item)"
            :disabled="
              (!item.status && !item.canActivate) ||
              !item.canActivate ||
              isReadonly
            "
            :title="getStatusButtonTitle(item)"
          >
            <i :class="item.status ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
          </button>
          <button
            class="btn-action btn-delete"
            @click="requestDeleteOfferCourse(item)"
            :disabled="!item.canDelete || isReadonly"
            :title="
              item.canDelete ? 'Excluir' : 'Não é possível excluir este curso'
            "
          >
            <i class="fa fa-trash fa-fw"></i>
          </button>
        </div>
      </template>

      <template #expanded-content="{ item }">
        <CustomTable
          :headers="offerClassTableHeaders"
          :items="item.offerClasses"
          tableClass="table-hover mb-0"
          theadClass="thead-dark"
          tbodyClass="tbody-light"
        >
          <template #item-name="{ item }">
            {{
              item.name.length > 20 ? item.name.slice(0, 20) + "..." : item.name
            }}
          </template>
          <template #item-status="{ item }">
            <span
              :class="[
                'operational-cycle badge',
                getOperationalCycleClassName(item.operational_cycle),
              ]"
            >
              {{ item.operational_cycle_name }}
            </span>
          </template>
          <template #item-actions="{ item }">
            <div class="action-buttons">
              <button
                class="btn-action btn-edit"
                @click="navigateToShowOfferClass(item)"
                title="Visualizar"
              >
                <ViewIcon />
              </button>
              <button
                class="btn-action btn-users"
                @click="navigateToEnrollments(item)"
                title="Usuários Matriculados"
              >
                <UsersIcon />
              </button>
              <button
                class="btn-action btn-edit"
                :disabled="isReadonly"
                @click="navigateToEditOfferClass(item)"
                title="Editar"
              >
                <i class="fas fa-pencil-alt"></i>
              </button>
              <button
                class="btn-action btn-duplicate"
                :disabled="isReadonly"
                @click="duplicateOfferClass(item)"
                title="Duplicar Turma"
              >
                <i class="fas fa-copy"></i>
              </button>
              <button
                class="btn-action"
                :class="item.status ? 'btn-deactivate' : 'btn-activate'"
                :title="item.status ? 'Inativar' : 'Ativar'"
                :disabled="isReadonly"
                @click="requestToggleOfferClassStatus(item)"
              >
                <i :class="item.status ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
              </button>
              <button
                class="btn-action btn-delete"
                @click="removeOfferClass(item)"
                :disabled="!item.canDelete || isReadonly"
                :title="
                  item.canDelete
                    ? 'Excluir'
                    : 'Não é possível excluir esta turma'
                "
              >
                <i class="fa fa-trash fa-fw"></i>
              </button>
            </div>
          </template>
        </CustomTable>
      </template>
    </CollapsibleTable>

    <Pagination
      ref="pagination"
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="totalItems"
      @update:current-page="handlePageChange"
    />

    <AddOfferCourseModal
      v-if="offerId"
      v-model="showAddCourseModalVisible"
      :offerId="offerId"
      @confirm="handleAddCourseConfirm"
    />

    <!-- Modal de Confirmação de Inativação de Curso -->
    <ConfirmationModal
      size="md"
      :show="showCourseStatusModal"
      :title="
        selectedOfferCourse?.showOfferClassStatusModal
          ? 'Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOfferCourse?.status
          ? ''
          : 'Tem certeza que deseja ativar este curso?'
      "
      :list-items="
        selectedOfferCourse?.status
          ? [
              'O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.',
              'Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.',
              'Novos alunos não poderão ser inscritos através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOfferCourse?.status ? 'Inativar curso' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOfferCourse?.status ? 'warning' : 'question'"
      @close="showCourseStatusModal = false"
      @confirm="toggleOfferCourseStatus"
    />

    <!-- Modal de Confirmação de Exclusão de Curso -->
    <ConfirmationModal
      size="md"
      :show="showDeleteOfferCourseModal"
      title="A exclusão deste curso da instância de oferta é uma ação irreversível"
      message="Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir curso"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteOfferCourseModal = false"
      @confirm="deleteOfferCourse"
    />

    <!-- Modal de Confirmação de Exclusão de Turma -->
    <ConfirmationModal
      size="md"
      :show="showDeleteOfferClassModal"
      title="A exclusão desta turma é uma ação irreversível"
      message="Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?"
      confirm-button-text="Excluir Turma"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showDeleteOfferClassModal = false"
      @confirm="deleteOfferClass"
    />

    <!-- Modal de Confirmação de Inativação de Turma -->
    <ConfirmationModal
      :show="showOfferClassStatusModal"
      size="md"
      :title="
        selectedOfferClass?.status
          ? 'Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:'
          : 'Confirmar Ativação'
      "
      :message="
        selectedOfferClass?.status
          ? ''
          : 'Tem certeza que deseja ativar esta turma?'
      "
      :list-items="
        selectedOfferClass?.status
          ? [
              'Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.',
              'Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.',
              'Novos alunos não poderão ser matriculados através da oferta.',
            ]
          : []
      "
      :confirm-button-text="
        selectedOfferClass?.status ? 'Inativar Turma' : 'Ativar'
      "
      cancel-button-text="Cancelar"
      :icon="selectedOfferClass?.status ? 'warning' : 'question'"
      @close="showOfferClassStatusModal = false"
      @confirm="toggleOfferClassStatus"
    />

    <!-- Modal de Duplicação de Turma -->
    <DuplicateOfferClassModal
      v-if="showDuplicateOfferClassModal"
      size="lg"
      :offerClass="offerClassToDuplicate"
      :parentOfferCourse="parentOfferCourse"
      :offerId="offerId"
      @close="showDuplicateOfferClassModal = false"
      @success="handleDuplicateSuccess"
    />

    <!-- Modal de Seleção de Tipo de Inscrição -->
    <AddOfferClassModal
      v-if="showEnrolTypeModal"
      :offerCourseId="selectedOfferCourseForClass?.id"
      :offerId="offerId"
      @close="showEnrolTypeModal = false"
      @confirm="handleAddClassConfirm"
    />

    <LFLoading :is-loading="loading" />

    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import UsersIcon from "@/assets/icons/users.svg";
import ViewIcon from "@/assets/icons/file-search-fill.svg";
import CircleCheckIcon from "@/assets/icons/circle-check.svg";
import CircleXMarkIcon from "@/assets/icons/circle-xmark.svg";
import CustomLabel from "@/components/CustomLabel.vue";
import ToastMessages from "@/mixins/toastMessages";
import OfferForm from "@/components/offer/Form.vue";
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomButton from "@/components/CustomButton.vue";
import Pagination from "@/components/Pagination.vue";
import CollapsibleTable from "@/components/CollapsibleTable.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import TextEditor from "@/components/TextEditor.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import AddOfferCourseModal from "@/components/offer-course/AddOfferCourseModal.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import DuplicateOfferClassModal from "@/components/offer-class/DuplicateOfferClassModal.vue";
import AddOfferClassModal from "@/components/offer-class/AddOfferClassModal.vue";
import Toast from "@/components/Toast.vue";
import LFLoading from "@/components/LFLoading.vue";

import {
  toggleCourseStatus,
  toggleClassStatus,
  removeCourseFromOffer,
  getCurrentCourses,
  getCategories,
  searchCurrentCoursesByCategory,
  getClasses,
  deleteClass,
} from "@/services/offer";

export default {
  name: "TableList",

  mixins: [ToastMessages],

  components: {
    ViewIcon,
    UsersIcon,
    CircleCheckIcon,
    CircleXMarkIcon,
    CustomLabel,
    OfferForm,
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomButton,
    Pagination,
    CollapsibleTable,
    PageHeader,
    BackButton,
    Autocomplete,
    TextEditor,
    CustomCheckbox,
    FilterRow,
    FilterGroup,
    FilterTag,
    FilterTags,
    AddOfferCourseModal,
    ConfirmationModal,
    Toast,
    DuplicateOfferClassModal,
    AddOfferClassModal,
    LFLoading,
  },

  props: {
    offerId: {
      type: Number,
      required: true,
    },
    isReadonly: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      showAddCourseModalVisible: false,
      showCourseStatusModal: false,
      showDeleteOfferCourseModal: false,
      offerCourseToDelete: null,
      showDeleteOfferClassModal: false,
      offerClassToDelete: null,
      showOfferClassStatusModal: false,
      showDuplicateOfferClassModal: false,
      showEnrolTypeModal: false,
      selectedOfferClass: null,
      offerClassToDuplicate: null,
      parentOfferCourse: null,
      selectedOfferCourseForClass: null,
      // Opções para os Autocomplete de categoria e curso
      categoryOptions: [],
      courseOptions: [],
      selectedOfferCourse: null,

      loading: false,

      // Filtros
      inputFilters: {
        course: null,
        category: null,
        onlyActive: false,
      },

      offerCourses: [],

      // Paginação
      currentPage: 1,
      perPage: 5,
      totalItems: 0,

      // Ordenação
      sortBy: "id",
      sortDesc: false,

      // Paginação de cursos potenciais
      filterCoursesPage: 1,
      filterCoursesPerPage: 20,
      coursesTotalPages: 1,
      hasMoreCourses: false,
      loadingCourses: false,
      loadingMoreCourses: false,
      filterCourseNoResultsText: "Nenhum curso encontrado",

      // Tabela de cursos
      offerCourseTableHeaders: [
        { text: "NOME DO CURSO", value: "name", sortable: true },
        { text: "CATEGORIA", value: "category", sortable: true },
        { text: "NÚMERO DE TURMAS", value: "courseClassCount", sortable: true },
        { text: "STATUS DO CURSO", value: "status", sortable: true },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      offerClassTableHeaders: [
        { text: "NOME DA TURMA", value: "name", sortable: false },
        { text: "TIPO DE INSCRIÇÃO", value: "enrolName", sortable: false },
        { text: "VAGAS", value: "vacancies", sortable: false },
        { text: "INSCRITOS", value: "totalEnrolled", sortable: false },
        { text: "DATA INÍCIO", value: "startDate", sortable: false },
        { text: "DATA FIM", value: "endDate", sortable: false },
        { text: "STATUS", value: "status", sortable: false },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],
    };
  },

  watch: {
    async "inputFilters.course"(newValue, oldValue) {
      this.currentPage = 1;

      if (newValue !== null) {
        this.inputFilters.category = null;
      }

      await this.getCourses();
    },

    async "inputFilters.category"(newValue, oldValue) {
      if (newValue === "") {
        this.inputFilters.category = null;
      }

      if (newValue !== null) {
        this.inputFilters.course = null;
      }

      this.currentPage = 1;

      await this.getCourses();
      await this.getCourseOptions();
    },

    async "inputFilters.onlyActive"(newValue, oldValue) {
      this.currentPage = 1;

      this.inputFilters.course = null;

      await this.getCourses();
      await this.getCourseOptions();
    },

    currentPage() {
      this.getCourses();
    },

    perPage() {
      this.currentPage = 1;

      this.getCourses();
    },
  },

  async created() {
    await this.getCourses();
    await this.getCategoryOptions();
    await this.getCourseOptions();
  },

  methods: {
    /**
     * Fetches the courses for the given offer
     *
     * @returns {Promise<void>}
     */
    async getCourses() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        const options = {
          onlyActive: this.inputFilters.onlyActive,
          page: this.currentPage,
          perPage: this.perPage,
          sortBy: this.sortBy,
          sortDesc: this.sortDesc,
        };

        if (this.inputFilters.course) {
          options.courseIds = [this.inputFilters.course.value];
        }

        if (this.inputFilters.category) {
          options.categorySearch = this.inputFilters.category.label;
        }

        const response = await getCurrentCourses(this.offerId, options);

        const {
          page,
          total_pages,
          total_items,
          courses: offerCourses,
        } = response;

        this.currentPage = page;

        const processedCourses = [];

        for (const offerCourse of offerCourses) {
          try {
            const offerClassesResponse = await getClasses(offerCourse.id);

            const offerClasses = offerClassesResponse.map((offerClass) => {
              return {
                ...offerClass,
                offerCourseId: offerCourse.id,
                enrol: offerClass.enrol || "-",
                enrolName: offerClass.enrol_name || "-",
                vacancies: offerClass.max_users
                  ? offerClass.max_users
                  : "Ilimitado",
                totalEnrolled: offerClass.enrolled_users || 0,
                startDate: this.formatDate(offerClass.startdate),
                endDate: this.formatDate(offerClass.enddate),
                status: !!parseInt(offerClass.status),
                statusName: !!parseInt(offerClass.status) ? "Ativa" : "Inativo",
                canActivate: offerClass.can_activate,
                canDelete: offerClass.can_delete,
              };
            });

            processedCourses.push({
              id: offerCourse.id,
              courseId: offerCourse.courseid,
              name: offerCourse.fullname,
              category: offerCourse.category_name || "-",
              courseClassCount: offerClasses.length,
              status: !!parseInt(offerCourse.status),
              statusName: !!parseInt(offerCourse.status) ? "Ativo" : "Inativo",
              canDelete: offerCourse.can_delete,
              canActivate: offerCourse.can_activate,
              offerClasses,
            });
          } catch (error) {
            console.log(error);
          }
        }

        this.offerCourses = processedCourses;

        this.totalItems = total_items;
      } catch (error) {
        this.showErrorMessage(error);
        this.offerCourses = [];
        this.totalItems = 0;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets the category options from the API
     *
     * @returns {Promise<void>}
     */
    async getCategoryOptions() {
      if (!this.offerId) return;

      try {
        this.loading = true;

        const response = await getCategories("", this.offerId);

        this.categoryOptions = response.map((category) => ({
          value: category.id,
          label: category.name,
        }));
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets the course options from the API
     *
     * @param {String}  search - Search term
     * @param {boolean} resetPagination - Reset pagination
     * @returns {Promise<void>}
     */
    async getCourseOptions(search = "", resetPagination = true) {
      if (!this.offerId) return;

      this.loading = true;

      try {
        if (resetPagination) {
          this.filterCoursesPage = 1;
          this.coursesTotalPages = 1;
          this.hasMoreCourses = false;
          this.loadingCourses = true;
          this.courseOptions = [];
        } else {
          this.loadingMoreCourses = true;
        }

        const response = await searchCurrentCoursesByCategory(
          this.offerId,
          this.inputFilters.category?.value,
          search,
          this.inputFilters.course?.value
            ? [this.inputFilters.course.value]
            : [],
          this.inputFilters.onlyActive
        );

        const courseOptions = response.map((course) => ({
          value: course.id || course.courseid,
          label: course.fullname,
        }));

        if (resetPagination) {
          this.courseOptions = courseOptions;
        } else {
          this.courseOptions = [...this.courseOptions, ...courseOptions];
        }

        this.hasMoreCourses = false;

        if (this.courseOptions.length === 0) {
          this.filterCourseNoResultsText =
            "Nenhum curso disponível nesta categoria";
        }
      } catch (error) {
        this.showErrorMessage("Erro ao carregar cursos da categoria.");

        if (resetPagination) {
          this.courseOptions = [];
        }

        this.hasMoreCourses = false;
      } finally {
        if (resetPagination) {
          this.loadingCourses = false;
        } else {
          this.loadingMoreCourses = false;
        }

        this.loading = false;
      }
    },

    /**
     * Returns the CSS class name associated with the specified operational cycle.
     *
     * @param {number} operational_cycle - The operational cycle identifier (e.g., 0, 1, 2).
     * @returns {string} The corresponding CSS class name ("secondary", "primary", "success")
     *                   or an empty string if the cycle is unrecognized.
     */
    getOperationalCycleClassName(operational_cycle) {
      switch (operational_cycle) {
        case 0:
          return "badge-secondary";
        case 1:
          return "badge-primary";
        case 2:
          return "badge-success";
        default:
          return "";
      }
    },

    /**
     * Format a date timestamp to the format DD/MM/YYYY
     *
     * @param {number} timestamp - Timestamp em segundos
     * @returns {string} Data formatada ou '-' se não houver data
     */
    formatDate(timestamp) {
      if (!timestamp) return "-";

      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    clearFilters() {
      this.inputFilters = {
        course: null,
        category: null,
        onlyActive: false,
      };

      this.getCourses();

      this.getCourseOptions();
    },

    async removeFilter(filter) {
      this.inputFilters[filter] = null;
    },

    /**
     * Carrega mais cursos (próxima página) para o scroll infinito
     */
    async loadMoreCourses() {
      if (
        this.hasMoreCourses &&
        !this.loadingMoreCourses &&
        !this.loadingCourses
      ) {
        this.filterCoursesPage += 1;

        if (this.inputFilters.category?.value) {
          await this.getCourseOptions("", false);
        }
      }
    },

    handleOnlyActiveChange() {
      this.currentPage = 1;

      this.getCourses();
    },

    async handleAddCourseConfirm(offerCourses) {
      try {
        this.loading = true;

        await this.getCourses();

        this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.");
      } catch (error) {
        this.showErrorMessage(
          error.message || "Ocorreu um erro ao adicionar os cursos."
        );
      } finally {
        this.loading = false;
      }
    },

    handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;
      this.getCourses();
    },

    /**
     * Manipula a mudança de página na paginação
     * @param {number} page - Número da página selecionada
     */
    handlePageChange(page) {
      this.currentPage = page;

      this.getCourses();
    },

    addOfferClass(course) {
      this.selectedOfferCourseForClass = course;
      this.showEnrolTypeModal = true;
    },

    handleAddClassConfirm(data) {
      this.showEnrolTypeModal = false;

      this.$router.push({
        name: "offer.class.create",
        query: {
          enrolMethod: data.enrolType,
          offerCourseId: data.offerCourseId,
        },
      });
    },

    /**
     * Redirect to show class page
     * @param {Object} offerClass - Class to be shown
     */
    navigateToShowOfferClass(offerClass) {
      this.$router.push({
        name: "offer.class.show",
        params: {
          offerClassId: offerClass.id,
        },
      });
    },

    /**
     * Redirect to edit class page
     * @param {Object} offerClass - Class to be edited
     */
    navigateToEditOfferClass(offerClass) {
      this.$router.push({
        name: "offer.class.edit",
        params: {
          offerClassId: offerClass.id,
        },
      });
    },

    requestToggleOfferClassStatus(offerClass) {
      this.selectedOfferClass = {
        ...offerClass,
      };

      this.showOfferClassStatusModal = true;
    },

    /**
     * Confirms and updates the status of the selected offer class.
     */
    async toggleOfferClassStatus() {
      if (!this.selectedOfferClass) return;

      this.loading = true;
      this.showOfferClassStatusModal = false;

      try {
        const { id, name, status } = this.selectedOfferClass;
        const newStatus = !status;

        await toggleClassStatus(id, newStatus);

        const offerCourse = this.offerCourses.find((c) =>
          c.offerClasses.some((t) => t.id === id)
        );

        if (offerCourse) {
          const courseClass = offerCourse.offerClasses.find((t) => t.id === id);

          if (courseClass) {
            courseClass.status = newStatus;
            courseClass.statusName = newStatus ? "Ativo" : "Inativo";
          }
        }

        this.showSuccessMessage(
          newStatus
            ? `Turma "${name}" ativada com sucesso.`
            : `Turma "${name}" inativada com sucesso.`
        );

        this.selectedOfferClass = null;
      } catch (error) {
        this.showErrorMessage(
          error.message || "Erro ao alterar status da turma."
        );
      } finally {
        this.loading = false;
      }
    },

    removeOfferClass(offerClass) {
      if (!offerClass.canDelete) {
        return;
      }

      this.offerClassToDelete = offerClass;
      this.showDeleteOfferClassModal = true;
    },

    /**
     * Navigates to the enrollments page of the given offer class.
     * @param {Object} offerClass - The offer class to navigate to.
     */
    navigateToEnrollments(offerClass) {
      this.$router.push({
        name: "offer.class.enrollments",
        params: {
          offerClassId: parseInt(offerClass.id),
        },
      });
    },

    // Método para tratar o sucesso da duplicação de turma
    async handleDuplicateSuccess(data) {
      await this.getCourses();

      if (data.totalDuplicates) {
        this.showSuccessMessage(
          `Turma "${data.offerClassName}" duplicada com sucesso para ${data.totalDuplicates} curso(s).`
        );
      }
    },

    /**
     * Prepares the DuplicateOfferClassModal to be shown with the given offerClass.
     * @param {Object} offerClass - The offer class to be duplicated.
     */
    duplicateOfferClass(offerClass) {
      this.offerClassToDuplicate = offerClass;

      this.parentOfferCourse = this.offerCourses.find(
        (offerCourse) => (offerCourse.id = offerClass.offerCourseId)
      );

      this.showDuplicateOfferClassModal = true;
    },

    /**
     * Confirms and processes the deletion of a offer class.
     *
     */
    async deleteOfferClass() {
      if (!this.offerClassToDelete) return;

      this.loading = true;
      this.showDeleteOfferClassModal = false;

      try {
        const { id, name, offerCourseId } = this.offerClassToDelete;

        await deleteClass(id);

        const offerCourse = this.offerCourses.find(
          (c) => c.id === offerCourseId
        );

        if (offerCourse && Array.isArray(offerCourse.offerClasses)) {
          const classIndex = offerCourse.offerClasses.findIndex(
            (t) => t.id === id
          );

          if (classIndex !== -1) {
            offerCourse.offerClasses.splice(classIndex, 1);
            offerCourse.courseClassCount = offerCourse.offerClasses.length;
          }
        }

        this.showSuccessMessage(`Turma ${name} excluída com sucesso.`);

        this.offerClassToDelete = null;
      } catch (error) {
        console.log(error);
        this.showErrorMessage(error.message || "Erro ao excluir turma.");
      } finally {
        this.loading = false;
      }
    },

    requestToggleOfferCourseStatus(course) {
      if (!course.canActivate) {
        return;
      }
      this.selectedOfferCourse = course;
      this.showCourseStatusModal = true;
    },

    getStatusButtonTitle(item) {
      if (item.status) {
        return item.canActivate
          ? "Inativar"
          : "Não é possível inativar este curso";
      } else {
        return item.canActivate ? "Ativar" : "Não é possível ativar este curso";
      }
    },

    /**
     * Toggles the status of a course in the offer.
     *
     * @param {Object} selectedOfferCourse - The course to toggle the status of.
     */
    async toggleOfferCourseStatus() {
      if (this.selectedOfferCourse) {
        try {
          this.loading = true;
          const newStatus = this.selectedOfferCourse.status ? false : true;
          const courseNome = this.selectedOfferCourse.name;

          const courseId = this.selectedOfferCourse.id;

          await toggleCourseStatus(this.offerId, courseId, newStatus);

          const courseIndex = this.offerCourses.findIndex(
            (c) => c.id === this.selectedOfferCourse.id
          );
          if (courseIndex !== -1) {
            const course = this.offerCourses[courseIndex];
            course.status = newStatus;
            course.statusName = newStatus ? "Ativo" : "Inativo";
          }

          this.showCourseStatusModal = false;
          this.selectedOfferCourse = null;

          await this.getCourses();

          this.showSuccessMessage(
            newStatus
              ? `Curso "${courseNome}" ativado com sucesso.`
              : `Curso "${courseNome}" inativado com sucesso.`
          );
        } catch (error) {
          //
        } finally {
          this.loading = false;
        }
      }
    },

    /**
     * Initiates the process to delete a course from the offer.
     *
     * @param {Object} course - The course to be deleted.
     *
     */
    requestDeleteOfferCourse(course) {
      if (!course.canDelete) {
        return;
      }

      this.offerCourseToDelete = course;
      this.showDeleteOfferCourseModal = true;
    },

    /**
     * Confirms and processes the deletion of a course from the offer.
     */
    async deleteOfferCourse() {
      if (this.offerCourseToDelete) {
        try {
          this.loading = true;
          this.showDeleteOfferCourseModal = false;

          const courseNome = this.offerCourseToDelete.name;
          const courseId = this.offerCourseToDelete.id;

          await removeCourseFromOffer(this.offerId, courseId);

          this.offerCourses = this.offerCourses.filter(
            (c) => c.id !== this.offerCourseToDelete.id
          );

          this.offerCourseToDelete = null;

          await this.getCourses();

          const message = `Curso "${courseNome}" excluído com sucesso.`;
          this.showSuccessMessage(message);
        } catch (error) {
          this.showErrorMessage(error.message || "Erro ao remover curso.");
        } finally {
          this.loading = false;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.empty-state {
  padding: 2rem;
  text-align: center;

  .no-results {
    font-size: 0.875rem;
    color: #6c757d;
  }
}

.btn-delete i {
  color: var(--danger);
}

.btn-activate {
  color: #6c757d !important;

  &:hover {
    background-color: rgba(108, 117, 125, 0.1) !important;
  }

  i {
    opacity: 0.7;
  }
}

.btn-deactivate {
  color: #fff !important;
}
</style>

<style lang="scss">
table .thead-dark {
  tr {
    th {
      font-size: 14px !important;
      color: #fff !important;
      background-color: #212529 !important;

      &.enrolName,
      &.vacancies,
      &.totalEnrolled,
      &.startDate,
      &.endDate,
      &.status,
      &.actions {
        text-align: center !important;
      }
    }
  }
}

.tbody-light {
  tr {
    height: 50px;
    align-items: center;
    background-color: #26292c;

    &:hover {
      background-color: rgba(255, 255, 255, 0.075) !important;
    }

    td {
      flex: 1;
      padding: 0.75rem 0.5rem;
      font-size: 14px;
      text-align: center;

      &:first-child {
        padding-left: 20px;
      }

      &.enrolName,
      &.vacancies,
      &.totalEnrolled,
      &.startDate,
      &.endDate,
      &.status,
      &.actions {
        text-align: center !important;
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
      }
    }
  }
}
</style>
